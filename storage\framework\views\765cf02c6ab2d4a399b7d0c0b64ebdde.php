

<!-- filter offcanvas -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="filtersOffcanvas">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title"><?php echo e(labels('admin_labels.filters', 'Filters')); ?></h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>

    <div class="container-fluid table-filter-section mb-8">

        <div class="dateRangeFilter d-none mt-5">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.date_range', 'Date Range')); ?></label>
            <div id="datepicker">
                <i class='bx bxs-calendar'></i>&nbsp;
                <span></span> <i class="fa fa-caret-down"></i>
            </div>
        </div>
        <div class="advertismentStatusFilter mt-5 d-none">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.select_status', 'Select Status')); ?></label>
            <select id="advertisment_status_filter" name="advertisment_status" placeholder="Select Status" class="form-select">
                <option value="all"><?php echo e(__('admin_labels.all_status')); ?></option>
                <option value="approved"><?php echo e(__('admin_labels.approved')); ?></option>
                <option value="pending"><?php echo e(__('admin_labels.pending')); ?></option>
                <option value="rejected"><?php echo e(__('admin_labels.rejected')); ?></option>
            </select>
        </div>
        

        <div class="orderStatusFilter mt-5 d-none">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.select_status', 'Select Status')); ?></label>
            <select id="order_status" name="order_status" placeholder="Select Status" class="form-select">
                <option value="">All Orders</option>
                <option value="received">Received</option>
                <option value="processed">Processed</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
                <option value="returned">Returned</option>
            </select>
        </div>

        <div class="paymentMethodFilter mt-5 d-none">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.payment_method', 'Payment Method')); ?></label>
            <select id="payment_method" name="payment_method" placeholder="Select Payment Method"
                class="form-control">
                <option value="">All Payment Methods</option>
                <option value="COD">Cash On Delivery</option>
                <option value="Paypal">Paypal</option>
                <option value="RazorPay">RazorPay</option>
                <option value="Paystack">Paystack</option>
                <option value="Flutterwave">Flutterwave</option>`
                <option value="Paytm">Paytm</option>
                <option value="Stripe">Stripe</option>
                <option value="bank_transfer">Direct Bank Transfers</option>
            </select>
        </div>

        <div class="orderTypeFilter mt-5 d-none">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.order_type', 'Order Type')); ?></label>
            <select id="order_type" name="order_type" placeholder="Select Order Type" class="form-control">
                <option value="">All Orders</option>
                <option value="physical_order">Physical Orders</option>
                <option value="digital_order">Digital Orders</option>
            </select>
        </div>

        <div class="StatusFilter mt-5 d-none ">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.select_status', 'Select Status')); ?></label>
            <select class='form-select' name='status' id="statusFilter">
                <option value=''><?php echo e(__('admin_labels.select_status')); ?></option>
                <option value='1'><?php echo e(__('admin_labels.active')); ?></option>
                <option value='0'><?php echo e(__('admin_labels.deactive')); ?></option>
            </select>
        </div>

        <div class="paymentRequestStatusFilter mt-5 d-none ">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.payment_request_status', 'Payment Request Status')); ?></label>
            <select class='form-select' name='payment_request_status' id="payment_request_status_filter">
                <option value=''>Select Status</option>
                <option value='0'>Pending</option>
                <option value='1'>Approved</option>
                <option value='2'>Rejected</option>
            </select>
        </div>

        <div class="productStatusFilter mt-5 d-none ">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.product_status', 'Status')); ?></label>
            <select class='form-select' name='status' id="status_filter">
                <option value=''>Select Status</option>
                <option value='1'>Approved</option>
                <option value='2'>Not-Approved</option>
                <option value='0'>Deactivated</option>
            </select>
        </div>

        <div class="productTypeFilter mt-5 d-none ">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.product_type', 'Product Type')); ?></label>
            <select class='form-select' name='product_type' id="product_type_filter">
                <option value=''>Select Type</option>
                <option value='simple_product'>Simple Product</option>
                <option value='variable_product'>Variable Product</option>
                <option value='digital_product'>Digital Product</option>
            </select>
        </div>
        <div class="cashCollectionTypeFilter mt-5 d-none ">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.cash_collection_type', 'Cash Collection Type')); ?></label>
            <select id="cash_collection_status" name="cash_collection_status" placeholder="Select Status"
                class="form-control">
                <option value="">Select Status</option>
                <option value="delivery_boy_cash">Delivery Boy Cash Received</option>
                <option value="delivery_boy_cash_collection">Cash Collected by Admin</option>
            </select>
        </div>


        <div class="blogCategoryFilter mt-5 d-none">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.select_category', 'Select Category')); ?></label>
            <select name="category_id" class="form-select get_filter_blog_categories" id="blog_category_id"
                data-placeholder="Search Categories">

            </select>
        </div>
        
        <div class="advertismentPackagesFilter mt-5 d-none">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.select_delivery_boy', 'Select Delivery Boy')); ?></label>
            <select name="ads_package_id" class="form-select get_filter_package" id="ads_package_id"
                data-placeholder="Search Delivery Boy">

            </select>
        </div>
        <div class="deliveryBoyFilter mt-5 d-none">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.select_delivery_boy', 'Select Delivery Boy')); ?></label>
            <select name="delivery_boy" class="form-select get_filter_delivery_boy" id="delivery_boy"
                data-placeholder="Search Delivery Boy">

            </select>
        </div>

        <div class="brandFilter mt-5 d-none ">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.select_brand', 'Select Brand')); ?></label>
            <select class="form-select admin_brand_list" id="admin_brand_list" name="brand">
            </select>
        </div>

        <div class="categoryFilter mt-5 d-none ">
            <label for=""
                class="form-label body-default"><?php echo e(labels('admin_labels.select_category', 'Select Category')); ?></label>

            <div class="col-md-12 search_seller_category">

            </div>
        </div>

        <?php

            use Illuminate\Support\Facades\DB;

            $store_id = getStoreId();
            $sellers = DB::table('seller_store')
                ->leftJoin('seller_data', 'seller_data.id', '=', 'seller_store.seller_id')
                ->leftJoin('users', 'users.id', '=', 'seller_store.user_id')
                ->select('seller_store.*', 'seller_data.*', 'users.username')
                ->where('seller_store.store_id', $store_id)
                ->get();
            $settings = getSettings('system_settings', true, true);
            $settings = json_decode($settings, true);
            $app_name = isset($settings['app_name']) && !empty($settings['app_name']) ? $settings['app_name'] : 'Eshop Plus';
        ?>

        <div class="sellerFilter mt-5 d-none">
            <label for="" class="form-label body-default">Choose Seller</label>
            <select class='form-control' name='seller_id' id="filterSellerId">
                <option value="">Select Seller</option>
                <?php $__currentLoopData = $sellers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $seller): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($seller->id); ?>">
                        <?php echo e($seller->username); ?> - <?php echo e($seller->store_name); ?> (store)
                    </option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>

    </div>
    <div class="offcanvas-body" id="columnFilterOffcanvasBody">
        <h6>Table Data</h6>

    </div>
    <div class="d-flex justify-content-end mb-3 pe-3">
        <button type="reset"
            class="btn reset-btn reset_filter_button me-3"><?php echo e(labels('admin_labels.reset_filter', 'Reset Filter')); ?></button>
        <button type="button" class="btn form-btn"
            id="tableFilterBtn"><?php echo e(labels('admin_labels.apply_filter', 'Apply Filter')); ?></button>
    </div>
</div>




<footer class="footer mt-4 py-3 bg-body">
    <div class="px-4">
        <div class="col-12">
            <div class="text-center">
                <div class="row">
                    <div class="col-md-6">
                        <span class="copyright">
                            Copyright © 2025 <a href="<?php echo e(config('app.url')); ?>"><?php echo e($app_name); ?></a>
                            All
                            rights reserved.
                        </span>
                    </div>
                    <div class="col-md-6 text-end">
                        <span class="text-end text-muted">
                            <span class="badge bg-primary footer-version-badge d-inline">V.
                                <?php echo e(get_current_version()); ?></span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
<?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/components/admin/footer.blade.php ENDPATH**/ ?>