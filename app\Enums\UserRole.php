<?php

namespace App\Enums;

enum UserRole: string
{       
    case ADMIN = 'admin';
    case STORE= 'STORE';
    case STOREEMPLOYE = 'store_employee';
    case CUSTOMER = 'customer';
    case seller='seller';
    case super_admin = 'super_admin';

    public function label(): string
    {
        return match($this) {
            self::ADMIN => 'ادمن',
            self::STORE => 'متجر',
            self::STOREEMPLOYE => 'موظف',
            self::CUSTOMER => 'عميل',
            self::seller => 'متحر',
            self::super_admin => 'ادمن اساسي',


        };
    }

    public function color(): string
    {
        return match($this) {
            self::ADMIN => 'yellow',
            self::STORE => 'green',
            self::STOREEMPLOYE => 'red',
            self::CUSTOMER => 'blue',
            self::seller => 'orange',
            self::super_admin => 'purple'
        };
    }
}
