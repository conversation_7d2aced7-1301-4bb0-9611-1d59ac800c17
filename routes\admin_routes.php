<?php

use App\Http\Controllers\Admin\AdvertismentController;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redirect;

use App\Http\Controllers\Admin\AreaController;
use App\Http\Controllers\Admin\HomeController;
use App\Http\Controllers\Admin\UserController;

use App\Http\Controllers\Admin\OfferController;
use App\Http\Controllers\Admin\SellerController;
use App\Http\Controllers\Admin\SliderController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\CategoryController;

use App\Http\Controllers\Admin\NotificationController;
use App\Http\Controllers\Admin\UserPermissionController;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\FaqController;
use App\Http\Controllers\Admin\TicketController;
use App\Http\Controllers\Admin\FeaturedSectionsController;

use App\Http\Controllers\Admin\StoreController;
use App\Http\Controllers\Admin\CashCollectionController;
use App\Http\Controllers\Admin\CronJobController;
use App\Http\Controllers\Admin\FundTransferController;
use App\Http\Controllers\Admin\PremuimAdsController;
use App\Http\Controllers\Admin\SpotlightAdController;
use Illuminate\Support\Facades\Artisan;
use Kreait\Firebase\Contract\Messaging;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification as MessagingNotification;

Route::group(
    ['middleware' => ['auth', 'role:super_admin,admin,editor', 'CheckStoreNotEmpty']],
    function () {

        Route::get('admin/test', function () {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');
        });

        Route::get('admin/home', [HomeController::class, 'index'])->name('admin.home');
        Route::get('admin/cronjob/settleSellerCommission', [CronJobController::class, 'settleSellerCommission'])->middleware('permissions:edit seller');
        Route::get('admin/cronjob/settleCashbackDiscount', [CronJobController::class, 'settleCashbackDiscount']);
        Route::get('admin/test', function () {
            $messaging = app(Messaging::class);

            $message = CloudMessage::withTarget('topic', 'test')
                ->withNotification(MessagingNotification::create('Title', 'Message Body'));

            $messaging->send($message);
            // subscribeUserToTopic('ehkKjCZQQSywlCxubqF8lY:APA91bGvU9dCTJYdnSIfIMt-Rb2TceJOBMWV1rUGpeJDnaiiVK3HaRujExVxepXh6SWyXuuPR5iLlkpSHzWIQyY6qneIAFFCRv-QqxVGxisn4wADBw9hajE','test');
            // return redirect()->back()->with('message', 'Sitemap generated successfully!');
        });

        Route::get('admin/account/{user}', [UserController::class, 'edit']);

        Route::put('admin/users/update/{user}', [UserController::class, 'update']);

        Route::get('admin/user/search_user', [UserController::class, 'searchUser']);
        // Route::get('admin/user/search_user', [UserController::class, 'searchSeller']);
        Route::get('admin/user/search_seller', [UserController::class, 'searchSeller']);

        Route::prefix('admin')->group(function () {

            //categories

            Route::resource("categories", CategoryController::class)->except(['show'])
                ->missing(function (Request $request) {
                    return Redirect::route('categories.index');
                });
            Route::post("/categories", [CategoryController::class, 'store'])->name('categories.store')->middleware('permissions:create categories');

            Route::get('categories/list', [CategoryController::class, 'list'])->name('categories.list');

            Route::get('categories/category_slider', [CategoryController::class, 'category_slider'])->name('category_slider.index');

            Route::get('categories/categories_data', [CategoryController::class, 'category_data']);

            Route::get('categories/get_categories', [CategoryController::class, 'getCategories']);

            Route::get('categories/getCategories', [CategoryController::class, 'get_categories']);

            Route::get('categories/get_seller_categories', [CategoryController::class, 'getSellerCategories']);


            Route::get('categories/get_category_details', [CategoryController::class, 'getCategoryDetails']);


            Route::get('categories/destroy/{id}', [CategoryController::class, 'destroy'])->name('admin.categories.destroy')->middleware('permissions:delete categories');

            Route::get('admin/categories/update_status/{id}', [CategoryController::class, 'update_status'])->middleware('permissions:edit categories');

            Route::get('categories/edit/{id}', [CategoryController::class, 'edit'])->name('categories.update');

            Route::put('categories/update/{id}', [CategoryController::class, 'update'])->middleware('permissions:edit categories');

            Route::post("categories/category_sliders", [CategoryController::class, 'store_category_slider'])->name('category_sliders.store')->middleware('permissions:create category_sliders');

            Route::get('categories/category_sliders_list', [CategoryController::class, 'category_sliders_list'])->name('category_sliders.list');

            Route::get('category_sliders/destroy/{id}', [CategoryController::class, 'category_slider_destroy'])->name('admin.category_sliders.destroy')->middleware('permissions:delete category_sliders');

            Route::get('/category_sliders/update_status/{id}', [CategoryController::class, 'update_category_slider_status'])->middleware('permissions:edit category_sliders');

            Route::get('category_sliders/edit/{id}', [CategoryController::class, 'category_slider_edit'])->name('admin.category_sliders.update');

            Route::put('category_sliders/update/{id}', [CategoryController::class, 'category_slider_update'])->middleware('permissions:edit category_sliders');

            Route::get('categories/get_seller_categories_filter', [CategoryController::class, 'get_seller_categories_filter']);
            Route::get('ads/get_packages_filter', [AdvertismentController::class, 'get_packages_filter']);


            // blog categories

            Route::resource("blogs", BlogController::class)->names([
                'index' => 'admin.blogs.index',
            ])->except('show');


            Route::post("/blog_categories", [BlogController::class, 'storeCategory'])->name('blog_category.store')->middleware('permissions:create blog_categories');

            Route::get('blog_categories/list', [BlogController::class, 'categoryList'])->name('blog_categories.list');

            Route::get('blog_categories/destroy/{id}', [BlogController::class, 'destroyCategory'])->name('admin.blog_categories.destroy')->middleware('permissions:delete blog_categories');

            Route::get('admin/blog_categories/update_status/{id}', [BlogController::class, 'updateCategoryStatus'])->middleware('permissions:edit blog_categories');

            Route::get('/blog_category/edit/{id}', [BlogController::class, 'editCategory'])->name('blog_categories.edit');

            Route::put('/blog_category/update/{id}', [BlogController::class, 'updateCategory'])->name('blog_categories.update')->middleware('permissions:edit blog_categories');

            // blogs

            Route::get('/manage_blogs', [BlogController::class, 'createBlog'])->name('manage_blogs.index');

            Route::post("/admin/manage_blogs", [BlogController::class, 'storeBlog'])->name('blogs.store')->middleware('permissions:create blogs');

            Route::get('blogs/get_blog_categories', [BlogController::class, 'getBlogCategories']);

            Route::get('blogs/list', [BlogController::class, 'blogList'])->name('blogs.list');

            Route::get('blogs/destroy/{id}', [BlogController::class, 'destroyBlog'])->name('blogs.destroy')->middleware('permissions:delete blogs');

            Route::get('admin/blogs/update_status/{id}', [BlogController::class, 'updateBlogStatus'])->middleware('permissions:edit blog_categories');

            Route::get('/blogs/edit/{id}', [BlogController::class, 'editBlog'])->name('blogs.edit');

            Route::put('/blogs/update/{id}', [BlogController::class, 'updateBlog'])->name('blogs.update')->middleware('permissions:edit blog_categories');

            //setting

            Route::get('settings/', [SettingController::class, 'index'])->name('settings.index');

            Route::get("settings/system_settings", [SettingController::class, 'systemSettings'])->name('system_settings');


            Route::post("settings/system_settings", [SettingController::class, 'storeSystemSetting'])->name('system_settings.store')->middleware('permissions:edit system_setting');

            Route::post("settings/removeSettingMedia", [SettingController::class, 'removeSettingMedia']);

            Route::get("settings/email_settings", [SettingController::class, 'emailSettings'])->name('email_settings');

            Route::post("settings/email_settings", [SettingController::class, 'storeEmailSetting'])->name('email_settings.store')->middleware('permissions:edit smtp_setting');





            Route::get("settings/time_slot_settings", [SettingController::class, 'timeSlotSettings'])->name('time_slot_settings');

            Route::post("settings/store_time_slot", [SettingController::class, 'storeTimeSlot'])->name('time_slot.store');

            Route::post("settings/time_slot_settings", [SettingController::class, 'storeTimeSlotConfig'])->name('time_slot_config.store');

            Route::get('settings/time_slot/list', [SettingController::class, 'timeSlotList'])->name('time_slots.list');

            Route::get('settings/time_slot/destroy/{id}', [SettingController::class, 'timeSlotDestroy'])->name('time_slot.destroy');

            Route::get('settings/time_slot/update_status/{id}', [SettingController::class, 'updateTimeSlotStatus']);


            Route::post("settings/store_exchange_rate_aap_id", [SettingController::class, 'storeExchangeRateAapId'])->name('exchange_rate_aap_id.store')->middleware('permissions:edit currency_setting');

            Route::post("settings/set_default_currency", [SettingController::class, 'setDefaultCurrency'])->name('default_currency.set')->middleware('permissions:edit currency_setting');


            Route::get('settings/get_exchange_rates/{appId}', [SettingController::class, 'getExchangeRates']);

            Route::get('settings/update_exchange_rates/{app_id}', [SettingController::class, 'updateExchangeRates'])->middleware('permissions:edit currency_setting');

            Route::get("settings/contact_settings", [SettingController::class, 'contactSettings'])->name('notification_and_contact_settings');






            Route::post("settings/about_us", [SettingController::class, 'storeAboutUs'])->name('about_us.store')->middleware('permissions:edit contact_setting');

            Route::get(
                '/currency/list',
                [SettingController::class, 'currencyList']
            )->name('currency.list');

            Route::get('/currency/update_status/{id}', [SettingController::class, 'updateCurrencyStatus'])->middleware('permissions:edit currency_setting');

            // web settings

            Route::get('web_settings/', [SettingController::class, 'webSettings']);

            Route::post("settings/web_settings", [SettingController::class, 'storeWebSettings'])->name('web_settings.store')->middleware('permissions:edit web_general_setting');




            Route::post("settings/firebase_settings", [SettingController::class, 'storeFirebaseSettings'])->name('firebase_settings.store')->middleware('permissions:edit firebase_setting');


            // system policies

            Route::get("settings/system_policies", [SettingController::class, 'systemPolicies'])->name('system_policies');


            Route::post("settings/terms_and_conditions", [SettingController::class, 'storeTermsAndCondition'])->name('terms_and_conditions.store')->middleware('permissions:edit system_policies');



            // admin , seller & delivery boy policies


            Route::post("settings/admin_privacy_policy", [SettingController::class, 'storeAdminPrivacyPolicy'])->name('admin_privacy_policy.store')->middleware('permissions:edit admin_policies');

            Route::post("settings/admin_terms_and_conditions", [SettingController::class, 'storeAdminTermsAndConditions'])->name('admin_terms_and_conditions.store')->middleware('permissions:edit admin_policies');

            Route::post("settings/seller_privacy_policy", [SettingController::class, 'storeSellerPrivacyPolicy'])->name('seller_privacy_policy.store')->middleware('permissions:edit admin_policies');

            Route::get("settings/seller_privacy_policy", [SettingController::class, 'sellerPrivacyPolicy'])->name('seller_privacy_policy.view')->middleware('permissions:edit admin_policies');

            Route::post("settings/seller_terms_and_conditions", [SettingController::class, 'storeSellerTermsAndConditions'])->name('seller_terms_and_conditions.store')->middleware('permissions:edit admin_policies');

            Route::get("settings/seller_terms_and_conditions", [SettingController::class, 'sellerTermsAndCondition'])->name('seller_terms_and_conditions.view')->middleware('permissions:edit admin_policies');

            // delivery boy policies


            //promocode

            Route::resource("spotlight", SpotlightAdController::class)->names([
                'index' => 'spotlight.index',
            ])->except('show');
            Route::post('spotlight', [SpotlightAdController::class, 'store'])->name('spotlight.store')->middleware('permissions:create promo_code');

            Route::get(
                '/spotlight/list',
                [SpotlightAdController::class, 'list']
            )->name('spotlight.list');

            Route::get('spotlight/{id}', [SpotlightAdController::class, 'show'])->name('spotlight.show');


            Route::get('spotlight/destroy/{id}', [SpotlightAdController::class, 'destroy'])->name('spotlight.destroy')->middleware('permissions:delete promo_code');
        });



        //products











        Route::get("admin/seller/get_seller_deliverable_type", [SellerController::class, 'get_seller_deliverable_type'])->name('admin.sellers.get_seller_deliverable_type');

        // Feature Section

        Route::resource("admin/feature_section", FeaturedSectionsController::class)->names([
            'index' => 'feature_section.index',
            'edit' => 'feature_section.edit',

        ])->except('show');
        Route::get(
            'admin/feature_section/list',
            [FeaturedSectionsController::class, 'list']
        )->name('feature_section.list');

        Route::put('admin/feature_section/update/{id}', [FeaturedSectionsController::class, 'update'])->name('feature_section.update')->middleware('permissions:edit featured_section');

        Route::post("admin/feature_section", [FeaturedSectionsController::class, 'store'])->name('feature_section.store')->middleware('permissions:create featured_section');

        Route::get('admin/feature_section/destroy/{id}', [FeaturedSectionsController::class, 'destroy'])->name('feature_section.destroy')->middleware('permissions:delete featured_section');

        Route::get('admin/feature_section/section_order', [FeaturedSectionsController::class, 'sectionOrder'])->name('feature_section.section_order');

        Route::get('admin/feature_section/update_section_order', [FeaturedSectionsController::class, 'updateSectionOrder'])->name('feature_section.update_section_order');

        // Orders Section

        Route::resource("admin/offers", OfferController::class)->names([
            'index' => 'admin.offers.index',
        ])->except('show');

        Route::get('admin/offers/show/{id}', [OfferController::class, 'show'])->name('admin.offers.show');
        Route::get('admin/offers/offer_codes', [OfferController::class, 'offer_codes'])->name('admin.offers.offer_codes');
        Route::post('admin/offers/generate_label', [OfferController::class, 'generate_label'])->name('admin.orders.generate_label')->middleware('permissions:edit orders');
        Route::get('admin/offers/get_order_tracking', [OfferController::class, 'get_offer_codes'])->name('admin.orders.get_order_tracking');
        Route::get('admin/offers/list', [OfferController::class, 'list'])->name('admin.orders.list');

        Route::get('admin/offers/reported_offers', [OfferController::class, 'reported_offers'])->name('admin.reported_offers.index');
        Route::get('/admin/offers/reported_offers/{id}', [OfferController::class, 'show_reported_offers'])->name('admin.reported_offers.show');
        Route::post('/admin/offers/{id}/deactivate', [OfferController::class, 'deactivate'])->name('admin.offers.deactivate');

        Route::get('admin/offers/order_item_list', [OfferController::class, 'order_item_list'])->name('admin.orders.item_list');
        Route::get('/admin/offers/requested', [OfferController::class, 'requestedOffers'])->name('admin.requested_offers');
        Route::get('/admin/offers/requested/{id}', [OfferController::class, 'showRequestedOffer'])->name('admin.requested_offers.show');
        Route::post('/admin/offers/requested/{id}/approve', [OfferController::class, 'approve'])->name('admin.requested_offers.approve');
        Route::post('/admin/offers/requested/{id}/reject', [OfferController::class, 'reject'])->name('admin.requested_offers.reject');


        Route::resource("admin/ads", AdvertismentController::class)->names([
            'index' => 'ads.index',
            'edit' => 'ads.edit',
        ])->except('show');
        Route::put('admin/ads/update/{id}', [AdvertismentController::class, 'update'])->name('offers.update')->middleware('permissions:edit offer_images');
        Route::get(
            '/ads/list',
            [AdvertismentController::class, 'ads_list']
        )->name('ads.list');
        Route::post('admin/ads', [AdvertismentController::class, 'store'])->name('offers.store')->middleware('permissions:create offer_images');
        Route::get('ads/destroy/{id}', [AdvertismentController::class, 'destroy'])->name('offers.destroy')->middleware('permissions:delete offer_images');



        //city

        Route::get("admin/area/city", [AreaController::class, 'displayCity'])->name('admin.display_city');

        Route::post("admin/area/city", [AreaController::class, 'storeCity'])->name('admin.city.store')->middleware('permissions:create city')->middleware('permissions:create city');

        Route::get(
            'admin/city/list',
            [AreaController::class, 'cityList']
        )->name('admin.city.list');

        Route::get("admin/area/get_cities", [AreaController::class, 'getCities']);

        Route::get('admin/city/destroy/{id}', [AreaController::class, 'cityDestroy'])->name('admin.city.destroy')->middleware('permissions:delete zipcodes');

        Route::get('admin/city/edit/{id}', [AreaController::class, 'cityEdit'])->name('admin.city.edit');

        Route::put('admin/city/update/{id}', [AreaController::class, 'cityUpdate'])->name('admin.city.update')->middleware('permissions:edit zipcodes');

        //area

        Route::get("admin/area/", [AreaController::class, 'displayArea'])->name('admin.display_area');

        Route::post("admin/area/", [AreaController::class, 'storeArea'])->name('admin.area.store');

        Route::get(
            'admin/area/list',
            [AreaController::class, 'areaList']
        )->name('admin.area.list');

        Route::get('area/destroy/{id}', [AreaController::class, 'areaDestroy'])->name('admin.area.destroy');



        Route::get('admin/area/edit/{id}', [AreaController::class, 'areaEdit'])->name('admin.area.edit');

        Route::put('admin/area/update/{id}', [AreaController::class, 'areaUpdate'])->name('admin.area.update');

        // permissions and system users


        Route::get('admin/system_users', [UserPermissionController::class, 'index'])->name('admin.system_users.index');

        Route::get('admin/system_users/permissions/{id}', [UserPermissionController::class, 'index'])->name('system_users.permissions');

        Route::put('admin/system_users/permissions_update/{id}', [UserPermissionController::class, 'permissionsUpdate'])->name('system_users.permissions_update')->middleware('permissions:edit system_user');

        Route::post("admin/system_users/store", [UserPermissionController::class, 'store'])->name('system_users.store')->middleware('permissions:create system_user');

        Route::get(
            'admin/system_users/list',
            [UserPermissionController::class, 'systemUsersList']
        )->name('system_users.list');

        Route::get('admin/manage_system_users/', [UserPermissionController::class, 'manageSystemUsers'])->name('admin.manage_system_users');

        Route::get('admin/system_users/edit/{id}', [UserPermissionController::class, 'edit'])->name('system_user.edit');

        Route::get('system_users/destroy/{id}', [UserPermissionController::class, 'destroy'])->name('system_user.destroy')->middleware('permissions:delete system_user');

        // send notification

        Route::get('admin/send_notification/', [NotificationController::class, 'index'])->name('notifications.index');


        Route::post("admin/notification/store", [NotificationController::class, 'store'])->name('notifications.store')->middleware('permissions:create send_notification');

        Route::get('admin/notification/list', [NotificationController::class, 'list'])->name('admin.notifications.list');

        Route::get('admin/notification/seller_notification_list', [NotificationController::class, 'seller_notification_list'])->name('admin.seller_notifications.list');

        Route::get('admin/seller_email_notification/', [NotificationController::class, 'seller_email_notification_index'])->name('seller_email_notifications.index');

        Route::post("admin/email_notification/store", [NotificationController::class, 'store_email_notification'])->name('email_notifications.store')->middleware('permissions:create send_notification');

        Route::resource("admin/faq", FaqController::class)->names([
            'index' => 'faqs.index',
        ])->except('show');

        Route::get(
            '/faqs/list',
            [FaqController::class, 'list']
        )->name('faqs.list');

        Route::post('admin/faq', [FaqController::class, 'store'])->name('faqs.store')->middleware('permissions:create faq');

        Route::get('admin/faq/edit/{id}', [FaqController::class, 'edit'])->name('faqs.edit');

        Route::put('admin/faq/update/{id}', [FaqController::class, 'update'])->name('faqs.update')->middleware('permissions:edit faq');

        Route::get('faqs/destroy/{id}', [FaqController::class, 'destroy'])->name('faqs.destroy')->middleware('permissions:delete faq');

        // ticket system

        Route::resource("admin/tickets/ticket_types", TicketController::class)->names([
            'index' => 'ticket_types.index',
        ])->except('show');

        Route::post('admin/tickets/ticket_types', [TicketController::class, 'store'])->name('ticket_types.store')->middleware('permissions:create tickets');

        Route::get('/ticket_types/list', [TicketController::class, 'list'])->name('ticket_types.list');

        Route::get('admin/ticket_types/edit/{id}', [TicketController::class, 'edit'])->name('ticket_types.edit');

        Route::put('admin/ticket_types/update/{id}', [TicketController::class, 'update'])->name('ticket_types.update')->middleware('permissions:edit tickets');

        Route::get('tickets/ticket_types/{id}', [TicketController::class, 'destroy'])->name('ticket_types.destroy')->middleware('permissions:delete tickets');

        Route::get('tickets/tickets/{id}', [TicketController::class, 'tickets_destroy'])->name('tickets.destroy')->middleware('permissions:delete tickets');

        Route::get('admin/tickets/', [TicketController::class, 'viewTickets'])->name('admin.tickets.viewTickets');

        Route::get('admin/tickets/getTicketList', [TicketController::class, 'getTicketList'])->name('admin.tickets.getTicketList');

        Route::get('admin/tickets/get_ticket_messages', [TicketController::class, 'getMessages']);

        Route::post('admin/tickets/sendMessage', [TicketController::class, 'sendMessage'])->name('admin.tickets.sendMessage')->middleware('permissions:create tickets');

        Route::post('admin/tickets/editTicketStatus', [TicketController::class, 'editTicketStatus']);




        // customers

        Route::get('admin/customers/', [UserController::class, 'customers'])->name('admin.customers');

        Route::get('admin/customers/list', [UserController::class, 'getCustomersList'])->name('customers.list');
        Route::get('customers/{id}', [UserController::class, 'show'])->name('admin.customers.show');
        Route::post('admin/customers/{id}/toggle-ban', [UserController::class, 'toggleBan'])->name('admin.users.toggle-ban');




        Route::get('/customers/getCustomersAddressesList', [UserController::class, 'getCustomersAddressesList'])->name('admin.customers.getCustomersAddressesList');


        Route::resource("admin/stores", StoreController::class)->names([
            'index' => 'admin.stores.index',
        ])->except('show');

        Route::post('admin/store', [StoreController::class, 'store'])->middleware('permissions:create store')->name('admin.stores.store');

        Route::get('admin/stores/list', [StoreController::class, 'list'])->name('admin.stores.list');

        Route::get('admin/stores/manage_store', [StoreController::class, 'manage_store'])->name('admin.stores.manage_store');

        Route::get('admin/store/edit/{id}', [StoreController::class, 'edit'])->name('admin.store.update');

        Route::get('admin/store/update_status/{id}', [StoreController::class, 'update_status'])->middleware('permissions:edit store');


        Route::put('/admin/store/update/{id}', [StoreController::class, 'update'])->name('admin.stores.update');

        Route::get('admin/stores/requested-stores', [StoreController::class, 'requested_stores'])->name('admin.stores.manage_requested_stores');
        Route::get('admin/stores/requested-stores/list', [StoreController::class, 'requested_stores_list'])->name('admin.stores.requested_stores_list');
        Route::get('admin/stores/requested-stores/edit/{id}', [StoreController::class, 'requested_stores_edit'])->name('admin.stores.requested_stores_edit');

        Route::post('/store/{id}/approve', [StoreController::class, 'approve'])->name('admin.stores.approve');

        Route::post('/store/{id}/reject', [StoreController::class, 'reject'])->name('admin.stores.reject');

        Route::get("admin/stores/transaction", [StoreController::class, 'storeTransactions'])->name('admin.stores.storeTransactions');

        Route::get("admin/stores/transactions_list", [StoreController::class, 'transactionsList'])->name('admin.stores.transactions_list');
        Route::get('admin/stores/view_transactions/{id}', [StoreController::class, 'show'])->name('admin.store.transaction_show');


        Route::post('admin/set_store', function (Request $request) {

            session(['store_id' => $request->store_id]);
            session(['store_name' => $request->store_name]);
            session(['store_image' => $request->store_image]);
            return response()->json(['success' => true]);
        })->name('set_store');

        Route::get('admin/store/get_stores_list', [StoreController::class, 'get_stores_list']);

        //  sliders


        Route::get('admin/sliders', [SliderController::class, 'index'])->name('admin.sliders.index');

        Route::get('admin/ads/sliders', [SliderController::class, 'list'])->name('admin.sliders.list');
        Route::get('admin/ads/new_slider', [SliderController::class, 'create'])->name('admin.sliders.create');
        Route::post("admin/ads/store", [SliderController::class, 'store'])->name('admin.sliders.store');
        Route::get('admin/ads/sliders/{id}/edit', [SliderController::class, 'edit'])->name('admin.sliders.edit');
        Route::put('admin/ads/sliders/{id}', [SliderController::class, 'update'])->name('admin.sliders.update');

        //premuim ads
        Route::get('admin/premuim_ads', [PremuimAdsController::class, 'index'])->name('admin.premuim.index');
        Route::get('admin/premuim_ads/list', [PremuimAdsController::class, 'list'])->name('admin.premuim.list');
        Route::get('admin/premuim_ads/new_premuim', [PremuimAdsController::class, 'create'])->name('admin.premuim.create');
        Route::post("admin/premuim_ads/store", [PremuimAdsController::class, 'store'])->name('admin.premuim.store');
        Route::get('admin/premuim_ads/{id}/edit', [PremuimAdsController::class, 'edit'])->name('admin.premuim.edit');
        Route::put('admin/premuim_ads/{id}', [PremuimAdsController::class, 'update'])->name('admin.premuim.update');


        Route::get('admin/ads/requested', [AdvertismentController::class, 'requestedAds'])->name('admin.ads.requested');
        Route::get('admin/ads/requested/{id}', [AdvertismentController::class, 'show'])->name('admin.requested.show');
        Route::post('/admin/ads/{id}/approve', [AdvertismentController::class, 'approve'])->name('admin.ads.approve');
        Route::post('/admin/ads/{id}/reject', [AdvertismentController::class, 'reject'])->name('admin.ads.reject');
        Route::get('/admin/dashboard/latest-reports', [HomeController::class, 'getLatestReports'])->name('admin.dashboard.latest-reports');





        Route::get('admin/ads/sliders/destroy/{id}', [SliderController::class, 'destroy'])->name('admin.slider.destroy')->middleware('permissions:delete offer_slider');
        // delete selected data routes

        Route::delete('/categories/delete', [CategoryController::class, 'delete_selected_data'])->name('categories.delete');

        Route::delete('/categories_sliders/delete', [CategoryController::class, 'delete_selected_slider_data'])->name('categories_sliders.delete');


        Route::delete('/sellers/delete', [SellerController::class, 'delete_selected_data'])->name('sellers.delete');


        Route::delete('/blog_categories/delete', [BlogController::class, 'delete_selected_data'])->name('blog_categories.delete');

        Route::delete('/blogs/delete', [BlogController::class, 'delete_selected_blog_data'])->name('blogs.delete');



        Route::delete('/ticket_type/delete', [TicketController::class, 'delete_selected_data'])->name('ticket_type.delete');

        Route::delete('/tickets/delete', [TicketController::class, 'delete_selected_ticket_data'])->name('tickets.delete');

        Route::delete('/featured_sections/delete', [FeaturedSectionsController::class, 'delete_selected_data'])->name('featured_sections.delete');

        Route::delete('/customers/delete', [UserController::class, 'delete_selected_data'])->name('customers.delete');


        Route::delete('/faqs/delete', [FaqController::class, 'delete_selected_data'])->name('faqs.delete');

        Route::delete('/notifications/delete', [NotificationController::class, 'delete_selected_data'])->name('notifications.delete');



        Route::delete('/cities/delete', [AreaController::class, 'delete_selected_city_data'])->name('cities.delete');

        Route::delete('/system_users/delete', [UserPermissionController::class, 'delete_selected_data'])->name('system_users.delete');
    }

);
