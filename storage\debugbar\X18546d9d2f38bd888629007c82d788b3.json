{"__meta": {"id": "X18546d9d2f38bd888629007c82d788b3", "datetime": "2025-06-28 18:15:17", "utime": **********.756438, "method": "GET", "uri": "/admin/settings/system_settings", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[18:15:17] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.696129, "xdebug_link": null, "collector": "log"}, {"message": "[18:15:17] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.732707, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.252359, "end": **********.756455, "duration": 1.5040960311889648, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": **********.252359, "relative_start": 0, "end": **********.753616, "relative_end": **********.753616, "duration": 0.5012571811676025, "duration_str": "501ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.753624, "relative_start": 0.5012650489807129, "end": **********.756457, "relative_end": 2.1457672119140625e-06, "duration": 1.0028331279754639, "duration_str": "1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 33256048, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "admin.pages.forms.system_settings", "param_count": null, "params": [], "start": **********.796812, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/admin/pages/forms/system_settings.blade.phpadmin.pages.forms.system_settings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fadmin%2Fpages%2Fforms%2Fsystem_settings.blade.php&line=1", "ajax": false, "filename": "system_settings.blade.php", "line": "?"}}, {"name": "admin.layout", "param_count": null, "params": [], "start": **********.805813, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/admin/layout.blade.phpadmin.layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fadmin%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}, {"name": "admin.include_css", "param_count": null, "params": [], "start": **********.695608, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/admin/include_css.blade.phpadmin.include_css", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fadmin%2Finclude_css.blade.php&line=1", "ajax": false, "filename": "include_css.blade.php", "line": "?"}}, {"name": "components.admin.side-bar", "param_count": null, "params": [], "start": **********.706898, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/side-bar.blade.phpcomponents.admin.side-bar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Fside-bar.blade.php&line=1", "ajax": false, "filename": "side-bar.blade.php", "line": "?"}}, {"name": "components.admin.header", "param_count": null, "params": [], "start": **********.723886, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/header.blade.phpcomponents.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "components.admin.footer", "param_count": null, "params": [], "start": **********.741107, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/footer.blade.phpcomponents.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "admin.include_script", "param_count": null, "params": [], "start": **********.753979, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/admin/include_script.blade.phpadmin.include_script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fadmin%2Finclude_script.blade.php&line=1", "ajax": false, "filename": "include_script.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/settings/system_settings", "middleware": "web, CheckInstallation, auth, role:super_admin,admin,editor, CheckStoreNotEmpty", "controller": "App\\Http\\Controllers\\Admin\\SettingController@systemSettings", "namespace": null, "prefix": "/admin", "where": [], "as": "system_settings", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FSettingController.php&line=28\" onclick=\"\">app/Http/Controllers/Admin/SettingController.php:28-43</a>"}, "queries": {"nb_statements": 27, "nb_visible_statements": 27, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.025529999999999997, "accumulated_duration_str": "25.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.769368, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 3.408}, {"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.772352, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 3.408, "width_percent": 3.682}, {"sql": "select * from `users` where `users`.`id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.7746558, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 7.09, "width_percent": 4.465}, {"sql": "select * from `roles` where `roles`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.7777991, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 11.555, "width_percent": 4.544}, {"sql": "select count(*) as aggregate from `stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "CheckStoreNotEmpty", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckStoreNotEmpty.php", "line": 19}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}], "start": **********.779931, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CheckStoreNotEmpty:19", "source": {"index": 19, "namespace": "middleware", "name": "CheckStoreNotEmpty", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckStoreNotEmpty.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FCheckStoreNotEmpty.php&line=19", "ajax": false, "filename": "CheckStoreNotEmpty.php", "line": "19"}, "connection": "eshop", "explain": null, "start_percent": 16.099, "width_percent": 2.429}, {"sql": "select * from `settings` where `variable` = 'system_settings' limit 1", "type": "query", "params": [], "bindings": ["system_settings"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7825952, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:609", "source": {"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=609", "ajax": false, "filename": "function_helper.php", "line": "609"}, "connection": "eshop", "explain": null, "start_percent": 18.527, "width_percent": 3.212}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 402 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [402, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.7885032, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:38", "source": {"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=38", "ajax": false, "filename": "AppServiceProvider.php", "line": "38"}, "connection": "eshop", "explain": null, "start_percent": 21.739, "width_percent": 4.544}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.790535, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:39", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=39", "ajax": false, "filename": "AppServiceProvider.php", "line": "39"}, "connection": "eshop", "explain": null, "start_percent": 26.283, "width_percent": 2.938}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.792094, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 29.221, "width_percent": 5.64}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.8014722, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 34.861, "width_percent": 3.917}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.688488, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 38.778, "width_percent": 5.327}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.698384, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 44.105, "width_percent": 7.716}, {"sql": "select * from `settings` where `variable` = 'system_settings' limit 1", "type": "query", "params": [], "bindings": ["system_settings"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.707606, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:609", "source": {"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=609", "ajax": false, "filename": "function_helper.php", "line": "609"}, "connection": "eshop", "explain": null, "start_percent": 51.821, "width_percent": 2.624}, {"sql": "select * from `settings` where `variable` = 'sms_gateway_settings' limit 1", "type": "query", "params": [], "bindings": ["sms_gateway_settings"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.709311, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:609", "source": {"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=609", "ajax": false, "filename": "function_helper.php", "line": "609"}, "connection": "eshop", "explain": null, "start_percent": 54.446, "width_percent": 3.408}, {"sql": "select count(*) as aggregate from `ch_messages` where `seen` = 0 and `from_id` != 402 and `to_id` = 402", "type": "query", "params": [], "bindings": [0, 402, 402], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/ChatifyMessenger.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\ChatifyMessenger.php", "line": 269}, {"index": 17, "namespace": "view", "name": "components.admin.side-bar", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/side-bar.blade.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.7162971, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ChatifyMessenger.php:269", "source": {"index": 16, "namespace": null, "name": "app/ChatifyMessenger.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\ChatifyMessenger.php", "line": 269}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FChatifyMessenger.php&line=269", "ajax": false, "filename": "ChatifyMessenger.php", "line": "269"}, "connection": "eshop", "explain": null, "start_percent": 57.854, "width_percent": 2.624}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.719321, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 60.478, "width_percent": 4.034}, {"sql": "select * from `stores` where (`is_active` = 1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.7245002, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:964", "source": {"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=964", "ajax": false, "filename": "function_helper.php", "line": "964"}, "connection": "eshop", "explain": null, "start_percent": 64.512, "width_percent": 2.546}, {"sql": "select * from `stores` where `is_active` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "components.admin.header", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/header.blade.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.726174, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "components.admin.header:25", "source": {"index": 15, "namespace": "view", "name": "components.admin.header", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/header.blade.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Fheader.blade.php&line=25", "ajax": false, "filename": "header.blade.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 67.058, "width_percent": 3.369}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "components.admin.header", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/header.blade.php", "line": 64}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.72827, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "components.admin.header:64", "source": {"index": 16, "namespace": "view", "name": "components.admin.header", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/header.blade.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Fheader.blade.php&line=64", "ajax": false, "filename": "header.blade.php", "line": "64"}, "connection": "eshop", "explain": null, "start_percent": 70.427, "width_percent": 2.781}, {"sql": "select `language` from `languages` where (`code` is null)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.729984, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:964", "source": {"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=964", "ajax": false, "filename": "function_helper.php", "line": "964"}, "connection": "eshop", "explain": null, "start_percent": 73.208, "width_percent": 3.408}, {"sql": "select `id` from `stores` where `user_id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.733017, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:9251", "source": {"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=9251", "ajax": false, "filename": "function_helper.php", "line": "9251"}, "connection": "eshop", "explain": null, "start_percent": 76.616, "width_percent": 2.703}, {"sql": "select `primary_color`, `secondary_color`, `hover_color`, `active_color` from `stores` where (`id` is null)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.734712, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:964", "source": {"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=964", "ajax": false, "filename": "function_helper.php", "line": "964"}, "connection": "eshop", "explain": null, "start_percent": 79.318, "width_percent": 2.859}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.736564, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 82.178, "width_percent": 4.818}, {"sql": "select `id` from `stores` where `user_id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.741709, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:9251", "source": {"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=9251", "ajax": false, "filename": "function_helper.php", "line": "9251"}, "connection": "eshop", "explain": null, "start_percent": 86.996, "width_percent": 3.643}, {"sql": "select `seller_store`.*, `seller_data`.*, `users`.`username` from `seller_store` left join `seller_data` on `seller_data`.`id` = `seller_store`.`seller_id` left join `users` on `users`.`id` = `seller_store`.`user_id` where `seller_store`.`store_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "view", "name": "components.admin.footer", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/footer.blade.php", "line": 179}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.743628, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "components.admin.footer:179", "source": {"index": 13, "namespace": "view", "name": "components.admin.footer", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/footer.blade.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Ffooter.blade.php&line=179", "ajax": false, "filename": "footer.blade.php", "line": "179"}, "connection": "eshop", "explain": null, "start_percent": 90.638, "width_percent": 2.899}, {"sql": "select * from `updates` order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 10573}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.745635, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:10573", "source": {"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 10573}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=10573", "ajax": false, "filename": "function_helper.php", "line": "10573"}, "connection": "eshop", "explain": null, "start_percent": 93.537, "width_percent": 2.115}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.7494779, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 95.652, "width_percent": 4.348}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 1939, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Store": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Setting": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\Updates": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUpdates.php&line=1", "ajax": false, "filename": "Updates.php", "line": "?"}}}, "count": 1958, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "ziSC8FvC1hn7nFe40PxPKu1wjIikkoVnUlR94dGl", "store_id": "20", "store_name": "null", "store_image": "null", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/settings/system_settings\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "402", "system_settings": "{\"app_name\":\"\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a\",\"support_number\":\"919876543210\",\"support_email\":\"<EMAIL>\",\"logo\":\"media/1751134451_logo.png\",\"favicon\":null,\"storage_type\":\"local\",\"current_version_of_android_app\":\"1.1.0\",\"current_version_of_ios_app\":\"1.0.0\",\"version_system_status\":0,\"expand_product_image\":0,\"customer_app_maintenance_status\":1,\"offer_request_status\":0,\"message_for_customer_app\":\"sdsa\",\"sidebar_color\":null,\"sidebar_type\":null,\"navbar_fixed\":0,\"theme_mode\":0}"}, "request": {"path_info": "/admin/settings/system_settings", "status_code": "<pre class=sf-dump id=sf-dump-686392936 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-686392936\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1528823130 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1528823130\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1959402273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1959402273\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-63272923 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/admin/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IndMM2JJN0gzUjlINVNPdWtHeWdTSXc9PSIsInZhbHVlIjoid1JGWXJMR25td2gxYkVML20vMVJwS2J4aEVxbDk1c2s0TzZvRW54NHJDMklZcWttSkEweDZVWmd0Mm5YUEN6cGRENThsNDNtclE1NHFaTVpyN0lUV21CUGpIRHE0ajlmL2hyejc3SllkeTkraDdBL2dMTXBtczJzVHVUT3JBTlYiLCJtYWMiOiI4Nzg1ZGVkYTI0NDJmZDhlNmU1NDY5YzQ3NGMzOTEyOTIxNjlmY2JlNTExNGQzNzEwM2IzZTk3MTI4ZDhmOTJmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkoyeGJpNnBGQ2hianQxcXRWdHFRcmc9PSIsInZhbHVlIjoiWEwxT0FuVHl3S0ZqOVVONmxvYkdpMHZBK2RycXBibmdYSXNXM1N2Y2NITkpodmZwZjR3OUVLOE5oWkEvVkdXQnZyNmNNbTF5QjdScExKLzVVdk9WUlZDSlBzRTZHNHNMZWV5cUt3T2VNdVlNMGw2R0taMUNHMFVZNnlUektVWDUiLCJtYWMiOiI3MDMwZDhjYmE4OTA1ODBiYTQ5YmIyNDAwNGM0Y2YyNmJiZGM0NTdlODI3ZTNhMTBmN2Q0MWM4ZWIyYTJkMTkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63272923\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-58782627 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ziSC8FvC1hn7nFe40PxPKu1wjIikkoVnUlR94dGl</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bUcSJzxBrcq71UHlT2HAo9naMPF0e95b7Q41AuRb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58782627\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-364095487 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 18:15:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlZlRk16dVJhZzhHN08wS2ZhV2NQOVE9PSIsInZhbHVlIjoidW5XQThqZjg1MjN6UzdpYkhnTjVtaEZieEtUVFdQV3poUVBWeWdZSUt3VEJpVEV4K1ZvcnczSmNCUjZHbHFreTRwQnEzc29kdWhJNml6NFYweGZDdEhORWNQaG9pZ0ZDVFE5R2EzeVY0OXFFRmRpQkxld3pwTkx5TjJPcCtGR3AiLCJtYWMiOiJlM2RhZWExMDUzOTIyZWQwZmNhNTY5ZGJhYzUyYmQ3NWM5YTVjNTM2OWMwYmNhZDA1ZGE2M2JmZGEzZjQ1YTdiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:15:17 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImxEWlJydFJFcFpHU29FdEIybXYyaWc9PSIsInZhbHVlIjoiV0dlczR5R0JNcUN4SDJRNmlUYXpKbFBxanBiVVpVZVRtSmZNdW9kK2haUWluZ1I1YXJSVHhMWTJXSWdFcDlOVnpQcGJGYmpnQ1lUbENuckN3ZE43c2h6azNOWTlIbVBNZG5sNlNBM01mUmIwdnFvb1h2Z2F4Z0FhemJyalFtR0siLCJtYWMiOiJlMDAxZjNlYTUwOTYzMGNlMDQwZjE5ZTdmYjk0NjdiM2U1NzRmNDgzNTFjNzhkMWU0YzNjM2QwYTY2NTIzZDRiIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:15:17 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlZlRk16dVJhZzhHN08wS2ZhV2NQOVE9PSIsInZhbHVlIjoidW5XQThqZjg1MjN6UzdpYkhnTjVtaEZieEtUVFdQV3poUVBWeWdZSUt3VEJpVEV4K1ZvcnczSmNCUjZHbHFreTRwQnEzc29kdWhJNml6NFYweGZDdEhORWNQaG9pZ0ZDVFE5R2EzeVY0OXFFRmRpQkxld3pwTkx5TjJPcCtGR3AiLCJtYWMiOiJlM2RhZWExMDUzOTIyZWQwZmNhNTY5ZGJhYzUyYmQ3NWM5YTVjNTM2OWMwYmNhZDA1ZGE2M2JmZGEzZjQ1YTdiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:15:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImxEWlJydFJFcFpHU29FdEIybXYyaWc9PSIsInZhbHVlIjoiV0dlczR5R0JNcUN4SDJRNmlUYXpKbFBxanBiVVpVZVRtSmZNdW9kK2haUWluZ1I1YXJSVHhMWTJXSWdFcDlOVnpQcGJGYmpnQ1lUbENuckN3ZE43c2h6azNOWTlIbVBNZG5sNlNBM01mUmIwdnFvb1h2Z2F4Z0FhemJyalFtR0siLCJtYWMiOiJlMDAxZjNlYTUwOTYzMGNlMDQwZjE5ZTdmYjk0NjdiM2U1NzRmNDgzNTFjNzhkMWU0YzNjM2QwYTY2NTIzZDRiIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:15:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364095487\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1705807147 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ziSC8FvC1hn7nFe40PxPKu1wjIikkoVnUlR94dGl</span>\"\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>20</span>\n  \"<span class=sf-dump-key>store_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/admin/settings/system_settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>402</span>\n  \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"492 characters\">{&quot;app_name&quot;:&quot;\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:&quot;media/1751134451_logo.png&quot;,&quot;favicon&quot;:null,&quot;storage_type&quot;:&quot;local&quot;,&quot;current_version_of_android_app&quot;:&quot;1.1.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;version_system_status&quot;:0,&quot;expand_product_image&quot;:0,&quot;customer_app_maintenance_status&quot;:1,&quot;offer_request_status&quot;:0,&quot;message_for_customer_app&quot;:&quot;sdsa&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1705807147\", {\"maxDepth\":0})</script>\n"}}