<?php

namespace App\Http\Controllers\admin;

use App\Enums\CodeStatus;
use App\Http\Controllers\Controller;
use App\Libraries\Shiprocket;
use App\Models\Offer;
use App\Models\OfferClaim;
use App\Models\OfferCode;
use App\Models\Rating;
use App\Models\ReportIssuse;
use App\Models\Store;
use App\Models\StoreBranch;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;



class OfferController extends Controller
{
    public function index()
    {
        return view('admin.pages.tables.manage_offers');
    }


    public function reported_offers(Request $request)
    {

        $reports = ReportIssuse::with('reporter', 'offer.store')
            ->orderBy('created_at', 'desc')
            ->paginate(10);
        return view('admin.pages.tables.manage_reported_offers', compact('reports'));
    }

    public function deactivate($id)
    {
        try {
            Offer::where('id', $id)->update(['is_active' => 0]);

            return back()->with('success', __('admin_labels.offer_deactivated_successfully'));
        } catch (\Exception $e) {
            Log::error('Deactivate Offer Error: ' . $e->getMessage());
            return back()->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }

    public function show_reported_offers($id)
    {
        $report = ReportIssuse::with('reporter', 'offer.store')
            ->orderBy('created_at', 'desc')
            ->find($id);
        return view('admin.pages.views.reported_offer', compact('report'));
    }



    public function offer_codes()
    {
        return view('admin.pages.tables.manage_offer_codes');
    }
    public function list()
    {

        try {
            $search = trim(request('search'));
            $sort = (request('sort')) ? request('sort') : "created_at";
            $order = (request('order')) ? request('order') : "DESC";
            $limit = request("limit")?request('limit'):10;
            $offset = $search || (request('pagination_offset')) ? (request('pagination_offset')) : 0;
            $settings = getSettings('system_settings', true);
            $settings = json_decode($settings, true);
            $locale = app()->getLocale();


            $multipleWhere = [];

            if (!empty($search)) {
                $multipleWhere = [
                    'offers.id' => $search,
                    'offers.title_ar' => $search,
                    'offers.title_en' => $search,
                    'offers.description_ar' => $search,
                    'offers.description_en' => $search,
                    'offers.short_description' => $search,
                ];
            }

            $query = Offer::query();
            $query->select('id', 'title_' . $locale . ' as name', 'is_active', 'created_at', 'expire_date', 'offer_views', 'store_id')
                ->with(['store', 'images' => function ($q) {
                    $q->where('is_main', 1)
                        ->select('offer_id', 'image_url as image');
                }])

            ;
            $query->where(function ($q) use ($multipleWhere) {
                foreach ($multipleWhere as $column => $value) {
                    $q->orWhere($column, 'like', '%' . $value . '%');
                }
            });
            $total =(clone $query)->count();

            $offers = $query
                ->orderBy($sort, $order)
                ->skip($offset)
                ->take($limit)
                ->get();

            $offers = $offers->map(function ($p) {
                $delete_url = route('admin.offers.destroy', $p->id);
                $show_url = route('admin.offers.show', $p->id);

                $action = '<div class="dropdown bootstrap-table-dropdown">
                    <a href="#" class="text-dark" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="bx bx-dots-horizontal-rounded"></i>
                        </a>
                        <div class="dropdown-menu table_dropdown product_action_dropdown" aria-labelledby="dropdownMenuButton">
                            <a class="dropdown-item delete-data" data-url="' . $delete_url . '"><i class="bx bx-trash"></i> ' . __("admin_labels.delete") . '</a>
                            <a class="dropdown-item" href="' . $show_url . '"><i class="bx bxs-show"></i> ' . __("admin_labels.view") . '</a>

                        </div>
                    </div>';

                $image = isset($p->images[0]->image) ?  getMediaImageUrl($p->images[0]->image) : null;

                return [
                    'id' => $p->id,
                    'name' => $p->name . '<br><small>' . ucwords(str_replace('_', ' ', $p->type)) . '</small><br><small> By </small><b>' . $p->store_name . '</b>',
                    'offer_views' => $p->offer_views,
                    'created_at' => $p->created_at->format('d-m-Y H:i:s'),
                    'expire_date' => $p->expire_date != null ? $p->expire_date->format('d-m-Y H:i:s') : null,
                    'store_name' => $p->store->{'name_' . app()->getLocale()},
                    // Dropdown to toggle active/inactive status
                    'status' => view('components.status-dropdown', [
                        'id'            => $p->id,
                        'url'           => "/admin/offers/update_status/{$p->id}",
                        'isActive'      => $p->is_active,
                        'activeClass'   => 'active_status',
                        'inactiveClass' => 'inactive_status'
                    ])->render(),
                    // Logo image preview with lightbox
                    'image' => view('components.image-lightbox', [
                        'id'        => "image-{$p->id}",
                        'url'       => $image,
                        'thumbnail' => $image,
                        'width'     => 90
                    ])->render(),
                    'image' => $image == null ? '-' : '<div><a href="' . getMediaImageUrl($p->images[0]->image) . '" data-lightbox="image-' . $p->pid . '"><img src="' . $image . '" alt="Avatar" class="rounded" width="60" height="90"/></a></div>',
                    'operate' => $action,

                ];
            });

            return response()->json([
                "rows" => $offers,
                "total" => $total,
            ]);
        } catch (\Exception $e) {
            Log::error('Error: ' . $e->getMessage());

            return back()
                ->withInput()
                ->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }

    

    public function show($id)
    {
        try {
            $locale = app()->getLocale();
            $data = Offer::with([
                'store:id,name_' . $locale . ' as store_name,logo as store_logo,description_' . $locale . ' as description',
                'store.branches',
                'storeBranches',
                'images',
            ])
                ->withCount([
                    // Count how many ratings are 1-star
                    'ratings as ratings_1_star' => function ($query) {
                        $query->where('rating', 1);
                    },
                    // Count how many ratings are 2-stars
                    'ratings as ratings_2_star' => function ($query) {
                        $query->where('rating', 2);
                    },
                    // Count how many ratings are 3-stars
                    'ratings as ratings_3_star' => function ($query) {
                        $query->where('rating', 3);
                    },
                    // Count how many ratings are 4-stars
                    'ratings as ratings_4_star' => function ($query) {
                        $query->where('rating', 4);
                    },
                    // Count how many ratings are 5-stars
                    'ratings as ratings_5_star' => function ($query) {
                        $query->where('rating', 5);
                    },
                    'ratings as ratings_count' => function ($query) {
                        $query->whereNotNull('rating');
                    },
                ])
                ->withAvg('ratings', 'rating')
                ->find($id);
            if ($data === null || empty($data)) {
                return view('admin.pages.views.no_data_found');
            } else {

                $store = Store::where('id', $data->store_id)->first();

                $branches = $data->is_for_all_branches
                    ? $data->store->branches
                    : StoreBranch::whereIn('id', $data->storeBranches->pluck('id'))
                    ->with('city')
                    ->get();

                $categories = $data->categories()->get();
                $category_names = [];
                if (!empty($categories)) {
                    foreach ($categories as $category) {
                        $category_names[] = $category->{'name_' . app()->getLocale()};
                    }
                }
                $has_code = OfferCode::where('offer_id', $id)->exists();

                // Get reviews for the offer with pagination
                $reviews = Rating::where('offer_id', $id)
                    ->with(['user:id,username'])
                    ->select(['user_id', 'rating', 'review', 'created_at'])
                    ->whereNotNull('review')
                    ->orderByDesc('created_at')
                    ->get();
                $main_image = $data->images()->where('is_main', 1)->first();
                $other_images = $data->images()->where('is_main', 0)->get();
                $claim_count = OfferClaim::where('offer_id', $id)
                    ->count();
                $code_counts = OfferCode::where('offer_id', $id)
                    ->where('status', CodeStatus::AVAILABLE)
                    ->select('store_branch_id', DB::raw('count(*) as count'))
                    ->groupBy('store_branch_id')
                    ->pluck('count', 'store_branch_id')
                    ->toArray();

                $status = $data->expire_date < Carbon::now() ? __('admin_labels.expired') : __('admin_labels.active');
                Log::info('code_counts', [$code_counts]);
                return view('admin.pages.views.offer', compact('data', 'store', 'branches', 'reviews', 'claim_count', 'category_names', 'main_image', 'other_images', 'has_code', 'code_counts', 'status'));
            }
        } catch (Exception $e) {
            Log::error('Error: ' . $e->getMessage());

            return back()
                ->withInput()
                ->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }


    /**
     * Deletes a specific offer by its ID.
     *
     * @param int $id The ID of the offer to be deleted.
     * @return JsonResponse Returns a JSON response indicating success or failure.
     */
    public function destroy($id): JsonResponse
    {
        // Attempt to find the offer by its ID.
        $offer = Offer::find($id);

        // Check if the offer exists.
        if ($offer) {
            // If the offer is found, attempt to delete it.
            $offer->delete();

            // Return a success JSON response.
            return response()->json([
                'error' => false,
                'message' => __('admin_labels.offer_deleted_successfully')
            ]);
        } else {
            // If the offer is not found, return an error JSON response.
            return response()->json([
                'error' => __('admin_labels.offer_deleted_failed')
            ]);
        }
    }
    public function get_offer_codes(Request $request)
    {
        try {
            $locale=app()->getLocale();
            $search = trim($request->input('search'));
            $offset = $search || (request('pagination_offset')) ? (request('pagination_offset')) : 0;
            $limit = $request->input('limit', 10);
            $order = $request->input('order', 'DESC');
            $order_id = $request->input('order_id');

            // Base query: OfferCodes belonging to offers of this store
            $codesQuery = OfferCode::whereHas('offer')
                ->with(['offer', 'storeBranch'])
                ->when($search, function ($query) use ($search) {
                    $query->where(function ($q) use ($search) {
                        $q->where('id', 'like', "%$search%")
                            ->orWhereHas('storeBranch', function ($sub) use ($search) {
                                $sub->where('label', 'like', "%$search%");
                            })
                            ->orWhereHas('offer', function ($sub) use ($search) {
                                $sub->where('title_ar', 'like', "%$search%")
                                    ->orWhere('title_en', 'like', "%$search%")
                                    ->orWhere('id', 'like', "%$search%");
                            });
                    });
                })
                ->orderBy('offer_codes.created_at', 'DESC');

            // Clone before pagination for accurate total count
            $total = (clone $codesQuery)->count();

            // Apply pagination
            $codes = $codesQuery->skip($offset)->take($limit)->get();

            // Format result data
            $formatted = $codes->map(function ($code) use ($locale) {
                return [
                    'id' => $code->id,
                    'code' => $code->code,
                    'offer_id' => $code->offer_id,
                    'offer_name' => $code->offer?->{'title_' . $locale} ?? '-',
                    'status' => '<span class="badge bg-warning">' . $code->status->label() . '</span>',
                    'branch_name' => $code->storeBranch->label ?? '-',
                ];
            });

            return response()->json([
                'rows' => $formatted,
                'total' => $total,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching code list: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
            return response()->json([
                'rows' => [],
                'total' => 0,
            ]);
        }
    }

    public function generate_label(Request $request)
    {
        $res = generate_label($request['shipment_id']);
        if (!empty($res)) {
            $response['error'] = false;
            $response['message'] =
                labels('admin_labels.label_generated_successfully', 'Label generated successfully');
            $response['data'] = $res;
        } else {
            $response['error'] = true;
            $response['message'] =
                labels('admin_labels.label_not_generated', 'Label not generated');
            $response['data'] = array();
        }
        return response()->json($response);
    }
    public function requestedOffers()
    {
        $locale = app()->getLocale();
        $offers = Offer::select('id', 'store_id', 'title_ar', 'title_en', 'description_'.$locale.' as description', 'price', 'discounted_price', 'created_at')
        ->with([
            'store' => function ($query) use ($locale) {
                $query->select('id', 'name_'.$locale.' as name'); // You must also select the ID here
            },
            'categories' => function ($query) use ($locale) {
                $query->select('categories.id', 'name_'.$locale.' as name');
            },
            'images'
        ])
        ->where('is_approved', 0)
        ->latest()
        ->paginate(15);
    
        return view('admin.pages.tables.requested_offers', compact('offers'));
    }
    public function showRequestedOffer($id)
{
    $locale = app()->getLocale();

    $offer = Offer::with(['store' => function ($query) use ($locale) {
        $query->select('id', 'name_'.$locale.' as name', 'logo');
    }, 'categories', 'images','store.user'])
        ->select('id', 'store_id', 'title_ar', 'title_en', 'description_ar', 'description_en', 'price', 'discounted_price', 'created_at','expire_date','is_approved')
        ->findOrFail($id);
        $hasCode = OfferCode::where('offer_id', $id)->exists();
    $offerType=$hasCode?__('admin_labels.offers_with_code'):__('admin_labels.offer_in_store');
Log::info("o",[ $offer->is_approved]);
    return view('admin.pages.views.requested_offers', compact('offer','offerType'));
}

public function approve($id)
{
    $offer = Offer::findOrFail($id);
    $offer->is_approved = 1;
    $offer->is_active=1;
    $offer->save();

    return redirect()->back()->with('success', __('admin_labels.offer_approved_successfully'));
}

public function reject($id)
{
    $offer = Offer::findOrFail($id);
    $offer->is_approved = 2; // You can use `2` or a status like `rejected`
    $offer->save();

    return redirect()->back()->with('success', __('admin_labels.offer_rejected_successfully'));
}
    /**
     * Toggle the active status of a specific offer.
     *
     * This method switches the `is_active` flag:
     * - If currently active (1), it becomes inactive (0)
     * - If currently inactive (0), it becomes active (1)
     *
     * Returns a JSON response indicating success or failure.
     *
     * @param int $id Offer ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function update_status($id)
    {
        // Attempt to find the offer or fail with 404
        $offer = Offer::findOrFail($id);

        try {
            // Toggle the is_active status
            $offer->is_active = $offer->is_active == 1 ? 0 : 1;

            // Save the updated status
            $offer->save();

            return response()->json([
                'success' => labels('admin_labels.status_updated_successfully', 'Status updated successfully.')
            ]);
        } catch (\Exception $e) {
            // Handle and return any exception that occurs
            return response()->json([
                'error' => labels('admin_labels.something_went_wrong', 'Something went wrong')
            ]);
        }
    }

}
