<?php

namespace App\Http\Controllers\admin;

use App\Enums\CodeStatus;
use App\Http\Controllers\Controller;
use App\Libraries\Shiprocket;
use App\Models\Offer;
use App\Models\OfferClaim;
use App\Models\OfferCode;
use App\Models\Rating;
use App\Models\ReportIssuse;
use App\Models\Store;
use App\Models\StoreBranch;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * OfferController handles all admin operations related to offers management
 *
 * This controller manages:
 * - Offer listing and display
 * - Offer status management (activate/deactivate)
 * - Reported offers handling
 * - Offer codes management
 * - Offer approval/rejection workflow
 * - Label generation for shipments
 */
class OfferController extends Controller
{
    /**
     * Display the main offers management page
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('admin.pages.tables.manage_offers');
    }

    /**
     * Display reported offers with pagination
     *
     * Retrieves all reported offers with their associated reporter and store information,
     * ordered by creation date in descending order.
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function reported_offers(Request $request)
    {
        // Get reported offers with related data and paginate results
        $reports = ReportIssuse::with('reporter', 'offer.store')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.pages.tables.manage_reported_offers', compact('reports'));
    }

    /**
     * Deactivate a specific offer
     *
     * Sets the is_active flag to 0 for the specified offer ID.
     *
     * @param int $id The offer ID to deactivate
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deactivate($id)
    {
        try {
            // Update the offer status to inactive
            Offer::where('id', $id)->update(['is_active' => 0]);

            return back()->with('success', __('admin_labels.offer_deactivated_successfully'));
        } catch (\Exception $e) {
            // Log the error and return with error message
            Log::error('Deactivate Offer Error: ' . $e->getMessage());
            return back()->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }

    /**
     * Display details of a specific reported offer
     *
     * @param int $id The report ID to display
     * @return \Illuminate\View\View
     */
    public function show_reported_offers($id)
    {
        // Get the specific report with related data
        $report = ReportIssuse::with('reporter', 'offer.store')
            ->orderBy('created_at', 'desc')
            ->find($id);

        return view('admin.pages.views.reported_offer', compact('report'));
    }

    /**
     * Display the offer codes management page
     *
     * @return \Illuminate\View\View
     */
    public function offer_codes()
    {
        return view('admin.pages.tables.manage_offer_codes');
    }

    /**
     * Get paginated list of offers with search and sorting functionality
     *
     * This method handles:
     * - Search across multiple offer fields (ID, titles, descriptions)
     * - Sorting by various columns
     * - Pagination with configurable limits
     * - Localized content based on current locale
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function list()
    {
        try {
            // Extract request parameters with defaults
            $search = trim(request('search'));
            $sort = (request('sort')) ? request('sort') : "created_at";
            $order = (request('order')) ? request('order') : "DESC";
            $limit = request("limit") ? request('limit') : 10;
            $offset = $search || (request('pagination_offset')) ? (request('pagination_offset')) : 0;

            // Get system settings and current locale
            $settings = getSettings('system_settings', true);
            $settings = json_decode($settings, true);
            $locale = app()->getLocale();

            // Initialize search conditions array
            $multipleWhere = [];

            // Build search conditions if search term is provided
            if (!empty($search)) {
                $multipleWhere = [
                    'offers.id' => $search,
                    'offers.title_ar' => $search,
                    'offers.title_en' => $search,
                    'offers.description_ar' => $search,
                    'offers.description_en' => $search,
                    'offers.short_description' => $search,
                ];
            }

            // Build the main query with selected fields and relationships
            $query = Offer::query();
            $query->select('id', 'title_' . $locale . ' as name', 'is_active', 'created_at', 'expire_date', 'offer_views', 'store_id')
                ->with(['store', 'images' => function ($q) {
                    // Only get main images for the offer
                    $q->where('is_main', 1)
                        ->select('offer_id', 'image_url as image');
                }]);

            // Apply search conditions using OR logic
            $query->where(function ($q) use ($multipleWhere) {
                foreach ($multipleWhere as $column => $value) {
                    $q->orWhere($column, 'like', '%' . $value . '%');
                }
            });

            // Get total count before applying pagination
            $total = (clone $query)->count();

            // Apply sorting and pagination
            $offers = $query
                ->orderBy($sort, $order)
                ->skip($offset)
                ->take($limit)
                ->get();

            // Transform the offers data for frontend display
            $offers = $offers->map(function ($p) {
                // Generate action URLs for each offer
                $delete_url = route('admin.offers.destroy', $p->id);
                $show_url = route('admin.offers.show', $p->id);

                // Build dropdown action menu HTML
                $action = '<div class="dropdown bootstrap-table-dropdown">
                    <a href="#" class="text-dark" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="bx bx-dots-horizontal-rounded"></i>
                        </a>
                        <div class="dropdown-menu table_dropdown product_action_dropdown" aria-labelledby="dropdownMenuButton">
                            <a class="dropdown-item delete-data" data-url="' . $delete_url . '"><i class="bx bx-trash"></i> ' . __("admin_labels.delete") . '</a>
                            <a class="dropdown-item" href="' . $show_url . '"><i class="bx bxs-show"></i> ' . __("admin_labels.view") . '</a>
                        </div>
                    </div>';

                // Get the main image URL or set to null if not available
                $image = isset($p->images[0]->image) ? getMediaImageUrl($p->images[0]->image) : null;

                return [
                    'id' => $p->id,
                    // Display offer name with type and store information
                    'name' => $p->name . '<br><small>' . ucwords(str_replace('_', ' ', $p->type)) . '</small><br><small> By </small><b>' . $p->store_name . '</b>',
                    'offer_views' => $p->offer_views,
                    'created_at' => $p->created_at->format('d-m-Y H:i:s'),
                    'expire_date' => $p->expire_date != null ? $p->expire_date->format('d-m-Y H:i:s') : null,
                    'store_name' => $p->store->{'name_' . app()->getLocale()},
                    // Render status dropdown component for toggling active/inactive status
                    'status' => view('components.status-dropdown', [
                        'id'            => $p->id,
                        'url'           => "/admin/offers/update_status/{$p->id}",
                        'isActive'      => $p->is_active,
                        'activeClass'   => 'active_status',
                        'inactiveClass' => 'inactive_status'
                    ])->render(),
                    // Render image with lightbox functionality or show placeholder
                    'image' => $image == null ? '-' : '<div><a href="' . getMediaImageUrl($p->images[0]->image) . '" data-lightbox="image-' . $p->id . '"><img src="' . $image . '" alt="Avatar" class="rounded" width="60" height="90"/></a></div>',
                    'operate' => $action,
                ];
            });

            // Return JSON response with formatted data
            return response()->json([
                "rows" => $offers,
                "total" => $total,
            ]);
        } catch (\Exception $e) {
            // Log error and return error response
            Log::error('Error: ' . $e->getMessage());

            return back()
                ->withInput()
                ->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }

    /**
     * Display detailed view of a specific offer
     *
     * This method retrieves comprehensive offer information including:
     * - Store details and branches
     * - Rating statistics (1-5 stars breakdown)
     * - Reviews and user feedback
     * - Images (main and additional)
     * - Categories and claim statistics
     * - Available offer codes count by branch
     *
     * @param int $id The offer ID to display
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function show($id)
    {
        try {
            $locale = app()->getLocale();

            // Retrieve offer with all related data and rating statistics
            $data = Offer::with([
                'store:id,name_' . $locale . ' as store_name,logo as store_logo,description_' . $locale . ' as description',
                'store.branches',
                'storeBranches',
                'images',
            ])
                ->withCount([
                    // Count ratings by star level (1-5 stars)
                    'ratings as ratings_1_star' => function ($query) {
                        $query->where('rating', 1);
                    },
                    'ratings as ratings_2_star' => function ($query) {
                        $query->where('rating', 2);
                    },
                    'ratings as ratings_3_star' => function ($query) {
                        $query->where('rating', 3);
                    },
                    'ratings as ratings_4_star' => function ($query) {
                        $query->where('rating', 4);
                    },
                    'ratings as ratings_5_star' => function ($query) {
                        $query->where('rating', 5);
                    },
                    // Total ratings count
                    'ratings as ratings_count' => function ($query) {
                        $query->whereNotNull('rating');
                    },
                ])
                ->withAvg('ratings', 'rating') // Calculate average rating
                ->find($id);

            // Check if offer exists
            if ($data === null || empty($data)) {
                return view('admin.pages.views.no_data_found');
            }

            // Get store information
            $store = Store::where('id', $data->store_id)->first();

            // Determine which branches this offer applies to
            $branches = $data->is_for_all_branches
                ? $data->store->branches // All store branches
                : StoreBranch::whereIn('id', $data->storeBranches->pluck('id'))
                    ->with('city')
                    ->get(); // Specific branches only

            // Get offer categories and format names for current locale
            $categories = $data->categories()->get();
            $category_names = [];
            if (!empty($categories)) {
                foreach ($categories as $category) {
                    $category_names[] = $category->{'name_' . app()->getLocale()};
                }
            }

            // Check if this offer has associated codes
            $has_code = OfferCode::where('offer_id', $id)->exists();

            // Get all reviews for this offer
            $reviews = Rating::where('offer_id', $id)
                ->with(['user:id,username'])
                ->select(['user_id', 'rating', 'review', 'created_at'])
                ->whereNotNull('review')
                ->orderByDesc('created_at')
                ->get();

            // Separate main image from other images
            $main_image = $data->images()->where('is_main', 1)->first();
            $other_images = $data->images()->where('is_main', 0)->get();

            // Count total claims for this offer
            $claim_count = OfferClaim::where('offer_id', $id)->count();

            // Count available codes per branch
            $code_counts = OfferCode::where('offer_id', $id)
                ->where('status', CodeStatus::AVAILABLE)
                ->select('store_branch_id', DB::raw('count(*) as count'))
                ->groupBy('store_branch_id')
                ->pluck('count', 'store_branch_id')
                ->toArray();

            // Determine offer status based on expiration date
            $status = $data->expire_date < Carbon::now() ? __('admin_labels.expired') : __('admin_labels.active');

            Log::info('code_counts', [$code_counts]);

            return view('admin.pages.views.offer', compact(
                'data', 'store', 'branches', 'reviews', 'claim_count',
                'category_names', 'main_image', 'other_images', 'has_code',
                'code_counts', 'status'
            ));

        } catch (Exception $e) {
            Log::error('Error: ' . $e->getMessage());

            return back()
                ->withInput()
                ->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }


    /**
     * Deletes a specific offer by its ID.
     *
     * @param int $id The ID of the offer to be deleted.
     * @return JsonResponse Returns a JSON response indicating success or failure.
     */
    public function destroy($id): JsonResponse
    {
        // Attempt to find the offer by its ID.
        $offer = Offer::find($id);

        // Check if the offer exists.
        if ($offer) {
            // If the offer is found, attempt to delete it.
            $offer->delete();

            // Return a success JSON response.
            return response()->json([
                'error' => false,
                'message' => __('admin_labels.offer_deleted_successfully')
            ]);
        } else {
            // If the offer is not found, return an error JSON response.
            return response()->json([
                'error' => __('admin_labels.offer_deleted_failed')
            ]);
        }
    }
    /**
     * Get paginated list of offer codes with search functionality
     *
     * This method retrieves offer codes with their associated offers and store branches,
     * supporting search across multiple fields and pagination.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function get_offer_codes(Request $request)
    {
        try {
            $locale = app()->getLocale();

            // Extract request parameters
            $search = trim($request->input('search'));
            $offset = $search || (request('pagination_offset')) ? (request('pagination_offset')) : 0;
            $limit = $request->input('limit', 10);
            // Note: $order and $order_id are extracted but not currently used in the query
            $order = $request->input('order', 'DESC');
            $order_id = $request->input('order_id');

            // Build base query for offer codes with relationships
            $codesQuery = OfferCode::whereHas('offer')
                ->with(['offer', 'storeBranch'])
                ->when($search, function ($query) use ($search) {
                    // Apply search conditions across multiple related fields
                    $query->where(function ($q) use ($search) {
                        $q->where('id', 'like', "%$search%")
                            ->orWhereHas('storeBranch', function ($sub) use ($search) {
                                $sub->where('label', 'like', "%$search%");
                            })
                            ->orWhereHas('offer', function ($sub) use ($search) {
                                $sub->where('title_ar', 'like', "%$search%")
                                    ->orWhere('title_en', 'like', "%$search%")
                                    ->orWhere('id', 'like', "%$search%");
                            });
                    });
                })
                ->orderBy('offer_codes.created_at', 'DESC');

            // Get total count before applying pagination
            $total = (clone $codesQuery)->count();

            // Apply pagination to the query
            $codes = $codesQuery->skip($offset)->take($limit)->get();

            // Format the data for frontend display
            $formatted = $codes->map(function ($code) use ($locale) {
                return [
                    'id' => $code->id,
                    'code' => $code->code,
                    'offer_id' => $code->offer_id,
                    'offer_name' => $code->offer?->{'title_' . $locale} ?? '-',
                    'status' => '<span class="badge bg-warning">' . $code->status->label() . '</span>',
                    'branch_name' => $code->storeBranch->label ?? '-',
                ];
            });

            return response()->json([
                'rows' => $formatted,
                'total' => $total,
            ]);
        } catch (\Exception $e) {
            // Log error and return empty response
            Log::error('Error fetching code list: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
            return response()->json([
                'rows' => [],
                'total' => 0,
            ]);
        }
    }

    /**
     * Generate shipping label for a specific shipment
     *
     * This method calls the generate_label helper function to create
     * a shipping label for the provided shipment ID.
     *
     * @param Request $request Contains shipment_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function generate_label(Request $request)
    {
        // Call the global generate_label function
        $res = generate_label($request['shipment_id']);

        if (!empty($res)) {
            // Label generation successful
            $response['error'] = false;
            $response['message'] = labels('admin_labels.label_generated_successfully', 'Label generated successfully');
            $response['data'] = $res;
        } else {
            // Label generation failed
            $response['error'] = true;
            $response['message'] = labels('admin_labels.label_not_generated', 'Label not generated');
            $response['data'] = array();
        }

        return response()->json($response);
    }
    /**
     * Display paginated list of offers pending approval
     *
     * Retrieves offers that have not been approved yet (is_approved = 0)
     * with their associated store, categories, and images.
     *
     * @return \Illuminate\View\View
     */
    public function requestedOffers()
    {
        $locale = app()->getLocale();

        // Get unapproved offers with related data
        $offers = Offer::select('id', 'store_id', 'title_ar', 'title_en', 'description_'.$locale.' as description', 'price', 'discounted_price', 'created_at')
            ->with([
                'store' => function ($query) use ($locale) {
                    $query->select('id', 'name_'.$locale.' as name');
                },
                'categories' => function ($query) use ($locale) {
                    $query->select('categories.id', 'name_'.$locale.' as name');
                },
                'images'
            ])
            ->where('is_approved', 0) // Only pending approval offers
            ->latest()
            ->paginate(15);

        return view('admin.pages.tables.requested_offers', compact('offers'));
    }

    /**
     * Display detailed view of a specific requested offer for approval
     *
     * Shows comprehensive information about an offer pending approval,
     * including store details, categories, images, and offer type.
     *
     * @param int $id The offer ID to display
     * @return \Illuminate\View\View
     */
    public function showRequestedOffer($id)
    {
        $locale = app()->getLocale();

        // Get the requested offer with all related data
        $offer = Offer::with([
                'store' => function ($query) use ($locale) {
                    $query->select('id', 'name_'.$locale.' as name', 'logo');
                },
                'categories',
                'images',
                'store.user'
            ])
            ->select('id', 'store_id', 'title_ar', 'title_en', 'description_ar', 'description_en', 'price', 'discounted_price', 'created_at','expire_date','is_approved')
            ->findOrFail($id);

        // Determine offer type based on whether it has codes
        $hasCode = OfferCode::where('offer_id', $id)->exists();
        $offerType = $hasCode ? __('admin_labels.offers_with_code') : __('admin_labels.offer_in_store');

        Log::info("Offer approval status", [$offer->is_approved]);

        return view('admin.pages.views.requested_offers', compact('offer','offerType'));
    }

    /**
     * Approve a requested offer
     *
     * Sets the offer as approved (is_approved = 1) and activates it (is_active = 1).
     *
     * @param int $id The offer ID to approve
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approve($id)
    {
        $offer = Offer::findOrFail($id);
        $offer->is_approved = 1; // Mark as approved
        $offer->is_active = 1;   // Activate the offer
        $offer->save();

        return redirect()->back()->with('success', __('admin_labels.offer_approved_successfully'));
    }

    /**
     * Reject a requested offer
     *
     * Sets the offer as rejected (is_approved = 2).
     *
     * @param int $id The offer ID to reject
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reject($id)
    {
        $offer = Offer::findOrFail($id);
        $offer->is_approved = 2; // Mark as rejected
        $offer->save();

        return redirect()->back()->with('success', __('admin_labels.offer_rejected_successfully'));
    }
    /**
     * Toggle the active status of a specific offer
     *
     * This method switches the `is_active` flag:
     * - If currently active (1), it becomes inactive (0)
     * - If currently inactive (0), it becomes active (1)
     *
     * Returns a JSON response indicating success or failure.
     *
     * @param int $id Offer ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function update_status($id)
    {
        // Attempt to find the offer or fail with 404
        $offer = Offer::findOrFail($id);

        try {
            // Toggle the is_active status (0 becomes 1, 1 becomes 0)
            $offer->is_active = $offer->is_active == 1 ? 0 : 1;

            // Save the updated status to database
            $offer->save();

            return response()->json([
                'success' => labels('admin_labels.status_updated_successfully', 'Status updated successfully.')
            ]);
        } catch (\Exception $e) {
            // Log the error for debugging purposes
            Log::error('Error updating offer status: ' . $e->getMessage());

            // Return error response
            return response()->json([
                'error' => labels('admin_labels.something_went_wrong', 'Something went wrong')
            ]);
        }
    }
}
