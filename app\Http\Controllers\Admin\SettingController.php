<?php

namespace App\Http\Controllers\Admin;

use App\Models\Setting;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * SettingController handles all admin settings management
 *
 * This controller manages various application settings including:
 * - System settings (app configuration, maintenance mode, versions)
 * - Email settings (SMTP configuration)
 * - Contact settings (about us, policies)
 * - Web settings (Firebase, web-specific configurations)
 * - Media management for settings
 */
class SettingController extends Controller
{
    /**
     * Display the main settings page
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('admin.pages.forms.settings');
    }

    /**
     * Display the system settings form
     *
     * Retrieves timezone list, supported locales, and current system settings
     * to populate the system settings form.
     *
     * @return \Illuminate\View\View
     */
    public function systemSettings()
    {
        // Get available timezones for dropdown
        $timezone = timezoneList();

        // Get supported locales from configuration
        $supported_locales_list = config('eshop_pro.supported_locales_list');

        // Retrieve current system settings from database
        $settings = getSettings('system_settings', true);
        $settings = json_decode($settings, true);

        return view('admin.pages.forms.system_settings', [
            'timezone' => $timezone,
            'supported_locales_list' => $supported_locales_list,
            'settings' => $settings
        ]);
    }

    /**
     * Store or update system settings
     *
     * This method handles the storage of comprehensive system settings including:
     * - App information (name, support details)
     * - Version control for mobile apps
     * - Maintenance mode settings
     * - UI customization options
     * - Logo and favicon management
     *
     * @param Request $request Contains all system setting fields
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function storeSystemSetting(Request $request)
    {
        // Log all incoming request data for debugging
        Log::info($request->all());

        // Check if user is authenticated
        if (!auth()->check()) {
            return redirect('admin/login')->refresh();
        }

        // Validate required fields
        $validator = Validator::make($request->all(), [
            'support_number' => 'required',
            'support_email' => 'required',
            'current_version_of_android_app' => 'required',
            'current_version_of_ios_app' => 'required',
        ]);

        // Handle validation failures
        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }

        // Initialize logo with existing value
        $logo = $request->logo;

        // Handle logo upload if new file is provided
        if ($request->hasFile('logo')) {
            // Get current settings to delete old logo
            $settings = getSettings('system_settings', true);
            $settings = json_decode($settings, true);

            // Delete old logo file
            delete_media($settings['logo']);

            // Upload new logo
            $logo = upload_image($request->file('logo'), 'media', 'logo');
            Log::info($logo);
        }

        // Prepare comprehensive system settings data for storage
        $data = [
            'variable' => 'system_settings',
            'value' => json_encode([
                'app_name' => 'اوفرلي', // Application name (Arabic)
                'support_number' => $request->support_number,
                'support_email' => $request->support_email,
                'logo' => $logo,
                'favicon' => $request->favicon,
                'storage_type' => 'local', // Fixed storage type
                'current_version_of_android_app' => $request->current_version_of_android_app,
                'current_version_of_ios_app' => $request->current_version_of_ios_app,
                // Convert checkbox values to boolean (1/0)
                'version_system_status' => isset($request->version_system_status) && $request->version_system_status == "on" ? 1 : 0,
                'expand_product_image' => isset($request->expand_product_image) && $request->expand_product_image == "on" ? 1 : 0,
                'customer_app_maintenance_status' => isset($request->customer_app_maintenance_status) && $request->customer_app_maintenance_status == "on" ? 1 : 0,
                'offer_request_status' => isset($request->offer_request_status) && $request->offer_request_status == "on" ? 1 : 0,
                'message_for_customer_app' => $request->message_for_customer_app,
                // UI customization settings
                'sidebar_color' => $request->sidebar_color,
                'sidebar_type' => $request->sidebar_type,
                'navbar_fixed' => isset($request->navbar_fixed) && $request->navbar_fixed == "on" ? 1 : 0,
                'theme_mode' => isset($request->theme_mode) && $request->theme_mode == "on" ? 1 : 0,
            ], JSON_UNESCAPED_SLASHES),
        ];

        // Store settings in session for immediate access
        session()->put('system_settings', $data['value']);

        // Check if system settings record already exists
        $setting_data = Setting::where('variable', 'system_settings')->first();

        if ($setting_data == null) {
            // Create new settings record if none exists
            $settings = Setting::create($data);
            $settings->save();

            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_inserted_successfully', 'Settings inserted successfully')
                ]);
            }
        } else {
            // Update existing settings record
            $setting_data->update($data);

            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_updated_successfully', 'Settings updated successfully')
                ]);
            }
        }
    }

    /**
     * Display the email settings form
     *
     * Retrieves current email/SMTP configuration settings for the form.
     *
     * @return \Illuminate\View\View
     */
    public function emailSettings()
    {
        // Get current email settings from database
        $settings = getSettings('email_settings', true);
        $settings = json_decode($settings, true);

        return view('admin.pages.forms.email_settings', [
            'settings' => $settings
        ]);
    }

    /**
     * Store or update email settings
     *
     * Handles SMTP configuration for the application's email functionality.
     * Validates all required email settings and stores them securely.
     *
     * @param Request $request Contains email configuration fields
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function storeEmailSetting(Request $request)
    {
        // Check user authentication
        if (!auth()->check()) {
            return redirect('admin/login')->refresh();
        }

        // Validate all required email configuration fields
        $validator = Validator::make($request->all(), [
            'email' => 'required',
            'password' => 'required',
            'smtp_host' => 'required',
            'smtp_port' => 'required',
            'email_content_type' => 'required',
            'smtp_encryption' => 'required',
        ]);

        // Handle validation failures
        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }

        // Prepare email settings data for storage
        $data = [
            'variable' => 'email_settings',
            'value' => json_encode([
                'email' => $request->email,
                'password' => $request->password,
                'smtp_host' => $request->smtp_host,
                'smtp_port' => $request->smtp_port,
                'email_content_type' => $request->email_content_type,
                'smtp_encryption' => $request->smtp_encryption,
            ], JSON_UNESCAPED_SLASHES),
        ];

        // Check if email settings record already exists
        $setting_data = Setting::where('variable', 'email_settings')->first();

        if ($setting_data == null) {
            // Create new email settings record
            $settings = Setting::create($data);
            $settings->save();

            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_inserted_successfully', 'Settings inserted successfully')
                ]);
            }
        } else {
            // Update existing email settings record
            $setting_data->update($data);

            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_updated_successfully', 'Settings updated successfully')
                ]);
            }
        }
    }

    /**
     * Display the contact settings form
     *
     * Retrieves the current "About Us" content for editing.
     *
     * @return \Illuminate\View\View
     */
    public function contactSettings()
    {
        // Get current about us content from database
        $about_us = getSettings('about_us', true);
        $about_us = json_decode($about_us, true);

        return view('admin.pages.forms.contact_settings', [
            'about_us' => $about_us
        ]);
    }

    /**
     * Store or update "About Us" content
     *
     * Uses the generic policy storage method to handle about us content.
     *
     * @param Request $request Contains about_us content
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function storeAboutUs(Request $request)
    {
        return $this->storePoliciesAndContactSetting($request, 'about_us');
    }

    /**
     * Display the system policies management form
     *
     * Retrieves all system policies (privacy, shipping, terms, return policy)
     * and displays them in a single management interface.
     *
     * @return \Illuminate\View\View
     */
    public function systemPolicies()
    {
        // Retrieve all policy types from database
        $privacy_policy = getSettings('privacy_policy', true);
        $privacy_policy = json_decode($privacy_policy, true);

        $shipping_policy = getSettings('shipping_policy', true);
        $shipping_policy = json_decode($shipping_policy, true);

        $terms_and_conditions = getSettings('terms_and_conditions', true);
        $terms_and_conditions = json_decode($terms_and_conditions, true);

        $return_policy = getSettings('return_policy', true);
        $return_policy = json_decode($return_policy, true);

        return view('admin.pages.forms.system_policies', [
            'privacy_policy' => $privacy_policy,
            'shipping_policy' => $shipping_policy,
            'return_policy' => $return_policy,
            'terms_and_conditions' => $terms_and_conditions
        ]);
    }

    /**
     * Store or update terms and conditions
     *
     * Uses the generic policy storage method to handle terms and conditions content.
     *
     * @param Request $request Contains terms_and_conditions content
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function storeTermsAndCondition(Request $request)
    {
        return $this->storePoliciesAndContactSetting($request, 'terms_and_conditions');
    }

    /**
     * Display terms and conditions view page
     *
     * Shows the terms and conditions content along with system settings
     * for public viewing or preview purposes.
     *
     * @return \Illuminate\View\View
     */
    public function termsAndConditions()
    {
        // Get terms and conditions content
        $terms_and_conditions = getSettings('terms_and_conditions', true);
        $terms_and_conditions = json_decode($terms_and_conditions, true);

        // Get system settings for additional context
        $setting = getSettings('system_settings', true);
        $setting = json_decode($setting, true);

        return view('admin.pages.views.terms_and_conditions', [
            'terms_and_conditions' => $terms_and_conditions,
            'setting' => $setting
        ]);
    }

    /**
     * Generic method for storing policies and contact settings
     *
     * This reusable method handles the storage of various text-based settings
     * such as policies, about us content, and other similar content types.
     *
     * @param Request $request Contains the content to be stored
     * @param string $variable_name The setting variable name (e.g., 'privacy_policy', 'about_us')
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function storePoliciesAndContactSetting(Request $request, $variable_name)
    {
        // Validate that the required field is present
        $validator = Validator::make($request->all(), [
            $variable_name => 'required',
        ]);

        // Handle validation failures
        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }

        // Prepare data for storage
        $data = [
            'variable' => $variable_name,
            'value' => json_encode([
                $variable_name => isset($request->$variable_name) ? $request->$variable_name : '',
            ], JSON_UNESCAPED_SLASHES),
        ];

        // Check if setting already exists
        $setting_data = Setting::where('variable', $variable_name)->first();

        if ($setting_data == null) {
            // Create new setting record
            $settings = Setting::create($data);
            $settings->save();

            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_inserted_successfully', 'Settings inserted successfully')
                ]);
            }
        } else {
            // Update existing setting record
            $setting_data->update($data);

            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_updated_successfully', 'Settings updated successfully')
                ]);
            }
        }
    }

    /**
     * Display the web settings form
     *
     * Retrieves web-specific settings and Firebase configuration
     * for web application management.
     *
     * @return \Illuminate\View\View
     */
    public function webSettings()
    {
        // Get web-specific settings
        $web_settings = getSettings('web_settings', true);
        $web_settings = json_decode($web_settings, true);

        // Get Firebase configuration settings
        $firebase_settings = getSettings('firebase_settings', true);
        $firebase_settings = json_decode($firebase_settings, true);

        return view('admin.pages.forms.web_settings', [
            'web_settings' => $web_settings,
            'firebase_settings' => $firebase_settings
        ]);
    }

    /**
     * Helper method to retrieve system settings and a specific policy
     *
     * This private method combines system settings with a specific policy
     * for views that need both pieces of information.
     *
     * @param string $policyName The name of the policy to retrieve
     * @return array Combined settings and policy data
     */
    private function getSettingsAndPolicy($policyName)
    {
        // Get system settings
        $setting = json_decode(getSettings('system_settings', true), true);

        // Get specific policy content
        $policy = json_decode(getSettings($policyName, true), true);

        return ['setting' => $setting, $policyName => $policy];
    }

    /**
     * Display terms and conditions view (alternative method)
     *
     * Uses the helper method to get both system settings and terms content.
     *
     * @return \Illuminate\View\View
     */
    public function terms_and_conditions()
    {
        return view('admin.pages.views.terms_and_conditions', $this->getSettingsAndPolicy('terms_and_conditions'));
    }

    /**
     * Remove media files from system settings
     *
     * This method handles the removal of specific media files (images)
     * from system settings arrays and updates the database accordingly.
     *
     * @param Request $request Contains field name and image name to remove
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeSettingMedia(Request $request)
    {
        // Get current system settings
        $system_settings = getSettings('system_settings', true);
        $system_settings = json_decode($system_settings, true);

        // Get the images array for the specified field
        $images = $system_settings[$request['field']];

        // Find and remove the specific image from the array
        $search_index = array_search($request['img_name'], $images);
        if ($search_index !== false) {
            unset($images[$search_index]);
        }

        // Update the system settings with the modified images array
        $system_settings[$request['field']] = $images;

        // Prepare updated data for database storage
        $data = [
            'variable' => 'system_settings',
            'value' => json_encode(
                $system_settings,
                JSON_UNESCAPED_SLASHES
            ),
        ];

        // Update the database record
        $setting_data = Setting::where('variable', 'system_settings')->first();
        if ($setting_data == null) {
            // No settings found - deletion failed
            $response['is_deleted'] = false;
        } else {
            // Update existing record with modified settings
            $setting_data->update($data);
            $response['is_deleted'] = true;
        }

        return response()->json([$response]);
    }
}
