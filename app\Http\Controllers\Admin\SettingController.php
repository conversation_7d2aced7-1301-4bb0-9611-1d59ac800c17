<?php

namespace App\Http\Controllers\Admin;

use App\Models\Setting;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class SettingController extends Controller
{
    public function index()
    {
        return view('admin.pages.forms.settings');
    }

    public function systemSettings()
    {

        $timezone = timezoneList();

        $supported_locales_list = config('eshop_pro.supported_locales_list');

        $settings = getSettings('system_settings', true);
        $settings = json_decode($settings, true);

        return view('admin.pages.forms.system_settings', [
            'timezone' => $timezone,
            'supported_locales_list' => $supported_locales_list,
            'settings' => $settings
        ]);
    }

 
  
  
/////edit it
    public function storeSystemSetting(Request $request)
    {
        Log::info($request->all());

        if (!auth()->check()) {
            return redirect('admin/login')->refresh();
        }

        $validator = Validator::make($request->all(), [
            'support_number' => 'required',
            'support_email' => 'required',
            'current_version_of_android_app' => 'required',
            'current_version_of_ios_app' => 'required',

        ]);



        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }
        $logo=$request->logo;
         // If a new image is uploaded, delete the old one and upload the new image
         if ($request->hasFile('logo')) {
            $settings = getSettings('system_settings', true);
            $settings = json_decode($settings, true);
            delete_media($settings['logo']);
            $logo= upload_image($request->file('logo'), 'media', 'logo');
            Log::info($logo);
        }

        // Prepare the data to be stored
        $data = [
            'variable' => 'system_settings',
            'value' => json_encode([
                'app_name' => 'اوفرلي',
                'support_number' => $request->support_number,
                'support_email' => $request->support_email,
                'logo' => $logo,
                'favicon' => $request->favicon,
                'storage_type' => 'local',
                'current_version_of_android_app' => $request->current_version_of_android_app,
                'current_version_of_ios_app' => $request->current_version_of_ios_app,
                'version_system_status' => isset($request->version_system_status) && $request->version_system_status == "on" ? 1 : 0,
                'expand_product_image' => isset($request->expand_product_image) && $request->expand_product_image == "on" ? 1 : 0,
                'customer_app_maintenance_status' => isset($request->customer_app_maintenance_status) && $request->customer_app_maintenance_status == "on" ? 1 : 0,
                'offer_request_status' => isset($request->offer_request_status) && $request->offer_request_status == "on" ? 1 : 0,
                'message_for_customer_app' => $request->message_for_customer_app,
                'sidebar_color' => $request->sidebar_color,
                'sidebar_type' => $request->sidebar_type,
                'navbar_fixed' => isset($request->navbar_fixed) && $request->navbar_fixed == "on" ? 1 : 0,
                'theme_mode' => isset($request->theme_mode) && $request->theme_mode == "on" ? 1 : 0,
            ], JSON_UNESCAPED_SLASHES),
        ];

        // Check if settings already exist in the database
        session()->put('system_settings', $data['value']);
        $setting_data = Setting::where('variable', 'system_settings')->first();
        if ($setting_data == null) {
            // Create a new record if no settings found
            $settings = Setting::create($data);
            $settings->save();
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_inserted_successfully', 'Settings inserted successfully')
                ]);
            }
        } else {
            // Update the existing record with the new settings
            $setting_data->update($data);
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_updated_successfully', 'Settings updated successfully')
                ]);
            }
        }
    }

    public function emailSettings()
    {
        $settings = getSettings('email_settings', true);
        $settings = json_decode($settings, true);

        return view('admin.pages.forms.email_settings', [
            'settings' => $settings
        ]);
    }

    public function storeEmailSetting(Request $request)
    {
        if (!auth()->check()) {
            return redirect('admin/login')->refresh();
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required',
            'password' => 'required',
            'smtp_host' => 'required',
            'smtp_port' => 'required',
            'email_content_type' => 'required',
            'smtp_encryption' => 'required',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }
        // Prepare the data to be stored
        $data = [
            'variable' => 'email_settings',
            'value' => json_encode([
                'email' => $request->email,
                'password' => $request->password,
                'smtp_host' => $request->smtp_host,
                'smtp_port' => $request->smtp_port,
                'email_content_type' => $request->email_content_type,
                'smtp_encryption' => $request->smtp_encryption,
            ], JSON_UNESCAPED_SLASHES),
        ];
        // Check if settings already exist in the database
        $setting_data = Setting::where('variable', 'email_settings')->first();
        if ($setting_data == null) {
            // Create a new record if no settings found
            $settings = Setting::create($data);
            $settings->save();
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_inserted_successfully', 'Settings inserted successfully')
                ]);
            }
        } else {
            // Update the existing record with the new settings
            $setting_data->update($data);
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_updated_successfully', 'Settings updated successfully')
                ]);
            }
        }
    }

    // notification and contacct setting

    public function contactSettings()
    {

        $about_us = getSettings('about_us', true);
        $about_us = json_decode($about_us, true);

        return view('admin.pages.forms.contact_settings', [
            'about_us' => $about_us
        ]);
    }

   

    public function storeAboutUs(Request $request)
    {
        return $this->storePoliciesAndContactSetting($request, 'about_us');
    }


    // system policies

    public function systemPolicies()
    {
        $privacy_policy = getSettings('privacy_policy', true);
        $privacy_policy = json_decode($privacy_policy, true);

        $shipping_policy = getSettings('shipping_policy', true);
        $shipping_policy = json_decode($shipping_policy, true);

        $terms_and_conditions = getSettings('terms_and_conditions', true);
        $terms_and_conditions = json_decode($terms_and_conditions, true);

        $return_policy = getSettings('return_policy', true);
        $return_policy = json_decode($return_policy, true);

        return view('admin.pages.forms.system_policies', [
            'privacy_policy' => $privacy_policy,
            'shipping_policy' => $shipping_policy,
            'return_policy' => $return_policy,
            'terms_and_conditions' => $terms_and_conditions
        ]);
    }

    public function storeTermsAndCondition(Request $request)
    {
        return $this->storePoliciesAndContactSetting($request, 'terms_and_conditions');
    }

   
    public function termsAndConditions()
    {
        $terms_and_conditions = getSettings('terms_and_conditions', true);
        $terms_and_conditions = json_decode($terms_and_conditions, true);

        $setting = getSettings('system_settings', true);
        $setting = json_decode($setting, true);

        return view('admin.pages.views.terms_and_conditions', [
            'terms_and_conditions' => $terms_and_conditions,
            'setting' => $setting
        ]);
    }



    // general function for store policies and contact setting

    public function storePoliciesAndContactSetting(Request $request, $variable_name)
    {
        $validator = Validator::make($request->all(), [
            $variable_name => 'required',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }

        $data = [
            'variable' => $variable_name,
            'value' => json_encode([
                $variable_name => isset($request->$variable_name) ? $request->$variable_name : '',
            ], JSON_UNESCAPED_SLASHES),
        ];

        $setting_data = Setting::where('variable', $variable_name)->first();
        if ($setting_data == null) {
            $settings = Setting::create($data);
            $settings->save();
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_inserted_successfully', 'Settings inserted successfully')
                ]);
            }
        } else {
            $setting_data->update($data);
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_updated_successfully', 'Settings updated successfully')
                ]);
            }
        }
    }

    public function webSettings()
    {

        $web_settings = getSettings('web_settings', true);
        $web_settings = json_decode($web_settings, true);

        $firebase_settings = getSettings('firebase_settings', true);
        $firebase_settings = json_decode($firebase_settings, true);
        return view('admin.pages.forms.web_settings', [
            'web_settings' => $web_settings,
            'firebase_settings' => $firebase_settings
        ]);
    }


    private function getSettingsAndPolicy($policyName)
    {
        $setting = json_decode(getSettings('system_settings', true), true);
        $policy = json_decode(getSettings($policyName, true), true);

        return ['setting' => $setting, $policyName => $policy];
    }

  
    public function terms_and_conditions()
    {
        return view('admin.pages.views.terms_and_conditions', $this->getSettingsAndPolicy('terms_and_conditions'));
    }

   
    public function removeSettingMedia(Request $request)
    {

        $system_settings = getSettings('system_settings', true);
        $system_settings = json_decode($system_settings, true);

        $images = $system_settings[$request['field']];

        $serch_index = array_search($request['img_name'], $images);
        if ($serch_index !== false) {
            unset($images[$serch_index]);
        }

        $system_settings[$request['field']] = $images;


        $data = [
            'variable' => 'system_settings',
            'value' => json_encode(
                $system_settings,
                JSON_UNESCAPED_SLASHES
            ),
        ];

        $setting_data = Setting::where('variable', 'system_settings')->first();
        if ($setting_data == null) {
            $response['is_deleted'] = false;
        } else {
            // Update the existing record with the new settings
            $setting_data->update($data);
            $response['is_deleted'] = true;
        }
        return response()->json([$response]);
    }



 
}
