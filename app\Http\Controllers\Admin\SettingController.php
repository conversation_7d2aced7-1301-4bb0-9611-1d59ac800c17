<?php

namespace App\Http\Controllers\Admin;

use ZipArchive;
use App\Models\Setting;
use App\Models\Updates;
use App\Models\Currency;
use App\Models\TimeSlot;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class SettingController extends Controller
{
    public function index()
    {
        return view('admin.pages.forms.settings');
    }

    public function systemSettings()
    {

        $timezone = timezoneList();

        $supported_locales_list = config('eshop_pro.supported_locales_list');

        $settings = getSettings('system_settings', true);
        $settings = json_decode($settings, true);

        return view('admin.pages.forms.system_settings', [
            'timezone' => $timezone,
            'supported_locales_list' => $supported_locales_list,
            'settings' => $settings
        ]);
    }

 
  
  
/////edit it
    public function storeSystemSetting(Request $request)
    {
        Log::info($request->all());

        if (!auth()->check()) {
            return redirect('admin/login')->refresh();
        }

        $validator = Validator::make($request->all(), [
            'support_number' => 'required',
            'support_email' => 'required',
            'current_version_of_android_app' => 'required',
            'current_version_of_ios_app' => 'required',

        ]);



        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }
        $logo=$request->logo;
         // If a new image is uploaded, delete the old one and upload the new image
         if ($request->hasFile('logo')) {
            $settings = getSettings('system_settings', true);
            $settings = json_decode($settings, true);
            delete_media($settings['logo']);
            $logo= upload_image($request->file('logo'), 'media', 'logo');
            Log::info($logo);
        }

        // Prepare the data to be stored
        $data = [
            'variable' => 'system_settings',
            'value' => json_encode([
                'app_name' => 'اوفرلي',
                'support_number' => $request->support_number,
                'support_email' => $request->support_email,
                'logo' => $logo,
                'favicon' => $request->favicon,
                'storage_type' => 'local',
                'current_version_of_android_app' => $request->current_version_of_android_app,
                'current_version_of_ios_app' => $request->current_version_of_ios_app,
                'version_system_status' => isset($request->version_system_status) && $request->version_system_status == "on" ? 1 : 0,
                'expand_product_image' => isset($request->expand_product_image) && $request->expand_product_image == "on" ? 1 : 0,
                'customer_app_maintenance_status' => isset($request->customer_app_maintenance_status) && $request->customer_app_maintenance_status == "on" ? 1 : 0,
                'offer_request_status' => isset($request->offer_request_status) && $request->offer_request_status == "on" ? 1 : 0,
                'message_for_customer_app' => $request->message_for_customer_app,
                'sidebar_color' => $request->sidebar_color,
                'sidebar_type' => $request->sidebar_type,
                'navbar_fixed' => isset($request->navbar_fixed) && $request->navbar_fixed == "on" ? 1 : 0,
                'theme_mode' => isset($request->theme_mode) && $request->theme_mode == "on" ? 1 : 0,
            ], JSON_UNESCAPED_SLASHES),
        ];

        // Check if settings already exist in the database
        session()->put('system_settings', $data['value']);
        $setting_data = Setting::where('variable', 'system_settings')->first();
        if ($setting_data == null) {
            // Create a new record if no settings found
            $settings = Setting::create($data);
            $settings->save();
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_inserted_successfully', 'Settings inserted successfully')
                ]);
            }
        } else {
            // Update the existing record with the new settings
            $setting_data->update($data);
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_updated_successfully', 'Settings updated successfully')
                ]);
            }
        }
    }

    public function emailSettings()
    {
        $settings = getSettings('email_settings', true);
        $settings = json_decode($settings, true);

        return view('admin.pages.forms.email_settings', [
            'settings' => $settings
        ]);
    }

    public function storeEmailSetting(Request $request)
    {
        if (!auth()->check()) {
            return redirect('admin/login')->refresh();
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required',
            'password' => 'required',
            'smtp_host' => 'required',
            'smtp_port' => 'required',
            'email_content_type' => 'required',
            'smtp_encryption' => 'required',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }
        // Prepare the data to be stored
        $data = [
            'variable' => 'email_settings',
            'value' => json_encode([
                'email' => $request->email,
                'password' => $request->password,
                'smtp_host' => $request->smtp_host,
                'smtp_port' => $request->smtp_port,
                'email_content_type' => $request->email_content_type,
                'smtp_encryption' => $request->smtp_encryption,
            ], JSON_UNESCAPED_SLASHES),
        ];
        // Check if settings already exist in the database
        $setting_data = Setting::where('variable', 'email_settings')->first();
        if ($setting_data == null) {
            // Create a new record if no settings found
            $settings = Setting::create($data);
            $settings->save();
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_inserted_successfully', 'Settings inserted successfully')
                ]);
            }
        } else {
            // Update the existing record with the new settings
            $setting_data->update($data);
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_updated_successfully', 'Settings updated successfully')
                ]);
            }
        }
    }

   

   

    public function timeSlotSettings()
    {
        $time_slot_config = getSettings('time_slot_config', true);
        $time_slot_config = json_decode($time_slot_config, true);
        return view('admin.pages.forms.time_slot_settings', [
            'time_slot_config' => $time_slot_config
        ]);
    }
    public function storeTimeSlotConfig(Request $request)
    {
        if (($request->is_time_slots_enabled) == "on") {
            $validator = Validator::make($request->all(), [
                'delivery_starts_from' => 'required',
                'allowed_days' => 'required',
            ]);
            if ($validator->fails()) {
                $errors = $validator->errors();

                if ($request->ajax()) {
                    return response()->json(['errors' => $errors->all()], 422);
                }
                return redirect()->back()->withErrors($errors)->withInput();
            }
        }


        // Prepare the data to be stored
        $data = [
            'variable' => 'time_slot_config',
            'value' => json_encode([
                'is_time_slots_enabled' => isset($request->is_time_slots_enabled) && $request->is_time_slots_enabled == "on" ? 1 : 0,
                'delivery_starts_from' => $request->delivery_starts_from,
                'allowed_days' => $request->allowed_days,
            ], JSON_UNESCAPED_SLASHES),
        ];
        // Check if settings already exist in the database
        $setting_data = Setting::where('variable', 'time_slot_config')->first();
        if ($setting_data == null) {
            // Create a new record if no settings found
            $settings = Setting::create($data);
            $settings->save();
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_inserted_successfully', 'Settings inserted successfully')
                ]);
            }
        } else {
            // Update the existing record with the new settings
            $setting_data->update($data);
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_updated_successfully', 'Settings updated successfully')
                ]);
            }
        }
    }
    public function storeTimeSlot(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'from_time' => 'required',
            'to_time' => 'required',
            'last_order_time' => 'required',
            'status' => 'required',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }

        $time_slot = new TimeSlot();
        $time_slot->title = $request->title;
        $time_slot->from_time = $request->from_time;
        $time_slot->to_time = $request->to_time;
        $time_slot->last_order_time = $request->last_order_time;
        $time_slot->status = $request->status;
        $time_slot->save();

        if ($request->ajax()) {
            return response()->json([
                'message' => labels('admin_labels.time_slot_added_successfully', 'Time slot added successfully')
            ]);
        }
    }

    public function timeSlotList(Request $request)
    {
        $search = trim(request('search'));
        $sort = (request('sort')) ? request('sort') : "id";
        $order = (request('order')) ? request('order') : "DESC";
        $offset = $search || (request('pagination_offset')) ? (request('pagination_offset')) : "0";
        $limit = (request('limit')) ? request('limit') : "10";

        $time_slot_data = TimeSlot::when($search, function ($query) use ($search) {
            return $query->where('title', 'like', '%' . $search . '%');
        });

        $total = $time_slot_data->count();

        // Use Paginator to handle the server-side pagination
        $time_slots = $time_slot_data->orderBy($sort, $order)->offset($offset)
            ->limit($limit)
            ->get();

        // Prepare the data for the "Actions" field
        $data = $time_slots->map(function ($t) {
            $delete_url = route('time_slot.destroy', $t->id);
            $action = '<div class="dropdown bootstrap-table-dropdown">
                <a href="#" class="text-dark" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="bx bx-dots-horizontal-rounded"></i>
                </a>
                <div class="dropdown-menu table_dropdown" aria-labelledby="dropdownMenuButton">
                    <a class="dropdown-item dropdown_menu_items" href="edit_url"><i class="bx bx-pencil mx-2"></i> Edit</a>
                    <a class="dropdown-item delete-data dropdown_menu_items" data-url="' . $delete_url . '"><i class="bx bx-trash mx-2"></i> Delete</a>
                </div>
            </div>';

            return [
                'id' => $t->id,
                'title' => $t->title,
                'from_time' => $t->from_time,
                'to_time' => $t->to_time,
                'last_order_time' => $t->last_order_time,

                'status' => '<select class="form-select status_dropdown change_toggle_status ' . ($t->status == 1 ? 'active_status' : 'inactive_status') . '" data-id="' . $t->id . '" data-url="/admin/settings/time_slot/update_status/' . $t->id . '" aria-label="">
                  <option value="1" ' . ($t->status == 1 ? 'selected' : '') . '>Active</option>
                  <option value="0" ' . ($t->status == 0 ? 'selected' : '') . '>Deactive</option>
              </select>',
                'operate' => $action,
            ];
        });

        return response()->json([
            "rows" => $data, // Return the formatted data for the "Actions" field
            "total" => $total,
        ]);
    }

    public function timeSlotDestroy($id)
    {
        $time_slot = TimeSlot::find($id);

        if ($time_slot->delete()) {
            return response()->json([
                'error' => false,
                'message' => labels('admin_labels.time_slot_deleted_successfully', 'Time Slot deleted successfully!')
            ]);
        } else {
            return response()->json(['error' => labels('admin_labels.something_went_wrong', 'Something went wrong')]);
        }
    }

    public function updateTimeSlotStatus($id)
    {
        $time_slot = TimeSlot::findOrFail($id);
        $time_slot->status = $time_slot->status == '1' ? '0' : '1';
        $time_slot->save();
        return response()->json(['success' => labels('admin_labels.status_updated_successfully', 'Status updated successfully.')]);
    }

    

    public function storeExchangeRateAapId(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'exchange_rate_app_id' => 'required',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }

        $data = [
            'variable' => 'exchange_rate_app_id',
            'value' => json_encode([
                'exchange_rate_app_id' => isset($request->exchange_rate_app_id) && $request->exchange_rate_app_id != "" ? $request->exchange_rate_app_id : "",
            ], JSON_UNESCAPED_SLASHES),
        ];
        $exchange_rate_app_id = Setting::where('variable', 'exchange_rate_app_id')->first();
        if ($exchange_rate_app_id == null) {
            $settings = Setting::create($data);
            $settings->save();
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.data_inserted_successfully', 'Data inserted successfully')
                ]);
            }
        } else {
            $exchange_rate_app_id->update($data);
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.data_updated_successfully', 'Data updated successfully')
                ]);
            }
        }
    }




    public function updateExchangeRates($app_id)
    {
        $api_response = $this->get_exchange_rates($app_id);

        if ($api_response) {

            Currency::update_exchange_rate_from_api($api_response['rates'], $api_response['base']);

            return response()->json([
                'message' => labels('admin_labels.exchange_rates_updated_successfully', 'Exchange rates updated successfully')
            ]);
        }

        return response()->json([
            'error' => labels('admin_labels.unable_to_fetch_api_response', 'Unable to fetch API response')
        ], 500);
    }

    // Your existing get_exchange_rates function
    public function getExchangeRates($app_id)
    {
        $app_id = getSettings('exchange_rate_app_id', true);
        $app_id = json_decode($app_id, true);

        $app_id = $app_id['exchange_rate_app_id'];

        $url = "https://openexchangerates.org/api/latest.json?app_id={$app_id}";

        try {
            $response = Http::get($url);
            if ($response->successful()) {
                $data = $response->json();
                return $data;
            } else {
                return null;
            }
        } catch (\Exception $e) {
            return null;
        }
    }

    public function setDefaultCurrency(Request $request)
    {
        $currency_id = $request->input('currency_id');

        // First, update all currencies to 'is_default' = 0 (false)
        Currency::query()->update(['is_default' => 0]);

        // Then, set the selected currency to 'is_default' = 1 (true)
        $currency = Currency::find($currency_id);
        if ($currency) {
            $currency->is_default = 1;
            $currency->save();
        }

        if ($request->ajax()) {
            return response()->json([
                'message' => labels('admin_labels.default_currency_set_successfully', 'Default currency set successfully')
            ]);
        }
    }

    public function currencyList(Request $request)
    {
        $search = trim(request('search'));
        $sort = (request('sort')) ? request('sort') : "id";
        $order = (request('order')) ? request('order') : "DESC";
        $offset = $search || (request('pagination_offset')) ? (request('pagination_offset')) : "0";
        $limit = (request('limit')) ? request('limit') : "10";

        $city_data = Currency::when($search, function ($query) use ($search) {
            return $query->where('name', 'like', '%' . $search . '%');
        });

        $total = $city_data->count();

        // Use Paginator to handle the server-side pagination
        $currencies = $city_data->orderBy($sort, $order)->offset($offset)
            ->limit($limit)
            ->get();

        // Prepare the data for the "Actions" field
        $data = $currencies->map(function ($c) {
            $delete_url = route('currency.destroy', $c->id);
            $action = '<div class="dropdown bootstrap-table-dropdown">
                <a href="#" class="text-dark" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="bx bx-dots-horizontal-rounded"></i>
                </a>

                <div class="dropdown-menu table_dropdown" aria-labelledby="dropdownMenuButton">
                    <a class="dropdown-item edit-currency dropdown_menu_items" data-id="' . $c->id . '"><i class="bx bx-pencil mx-2"></i> Edit</a>
                    <a class="dropdown-item delete-data dropdown_menu_items" data-url="' . $delete_url . '"><i class="bx bx-trash mx-2"></i> Delete</a>
                </div>
            </div>';

            return [
                'id' => $c->id,
                'name' => $c->name . '<label class="badge bg-success">' . ($c->is_default == 1 ? "Default" : "") . '</label>',
                'symbol' => $c->symbol,
                'exchange_rate' => $c->exchange_rate,
                'status' => '<select class="form-select status_dropdown change_toggle_status ' . ($c->status == 1 ? 'active_status' : 'inactive_status') . '" data-id="' . $c->id . '" data-url="/admin/currency/update_status/' . $c->id . '" aria-label="">
                  <option value="1" ' . ($c->status == 1 ? 'selected' : '') . '>Active</option>
                  <option value="0" ' . ($c->status == 0 ? 'selected' : '') . '>Deactive</option>
              </select>',
                'operate' => $action,
            ];
        });

        return response()->json([
            "rows" => $data,
            "total" => $total,
        ]);
    }


    // notification and contacct setting

    public function contactSettings()
    {

        $about_us = getSettings('about_us', true);
        $about_us = json_decode($about_us, true);

        return view('admin.pages.forms.contact_settings', [
            'about_us' => $about_us
        ]);
    }

   

    public function storeAboutUs(Request $request)
    {
        return $this->storePoliciesAndContactSetting($request, 'about_us');
    }


    // system policies

    public function systemPolicies()
    {
        $privacy_policy = getSettings('privacy_policy', true);
        $privacy_policy = json_decode($privacy_policy, true);

        $shipping_policy = getSettings('shipping_policy', true);
        $shipping_policy = json_decode($shipping_policy, true);

        $terms_and_conditions = getSettings('terms_and_conditions', true);
        $terms_and_conditions = json_decode($terms_and_conditions, true);

        $return_policy = getSettings('return_policy', true);
        $return_policy = json_decode($return_policy, true);

        return view('admin.pages.forms.system_policies', [
            'privacy_policy' => $privacy_policy,
            'shipping_policy' => $shipping_policy,
            'return_policy' => $return_policy,
            'terms_and_conditions' => $terms_and_conditions
        ]);
    }

    public function storeTermsAndCondition(Request $request)
    {
        return $this->storePoliciesAndContactSetting($request, 'terms_and_conditions');
    }

   
    public function termsAndConditions()
    {
        $terms_and_conditions = getSettings('terms_and_conditions', true);
        $terms_and_conditions = json_decode($terms_and_conditions, true);

        $setting = getSettings('system_settings', true);
        $setting = json_decode($setting, true);

        return view('admin.pages.views.terms_and_conditions', [
            'terms_and_conditions' => $terms_and_conditions,
            'setting' => $setting
        ]);
    }


   



    // general function for store policies and contact setting

    public function storePoliciesAndContactSetting(Request $request, $variable_name)
    {
        $validator = Validator::make($request->all(), [
            $variable_name => 'required',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors();

            if ($request->ajax()) {
                return response()->json(['errors' => $errors->all()], 422);
            }
            return redirect()->back()->withErrors($errors)->withInput();
        }

        $data = [
            'variable' => $variable_name,
            'value' => json_encode([
                $variable_name => isset($request->$variable_name) ? $request->$variable_name : '',
            ], JSON_UNESCAPED_SLASHES),
        ];

        $setting_data = Setting::where('variable', $variable_name)->first();
        if ($setting_data == null) {
            $settings = Setting::create($data);
            $settings->save();
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_inserted_successfully', 'Settings inserted successfully')
                ]);
            }
        } else {
            $setting_data->update($data);
            if ($request->ajax()) {
                return response()->json([
                    'message' => labels('admin_labels.settings_updated_successfully', 'Settings updated successfully')
                ]);
            }
        }
    }

    public function webSettings()
    {

        $web_settings = getSettings('web_settings', true);
        $web_settings = json_decode($web_settings, true);

        $firebase_settings = getSettings('firebase_settings', true);
        $firebase_settings = json_decode($firebase_settings, true);
        return view('admin.pages.forms.web_settings', [
            'web_settings' => $web_settings,
            'firebase_settings' => $firebase_settings
        ]);
    }


    private function getSettingsAndPolicy($policyName)
    {
        $setting = json_decode(getSettings('system_settings', true), true);
        $policy = json_decode(getSettings($policyName, true), true);

        return ['setting' => $setting, $policyName => $policy];
    }

    public function privacy_policy()
    {
        return view('admin.pages.views.privacy_policy', $this->getSettingsAndPolicy('privacy_policy'));
    }
    public function delivery_boy_privacy_policy()
    {
        return view('admin.pages.views.delivery_boy_privacy_policy', $this->getSettingsAndPolicy('delivery_boy_privacy_policy'));
    }

    public function terms_and_conditions()
    {
        return view('admin.pages.views.terms_and_conditions', $this->getSettingsAndPolicy('terms_and_conditions'));
    }
    public function delivery_boy_terms_and_conditions()
    {
        return view('admin.pages.views.delivery_boy_terms_and_conditions', $this->getSettingsAndPolicy('delivery_boy_terms_and_conditions'));
    }

    public function shipping_policy()
    {
        return view('admin.pages.views.shipping_policy', $this->getSettingsAndPolicy('shipping_policy'));
    }

    public function return_policy()
    {
        return view('admin.pages.views.return_policy', $this->getSettingsAndPolicy('return_policy'));
    }

   
    public function removeSettingMedia(Request $request)
    {

        $system_settings = getSettings('system_settings', true);
        $system_settings = json_decode($system_settings, true);

        $images = $system_settings[$request['field']];

        $serch_index = array_search($request['img_name'], $images);
        if ($serch_index !== false) {
            unset($images[$serch_index]);
        }

        $system_settings[$request['field']] = $images;


        $data = [
            'variable' => 'system_settings',
            'value' => json_encode(
                $system_settings,
                JSON_UNESCAPED_SLASHES
            ),
        ];

        $setting_data = Setting::where('variable', 'system_settings')->first();
        if ($setting_data == null) {
            $response['is_deleted'] = false;
        } else {
            // Update the existing record with the new settings
            $setting_data->update($data);
            $response['is_deleted'] = true;
        }
        return response()->json([$response]);
    }



   
}
