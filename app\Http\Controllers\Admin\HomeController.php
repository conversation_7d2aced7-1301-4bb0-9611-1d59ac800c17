<?php

namespace App\Http\Controllers\Admin;

use App\Enums\AdvertismentPackageType;
use App\Enums\AdvertismentStatus;
use App\Models\awfarly\Advertisment;
use App\Models\awfarly\Category;
use App\Models\awfarly\Offer;
use App\Models\awfarly\OfferClaim;
use App\Models\awfarly\ReportIssuse;
use App\Models\awfarly\Role;
use App\Models\awfarly\Store;
use App\Models\awfarly\User;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

use Illuminate\Support\Str;

/**
 * HomeController handles the admin dashboard functionality
 *
 * This controller manages:
 * - Dashboard statistics and metrics
 * - User growth analytics and charts
 * - Recent activities tracking
 * - Top performers (stores, offers, categories, users)
 * - Advertisement statistics and analytics
 * - Report management
 */
class HomeController extends Controller
{
    /**
     * Display the admin dashboard with comprehensive statistics and analytics
     *
     * This method aggregates various platform metrics including user statistics,
     * store performance, offer analytics, advertisement data, and recent activities
     * to provide a comprehensive overview of the platform's performance.
     *
     * @return \Illuminate\View\View Returns the admin dashboard view with all statistics
     */
    public function index()
    {
        // Initialize basic variables
        $id = 0;
        $store_id = getStoreId();

        // Determine user's preferred theme mode
        $dark_mode = Auth::user() && Auth::user()->dark_mode < 1 ? 'light' : 'dark';

        // ===== BASIC PLATFORM STATISTICS =====

        // User statistics
        $totalUsers = User::where('role_id', Role::where('name', 'customer')->value('id'))->count();
        $bannedUsers = User::where('is_banned', true)->count();
        $newUsersToday = User::whereDate('created_at', today())->count();
        $activeUsersToday = User::whereDate('last_active', today())->where('is_active', true)->count();

        // Store statistics
        $activeStores = Store::where('is_active', true)->count();
        $pandingStore = Store::where('is_approved', false)->count(); // Note: typo in original variable name

        // Offer statistics
        $activeOffers = Offer::where('is_active', true)->count();
        $pendingOffers = Offer::where('is_approved', false)->count();
        $claimedOffers = OfferClaim::count();
        $redeemedOffers = OfferClaim::where('is_redeemed', true)->count();

        // Advertisement statistics
        $adsRunning = Advertisment::whereDate('end_date', '>=', now())->count();
        $pendingAs = Advertisment::where('status', AdvertismentStatus::PENDING)->count();

        // ===== USER ANALYTICS =====

        // Get user counter data (likely from a helper function)
        $user_counter = countNewUsers();

        // Prepare user growth chart data for the last 30 days
        $userGrowth = [
            'labels' => [], // Date labels (e.g. ['Jun 1', 'Jun 2', ..., 'Jun 7'])
            'data' => [],   // User registration counts (e.g. [2, 5, 8, 3, ...])
        ];

        // Generate daily user registration data for the past 30 days
        $startDate = now()->subDays(30);
        for ($i = 0; $i <= 30; $i++) {
            $date = $startDate->copy()->addDays($i);
            $label = $date->format('M d');
            $count = \App\Models\User::whereDate('created_at', $date)->count();
            $userGrowth['labels'][] = $label;
            $userGrowth['data'][] = $count;
        }

        // ===== RECENT ACTIVITIES FEED =====

        // Initialize collection for recent activities
        $recentActivities = collect();

        // Fetch recent user registrations (last 5)
        $recentUsers = User::latest()->take(5)->get()->map(function ($user) {
            return [
                'type' => 'user',
                'message' => __('admin_labels.new_user_registered') . $user->username,
                'time' => $user->created_at,
                'url' => route('admin.customers.show', ['id' => $user->id]),
            ];
        });

        // Fetch recent offer creations (last 5)
        $recentOffers = Offer::latest()->take(5)->get()->map(function ($offer) {
            return [
                'type' => 'offer',
                'message' => __('admin_labels.new_offer_created') . $offer->title_ar,
                'time' => $offer->created_at,
                'url' => route('admin.offers.show', ['id' => $offer->id]),
            ];
        });

        // Fetch recent offer claims (last 5)
        $recentClaims = OfferClaim::latest()->take(5)->get()->map(function ($claim) {
            return [
                'type' => 'claim',
                'message' => __('admin_labels.offer_claimed') . $claim->user_id,
                'time' => $claim->created_at,
                'url' => route('admin.offers.show', ['id' => $claim->offer_id]),
            ];
        });

        // Combine all activities, sort by time, and limit to 10 most recent
        $recentActivities = $recentUsers
            ->merge($recentOffers)
            ->merge($recentClaims)
            ->sortByDesc('time')
            ->take(10);
        // ===== TOP PERFORMERS ANALYTICS =====

        // Top 5 stores by view count
        $topStores = Store::orderByDesc('store_views')
            ->take(5)
            ->get();

        // Top 5 offers by view count (with store relationship)
        $topOffers = Offer::with('store')
            ->orderByDesc('offer_views')
            ->take(5)
            ->get();

        // Top 5 categories by number of claimed offers
        // Uses complex join to count claims through offer-category relationships
        $topCategories = Category::select('categories.*', DB::raw('COUNT(offer_claims.id) as claims_count'))
            ->join('offer_categories', 'categories.id', '=', 'offer_categories.category_id')
            ->join('offers', 'offer_categories.offer_id', '=', 'offers.id')
            ->join('offer_claims', 'offer_claims.offer_id', '=', 'offers.id')
            ->groupBy('categories.id')
            ->orderByDesc('claims_count')
            ->take(5)
            ->get();

        // Top 5 most active users (customers) by number of offer claims
        $topUsers = User::withCount(['claims as claims_count'])
            ->where('role_id', Role::where('name', 'customer')->value('id'))
            ->orderByDesc('claims_count')
            ->take(5)
            ->get();

        // ===== ADVERTISEMENT STATISTICS =====

        // Active ads: approved, paid, and not expired
        $activeAds = Advertisment::where('status', AdvertismentStatus::APPROVED)
            ->where('is_paid', 1)
            ->where('end_date', '>=', now())
            ->count();

        // Pending ads awaiting approval
        $pendingAds = Advertisment::where('status', AdvertismentStatus::PENDING)->count();

        // Approved ads waiting for payment
        $withForPay = Advertisment::where('status', AdvertismentStatus::APPROVED)
            ->where('is_paid', 0)
            ->count();

        // Expired ads that were previously active
        $expiredAds = Advertisment::where('status', AdvertismentStatus::APPROVED)
            ->where('is_paid', 1)
            ->where('end_date', '<', now())
            ->count();
        // ===== ADVERTISEMENT ANALYTICS CHARTS =====

        // Pie chart data: Advertisement counts by package type
        $bannerCount = Advertisment::where('package_type', AdvertismentPackageType::BANNER)->count();
        $premuimCount = Advertisment::where('package_type', AdvertismentPackageType::PERMIUM)->count();
        $popupCount = Advertisment::where('package_type', AdvertismentPackageType::SPOTLIGHT)->count();

        // Bar chart data: Monthly advertisement creation trends (last 12 months)
        $adsPerMonth = Advertisment::select(
                DB::raw("COUNT(id) as count"),
                DB::raw("DATE_FORMAT(created_at, '%Y-%m') as month")
            )
            ->where('created_at', '>=', now()->subMonths(12))
            ->where('status', AdvertismentStatus::APPROVED)
            ->where('is_paid', 1)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Format chart data for frontend consumption
        $months = [];
        $counts = [];

        foreach ($adsPerMonth as $data) {
            $months[] = Carbon::createFromFormat('Y-m', $data->month)->format('M Y');
            $counts[] = $data->count;
        }

        // ===== RETURN DASHBOARD VIEW WITH ALL DATA =====

        // Pass all collected statistics and analytics to the dashboard view
        return view('admin.pages.forms.home', compact(
            'id',                    // Basic ID (likely for routing)
            'store_id',              // Current store context
            'user_counter',          // User counter data
            'dark_mode',             // User's theme preference

            // User statistics
            'totalUsers',            // Total customer count
            'bannedUsers',           // Banned users count
            'newUsersToday',         // New registrations today
            'activeUsersToday',      // Active users today

            // Store statistics
            'activeStores',          // Active stores count
            'pandingStore',          // Pending store approvals

            // Offer statistics
            'activeOffers',          // Active offers count
            'pendingOffers',         // Pending offer approvals
            'claimedOffers',         // Total claimed offers
            'redeemedOffers',        // Total redeemed offers

            // Advertisement statistics
            'adsRunning',            // Currently running ads
            'pendingAs',             // Pending ad approvals
            'activeAds',             // Active paid ads
            'pendingAds',            // Pending ads
            'withForPay',            // Approved ads awaiting payment
            'expiredAds',            // Expired ads

            // Analytics and charts
            'recentActivities',      // Recent platform activities
            'userGrowth',            // User growth chart data
            'topStores',             // Top performing stores
            'topOffers',             // Top performing offers
            'topCategories',         // Top categories by claims
            'topUsers',              // Most active users

            // Advertisement analytics
            'bannerCount',           // Banner ads count
            'premuimCount',          // Premium ads count
            'popupCount',            // Spotlight ads count
            'months',                // Monthly chart labels
            'counts'                 // Monthly chart data
        ));
    }

    /**
     * Get latest issue reports for dashboard display
     *
     * This method retrieves the 5 most recent issue reports with related
     * offer and reporter information for display in the admin dashboard.
     * It formats the data for easy consumption by frontend components.
     *
     * @return \Illuminate\Http\JsonResponse JSON response containing formatted report data
     */
    public function getLatestReports()
    {
        // Fetch latest 5 reports with relationships
        $latestReports = ReportIssuse::with('reporter', 'offer')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($report) {
                return [
                    'id' => $report->id,
                    'related_id' => $report->offer_id,
                    // Get offer title in current locale, fallback to 'N/A'
                    'offer_name' => $report->offer->{"title_" . app()->getLocale()} ?? 'N/A',
                    // Limit description to 80 characters for display
                    'description' => Str::limit($report->description, 80),
                    // Get reporter username, fallback to 'N/A' if no reporter
                    'user_name' => optional($report->reporter)->username ?? 'N/A',
                    // Human-readable time difference (e.g., "2 hours ago")
                    'created_at' => $report->created_at->diffForHumans(),
                ];
            });

        // Return formatted reports as JSON response
        return response()->json(['reports' => $latestReports]);
    }
}
