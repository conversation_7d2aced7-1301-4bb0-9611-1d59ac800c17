<?php $__env->startSection('title'); ?>
<?php echo e(labels('admin_labels.add_store', 'Add Store')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <h2 class="mb-4"><?php echo e(__('admin_labels.add_store')); ?></h2>
    <?php if($errors->has('error')): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <?php echo e($errors->first('error')); ?>

    </div>
    <?php endif; ?>

    <form action="<?php echo e(route('admin.stores.store')); ?>" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        
        <div class="card mb-4">
            <div class="card-header fw-bold"><?php echo e(__('admin_labels.store_user_data')); ?></div>
            <div class="card-body row g-3">

                
                <div class="col-md-4">
                    <label class="form-label"><?php echo e(__('admin_labels.username')); ?></label>
                    <span class='text-asterisks text-sm'>*</span></label>
                    <input type="text" name="username" value="<?php echo e(old('username')); ?>" class="form-control <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="col-md-4">
                    <label class="form-label"><?php echo e(__('admin_labels.email')); ?></label>
                    <span class='text-asterisks text-sm'>*</span></label>
                    <input type="email" name="email" value="<?php echo e(old('email')); ?>" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="col-md-4">
                    <label class="form-label"><?php echo e(__('admin_labels.mobile')); ?></label>
                    <span class='text-asterisks text-sm'>*</span></label>
                    <input type="text" name="mobile" value="<?php echo e(old('mobile')); ?>" class="form-control <?php $__errorArgs = ['mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php $__errorArgs = ['mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="col-md-6">
                    <label class="form-label"><?php echo e(__('admin_labels.password')); ?></label>
                    <span class='text-asterisks text-sm'>*</span></label>
                    <input type="password" name="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="col-md-6">
                    <label class="form-label"><?php echo e(__('admin_labels.password_confirmation')); ?></label>
                    <span class='text-asterisks text-sm'>*</span></label>
                    <input type="password" name="password_confirmation" class="form-control">
                </div>

            </div>
        </div>

        
        <div class="card mb-4">
            <div class="card-header fw-bold"><?php echo e(__('admin_labels.store_info')); ?></div>
            <div class="card-body row g-3">

                
                <div class="col-md-6">
                    <label class="form-label"><?php echo e(__('admin_labels.name_ar')); ?></label>
                    <span class='text-asterisks text-sm'>*</span></label>
                    <input type="text" name="name_ar" value="<?php echo e(old('name_ar')); ?>" class="form-control <?php $__errorArgs = ['name_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php $__errorArgs = ['name_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="col-md-6">
                    <label class="form-label"><?php echo e(__('admin_labels.name_en')); ?></label>
                    <span class='text-asterisks text-sm'>*</span></label>
                    <input type="text" name="name_en" value="<?php echo e(old('name_en')); ?>" class="form-control <?php $__errorArgs = ['name_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php $__errorArgs = ['name_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="col-md-6">
                    <label class="form-label"><?php echo e(__('admin_labels.description_ar')); ?></label>
                    <textarea name="description_ar" class="form-control"><?php echo e(old('description_ar')); ?></textarea>
                </div>

                
                <div class="col-md-6">
                    <label class="form-label"><?php echo e(__('admin_labels.description_en')); ?></label>
                    <textarea name="description_en" class="form-control"><?php echo e(old('description_en')); ?></textarea>
                </div>
            </div>
        </div>

        
        <div class="card mb-4">
            <div class="card-header fw-bold"><?php echo e(__('admin_labels.store_media')); ?></div>
            <div class="card-body row g-3">
                
                <div class="col-md-6">
                    <label class="form-label"><?php echo e(__('admin_labels.logo')); ?></label>
                    <span class='text-asterisks text-sm'>*</span></label>
                    <input type="file" name="logo" class="form-control" accept="image/*">
                    <?php $__errorArgs = ['logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="text-danger small mt-1"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="col-md-6">
                    <label class="form-label"><?php echo e(__('admin_labels.banner')); ?></label>
                    <input type="file" name="cover" class="form-control" accept="image/*">
                    <?php $__errorArgs = ['cover'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="text-danger small mt-1"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

            </div>
        </div>

        
        <div class="card mb-4">
            <div class="card-header fw-bold"><?php echo e(__('admin_labels.colors')); ?></div>
            <div class="card-body row g-3">
                <?php $__currentLoopData = ['primary', 'secondary', 'hover', 'active', 'background']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $color): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-4">
                    <label class="form-label"><?php echo e(__('admin_labels.' . $color . '_color')); ?></label>
                    <input type="color" name="<?php echo e($color); ?>_color" value="<?php echo e(old($color . '_color', '#ffffff')); ?>" class="form-control form-control-color">
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        
        <div class="mb-4">
            <label class="form-label fw-bold"><?php echo e(__('admin_labels.status')); ?></label>
            <select name="is_active" class="form-select">
                <option value="1" <?php echo e(old('is_active') == '1' ? 'selected' : ''); ?>><?php echo e(__('admin_labels.active')); ?></option>
                <option value="0" <?php echo e(old('is_active') == '0' ? 'selected' : ''); ?>><?php echo e(__('admin_labels.deactive')); ?></option>
            </select>
        </div>

        
        <div class="d-flex justify-content-start gap-3">
            <button type="submit" class="btn btn-primary px-4"><?php echo e(__('admin_labels.add')); ?></button>
            <a href="<?php echo e(route('admin.stores.manage_store')); ?>" class="btn btn-outline-secondary"><?php echo e(__('admin_labels.cancel')); ?></a>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/pages/forms/stores.blade.php ENDPATH**/ ?>