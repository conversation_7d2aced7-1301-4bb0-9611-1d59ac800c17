{"__meta": {"id": "X248683c7fbcbeb5f8b51f563c6adb48a", "datetime": "2025-06-28 18:32:42", "utime": **********.781223, "method": "POST", "uri": "/seller/offers", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[18:32:42] LOG.info: req [\n    {\n        \"store_id\": \"26\",\n        \"_token\": \"ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz\",\n        \"pro_input_name_ar\": \"saa\",\n        \"pro_input_name_en\": \"asas\",\n        \"description_ar\": \"asa\",\n        \"description_en\": \"asas\",\n        \"tags\": null,\n        \"short_description\": \"asas\",\n        \"pro_input_price_before_discount\": \"11\",\n        \"pro_input_discounted_price\": \"1\",\n        \"expire_date\": \"2025-07-05T20:32\",\n        \"all_branches\": \"on\",\n        \"add_code\": \"on\",\n        \"code_count\": \"11\",\n        \"category_id\": \"71,77\",\n        \"main_image\": {},\n        \"other_images\": [\n            {},\n            {}\n        ]\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.587524, "xdebug_link": null, "collector": "log"}, {"message": "[18:32:42] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 87", "message_html": null, "is_string": false, "label": "warning", "time": **********.595615, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751135560.138502, "end": **********.78127, "duration": 2.642768144607544, "duration_str": "2.64s", "measures": [{"label": "Booting", "start": 1751135560.138502, "relative_start": 0, "end": **********.416089, "relative_end": **********.416089, "duration": 2.2775871753692627, "duration_str": "2.28s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.416117, "relative_start": 2.2776150703430176, "end": **********.781275, "relative_end": 5.0067901611328125e-06, "duration": 0.3651580810546875, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 34795680, "peak_usage_str": "33MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST seller/offers", "middleware": "web, CheckInstallation, auth, role:seller", "controller": "App\\Http\\Controllers\\Seller\\OfferController@store", "namespace": null, "prefix": "", "where": [], "as": "seller_products.store", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=63\" onclick=\"\">app/Http/Controllers/Seller/OfferController.php:63-184</a>"}, "queries": {"nb_statements": 12, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.035870000000000006, "accumulated_duration_str": "35.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.506218, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 8.168}, {"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.52318, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 8.168, "width_percent": 3.847}, {"sql": "select * from `users` where `users`.`id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.530767, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 12.016, "width_percent": 3.067}, {"sql": "select * from `roles` where `roles`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.541327, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 15.082, "width_percent": 2.202}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.595284, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "OfferController.php:73", "source": {"index": 10, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=73", "ajax": false, "filename": "OfferController.php", "line": "73"}, "connection": "eshop", "explain": null, "start_percent": 17.285, "width_percent": 0}, {"sql": "select * from `settings` where `variable` = 'system_settings' limit 1", "type": "query", "params": [], "bindings": ["system_settings"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.617013, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:609", "source": {"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=609", "ajax": false, "filename": "function_helper.php", "line": "609"}, "connection": "eshop", "explain": null, "start_percent": 17.285, "width_percent": 3.485}, {"sql": "select exists(select * from `offers` where `slug` = 'asas') as `exists`", "type": "query", "params": [], "bindings": ["asas"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 509}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6260061, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:509", "source": {"index": 10, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 509}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=509", "ajax": false, "filename": "function_helper.php", "line": "509"}, "connection": "eshop", "explain": null, "start_percent": 20.769, "width_percent": 2.704}, {"sql": "insert into `offers` (`title_ar`, `title_en`, `short_description`, `slug`, `tags`, `store_id`, `price`, `discounted_price`, `is_for_all_branches`, `is_active`, `is_approved`, `description_ar`, `description_en`, `expire_date`, `updated_at`, `created_at`) values ('saa', 'asas', 'asas', 'asas', '', '26', '11', '1', 1, 1, 1, 'asa', 'asas', '2025-07-05 20:32:00', '2025-06-28 18:32:42', '2025-06-28 18:32:42')", "type": "query", "params": [], "bindings": ["saa", "asas", "asas", "asas", "", "26", "11", "1", 1, 1, 1, "asa", "asas", "2025-07-05 20:32:00", "2025-06-28 18:32:42", "2025-06-28 18:32:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 114}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6400151, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:114", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=114", "ajax": false, "filename": "OfferController.php", "line": "114"}, "connection": "eshop", "explain": null, "start_percent": 23.474, "width_percent": 6.747}, {"sql": "insert into `offer_images` (`offer_id`, `image_url`, `is_main`, `updated_at`, `created_at`) values (96, 'offers/J11viuD8YA0qhPegN6fLe8l17qty4Ac1QD3Jau8Y.png', 1, '2025-06-28 18:32:42', '2025-06-28 18:32:42')", "type": "query", "params": [], "bindings": [96, "offers/J11viuD8YA0qhPegN6fLe8l17qty4Ac1QD3Jau8Y.png", 1, "2025-06-28 18:32:42", "2025-06-28 18:32:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 354}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.647022, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:354", "source": {"index": 21, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 354}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=354", "ajax": false, "filename": "function_helper.php", "line": "354"}, "connection": "eshop", "explain": null, "start_percent": 30.22, "width_percent": 5.102}, {"sql": "insert into `offer_images` (`created_at`, `image_url`, `is_main`, `offer_id`, `updated_at`) values ('2025-06-28 18:32:42', 'offers/OFIwiio6vYhzR0oIaLrhR9GJ0trj6PaRfnOW5AC7.png', 0, 96, '2025-06-28 18:32:42'), ('2025-06-28 18:32:42', 'offers/WVgyoZpewcJlY3PImgHJB2H806KtrtisbPfL1Csv.png', 0, 96, '2025-06-28 18:32:42')", "type": "query", "params": [], "bindings": ["2025-06-28 18:32:42", "offers/OFIwiio6vYhzR0oIaLrhR9GJ0trj6PaRfnOW5AC7.png", 0, 96, "2025-06-28 18:32:42", "2025-06-28 18:32:42", "offers/WVgyoZpewcJlY3PImgHJB2H806KtrtisbPfL1Csv.png", 0, 96, "2025-06-28 18:32:42"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 373}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.653228, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:373", "source": {"index": 14, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 373}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=373", "ajax": false, "filename": "function_helper.php", "line": "373"}, "connection": "eshop", "explain": null, "start_percent": 35.322, "width_percent": 4.488}, {"sql": "insert into `offer_categories` (`category_id`, `offer_id`) values (71, 96), (77, 96)", "type": "query", "params": [], "bindings": [71, 96, 77, 96], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 141}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.664607, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:141", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 141}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=141", "ajax": false, "filename": "OfferController.php", "line": "141"}, "connection": "eshop", "explain": null, "start_percent": 39.81, "width_percent": 10.873}, {"sql": "select `categories`.*, `offer_categories`.`offer_id` as `pivot_offer_id`, `offer_categories`.`category_id` as `pivot_category_id` from `categories` inner join `offer_categories` on `categories`.`id` = `offer_categories`.`category_id` where `offer_categories`.`offer_id` = 96", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 159}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6741102, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:159", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=159", "ajax": false, "filename": "OfferController.php", "line": "159"}, "connection": "eshop", "explain": null, "start_percent": 50.683, "width_percent": 3.68}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"68ff27bb-9594-481c-b31f-fb2701811101\\\",\\\"displayName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\SendOfferNotifications\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\\\\\Jobs\\\\\\\\SendOfferNotifications\\\",\\\"command\\\":\\\"O:31:\\\\\\\"App\\\\\\\\Jobs\\\\\\\\SendOfferNotifications\\\\\\\":1:{s:8:\\\\\\\"\\\\u0000*\\\\u0000offer\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:24:\\\\\\\"App\\\\\\\\Models\\\\\\\\awfarly\\\\\\\\Offer\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:96;s:9:\\\\\\\"relations\\\\\\\";a:1:{i:0;s:10:\\\\\\\"categories\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"68ff27bb-9594-481c-b31f-fb2701811101\",\"displayName\":\"App\\\\Jobs\\\\SendOfferNotifications\",\"job\":\"Illuminate\\\\Queue\\\\CallQueued<PERSON><PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendOfferNotifications\",\"command\":\"O:31:\\\"App\\\\Jobs\\\\SendOfferNotifications\\\":1:{s:8:\\\"\\u0000*\\u0000offer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\awfarly\\\\Offer\\\";s:2:\\\"id\\\";i:96;s:9:\\\"relations\\\";a:1:{i:0;s:10:\\\"categories\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 254}], "start": **********.724562, "duration": 0.016370000000000003, "duration_str": "16.37ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "eshop", "explain": null, "start_percent": 54.363, "width_percent": 45.637}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.757259, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "OfferController.php:73", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=73", "ajax": false, "filename": "OfferController.php", "line": "73"}, "connection": "eshop", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\awfarly\\Category": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Setting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/seller/offers\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "store_id": "26", "store_name": "<PERSON><PERSON>", "store_image": "stores/1750281403_logo.png", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "403", "system_settings": "{\"app_name\":\"\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a\",\"support_number\":\"919876543210\",\"support_email\":\"<EMAIL>\",\"logo\":\"media\\/1751134451_logo.png\",\"favicon\":null,\"storage_type\":\"local\",\"current_version_of_android_app\":\"1.1.0\",\"current_version_of_ios_app\":\"1.0.0\",\"version_system_status\":0,\"expand_product_image\":0,\"customer_app_maintenance_status\":1,\"offer_request_status\":0,\"message_for_customer_app\":\"sdsa\",\"sidebar_color\":null,\"sidebar_type\":null,\"navbar_fixed\":0,\"theme_mode\":0,\"currency_setting\":\"\\u062f.\\u0644\"}"}, "request": {"path_info": "/seller/offers", "status_code": "<pre class=sf-dump id=sf-dump-1209077479 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1209077479\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1705520078 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1705520078\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1545993341 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>store_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">26</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>pro_input_name_ar</span>\" => \"<span class=sf-dump-str title=\"3 characters\">saa</span>\"\n  \"<span class=sf-dump-key>pro_input_name_en</span>\" => \"<span class=sf-dump-str title=\"4 characters\">asas</span>\"\n  \"<span class=sf-dump-key>description_ar</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asa</span>\"\n  \"<span class=sf-dump-key>description_en</span>\" => \"<span class=sf-dump-str title=\"4 characters\">asas</span>\"\n  \"<span class=sf-dump-key>tags</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">asas</span>\"\n  \"<span class=sf-dump-key>pro_input_price_before_discount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  \"<span class=sf-dump-key>pro_input_discounted_price</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"16 characters\">2025-07-05T20:32</span>\"\n  \"<span class=sf-dump-key>all_branches</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>add_code</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>code_count</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">71,77</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1545993341\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1948265986 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">425234</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryof7JRvq577Bqm3A5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/seller/offers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlJhQlc2NlpBbUVNQ1JBZVdVQUJqb0E9PSIsInZhbHVlIjoiRVhCNUN2OWJSNGg1SklvM3VNdFdpbWhOd0dHbHh4aUhKYzAxZlZKVkU5TldNRlM2MWNVRVE2RlZXTXpTVWgwcngvUGZ3QjFadkpLcXdaOU5RM1B6RVgxZDlHUThGOXEzc2I0QkgrU3MxdWFZbGdTSmo4QjMxcVRqV21TMkdvYUgiLCJtYWMiOiI2Y2M5NDAzYjMyYThlZWJmM2VmMjM4ZjBjMmRlMDQ2NmZjMzkyYzM5Njg5YWExNzBmZjQ3ZDQxN2ZiMDFhODE2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjFtRk1kRkFUaEh4M2pCTnQvc3hvdlE9PSIsInZhbHVlIjoiTnhrelU5ZEtGNGdrTzh6YkpGYWpLWWJpSHllMjBiZHNiTVV6R3c2T25kNXU2TzhnTnN0RE1hVU1KTkZzemt0OEJpajJna1RkT0tRS3YzQlpjSlFKbXV4bGVYMlNyNGsvcHFLYVFaZURjbWtIeHcwZlZPNXhxd1lObXZUTFFJZU4iLCJtYWMiOiIxODhjMGRjZGM1ZmNmOGRjYzhmZmE4MDVjNWE1ZTEwYTk3OWU2MWE2YjFjMGQ1N2ViMGM1MThlMzA4NTFjNmVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948265986\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1960220851 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mVUiKCS25MLlribzCWoFa9ZEN0oL6qkA0JoxpOT0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960220851\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1308172940 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 18:32:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImRJcjd3MGJHakVET09ScjRqL2pWUXc9PSIsInZhbHVlIjoiWE1DTUJ4MVd0NituNlc2OWluWUYwSjRDbGFySk5nQUw4VUthZXVZNGtFbkZRRUV6Z0E1WW1rZlNTZktFUUZFMEtGMUJmOGgrdTcybUsxZFZTYmp6TitXOEpTaStoTFVoTTc1clBlNjZ1bThRTytSdWRGcGxMMWFDTW50MnRQTDYiLCJtYWMiOiI0YTBjMGMyNTdmOGU4OTE2ZDk0ODY0MGQwNTEzMWMyMmFiMjEzOTIxZmM1YzdiMTAyMDllMjZhNDQ5ODlmZWU3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:32:42 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlV3dTVRVFY3cUp2NkNRQ3oxU1hLWnc9PSIsInZhbHVlIjoiWEZ0cElJWUZvQ0wrS2wvMzA2Wjc0N1FPWGU5UG5xMEM3djh3d2h2OWg2TDM5bWFaRnFBN1ZTVU5zT0JtUERaMzl3QUVnQU0zazhnbytIYUgzSjFLUitsRnNVNW40b0JTTTB4OE9KOGJQYkpCRmRWMElZU3ZGN2ZQcVYvZjFUOS8iLCJtYWMiOiJkYjhlODkwYjFhNmUxYTcyNzM2YzI1NGEyODU3YmE5NzM2ODQ4YzAzNDUzZTE0ZWU0ZjU3MDgwYTdiZDJhMTUyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:32:42 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImRJcjd3MGJHakVET09ScjRqL2pWUXc9PSIsInZhbHVlIjoiWE1DTUJ4MVd0NituNlc2OWluWUYwSjRDbGFySk5nQUw4VUthZXVZNGtFbkZRRUV6Z0E1WW1rZlNTZktFUUZFMEtGMUJmOGgrdTcybUsxZFZTYmp6TitXOEpTaStoTFVoTTc1clBlNjZ1bThRTytSdWRGcGxMMWFDTW50MnRQTDYiLCJtYWMiOiI0YTBjMGMyNTdmOGU4OTE2ZDk0ODY0MGQwNTEzMWMyMmFiMjEzOTIxZmM1YzdiMTAyMDllMjZhNDQ5ODlmZWU3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:32:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlV3dTVRVFY3cUp2NkNRQ3oxU1hLWnc9PSIsInZhbHVlIjoiWEZ0cElJWUZvQ0wrS2wvMzA2Wjc0N1FPWGU5UG5xMEM3djh3d2h2OWg2TDM5bWFaRnFBN1ZTVU5zT0JtUERaMzl3QUVnQU0zazhnbytIYUgzSjFLUitsRnNVNW40b0JTTTB4OE9KOGJQYkpCRmRWMElZU3ZGN2ZQcVYvZjFUOS8iLCJtYWMiOiJkYjhlODkwYjFhNmUxYTcyNzM2YzI1NGEyODU3YmE5NzM2ODQ4YzAzNDUzZTE0ZWU0ZjU3MDgwYTdiZDJhMTUyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:32:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308172940\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-95704213 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/seller/offers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>26</span>\n  \"<span class=sf-dump-key>store_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">bothina</span>\"\n  \"<span class=sf-dump-key>store_image</span>\" => \"<span class=sf-dump-str title=\"26 characters\">stores/1750281403_logo.png</span>\"\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>403</span>\n  \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"528 characters\">{&quot;app_name&quot;:&quot;\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:&quot;media\\/1751134451_logo.png&quot;,&quot;favicon&quot;:null,&quot;storage_type&quot;:&quot;local&quot;,&quot;current_version_of_android_app&quot;:&quot;1.1.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;version_system_status&quot;:0,&quot;expand_product_image&quot;:0,&quot;customer_app_maintenance_status&quot;:1,&quot;offer_request_status&quot;:0,&quot;message_for_customer_app&quot;:&quot;sdsa&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0,&quot;currency_setting&quot;:&quot;\\u062f.\\u0644&quot;}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95704213\", {\"maxDepth\":0})</script>\n"}}