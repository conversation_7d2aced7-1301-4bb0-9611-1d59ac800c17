<?php

namespace App\Http\Controllers\Store_employee;

use App\Enums\CodeStatus;
use App\Enums\NotificationSendTo;
use App\Enums\NotificationType;
use App\Enums\ScanStatus;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationJob;
use App\Models\Notification;
use App\Models\Offer;
use App\Models\OfferClaim;
use App\Models\OfferCode;
use App\Models\OfferScan;
use App\Models\StoreUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * HomeController for Store Employee/Delivery Boy Dashboard
 *
 * This controller handles the main dashboard functionality for store employees
 * and delivery personnel, including:
 * - Dashboard display with recent scan history
 * - QR code scanning for offer redemption
 * - Offer validation and redemption processing
 * - Push notifications for successful redemptions
 * - Scan logging and error handling
 */
class HomeController extends Controller
{
    /**
     * Display the store employee dashboard
     *
     * Shows the main dashboard with:
     * - User information (store employee details)
     * - Recent QR code scans from today
     * - Test notification functionality (development/testing)
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Test notification functionality (should be removed in production)
        $offer = Offer::find(4);
        $title = 'تم مسح رمز QR '; // Arabic: "QR code scanned"
        $message = "تم مسح العرض '{$offer->title_ar}'."; // Arabic: "Offer scanned"
        $tokens = get_users_with_fcm_tokens([10])->pluck('fcm_token')->toArray();
        Log::info('tokens', [$tokens]);

        // Dispatch push notification job for testing
        PushNotificationJob::dispatch($tokens, $title, $message, [
            'offer_id' => 1,
        ]);

        // Get current authenticated store employee with relationships
        $user = StoreUser::with(['user', 'store', 'branch'])
            ->where('user_id', auth()->id())
            ->first();

        // Get today's scan history for the current user
        $recentScans = OfferScan::where('user_id', auth()->id())
            ->whereDate('created_at', today())
            ->orderBy('created_at', 'desc')
            ->get();

        return view('store_employee.pages.forms.dashboard', compact('recentScans', 'user'));
    }
    /**
     * Process QR code scanning for offer redemption
     *
     * This method handles the complete QR code scanning workflow:
     * 1. Validates the QR code format and extracts user/code information
     * 2. Finds and validates the offer code and associated claim
     * 3. Checks offer validity (expiration, active status, redemption status)
     * 4. Processes the redemption (updates usage count, marks as used if needed)
     * 5. Sends push notification to the user
     * 6. Logs the scan attempt for audit purposes
     *
     * Expected QR code format: "USER:{user_id}-CODE:{code_value}"
     *
     * @param Request $request Contains 'code' field with QR code data
     * @return \Illuminate\Http\JsonResponse Success or error response
     */
    public function scan(Request $request)
    {
        // Start database transaction to ensure data consistency
        DB::beginTransaction();

        try {
            // Validate incoming request data
            $validated = $request->validate(['code' => 'required|string']);
            $code = $validated['code'];

            // Parse QR code format: USER:{user_id}-CODE:{code_value}
            if (!preg_match('/USER:(\d+)-CODE:(\w+)/', $code, $matches)) {
                return $this->logAndRespond(
                    __('admin_labels.invalid_qr_code_format'),
                    400,
                    ScanStatus::INVALID
                );
            }

            // Extract user ID and code value from QR code
            [$userId, $codeValue] = [$matches[1], $matches[2]];
            $employeeId = auth()->id();
            $storeId = $this->getEmployeeStoreId($employeeId);

            // Find offer code with all necessary relationships loaded
            // Only includes offers belonging to the employee's store
            $offerCode = OfferCode::with([
                'offer' => fn($q) => $q->where('store_id', $storeId),
                'claims' => fn($q) => $q->with([
                    'offer.images' => fn($q) => $q->where('is_main', 1)
                ])
            ])->where('code', $codeValue)->first();

            // Check if offer code exists and belongs to this store
            if (!$offerCode) {
                return $this->logAndRespond(
                    __('admin_labels.invalid_qr_code'),
                    404,
                    ScanStatus::INVALID
                );
            }

            // Get the claim and offer from the loaded relationships
            $claim = $offerCode->claims->first();
            $offer = $offerCode->offer;

            // Validate offer conditions (expiration, active status, redemption status)
            if ($validationError = $this->validateOffer($offer, $claim)) {
                return $validationError;
            }

            // Process the redemption (update usage count, mark as used if needed)
            $this->processRedemption($offerCode, $claim);

            // Send push notification to the user about successful redemption
            $this->sendOfferRedeemedNotification($offer, $userId);

            // Commit the transaction if everything succeeded
            DB::commit();

            // Return success response with offer data
            return response()->json([
                'message' => __('admin_labels.offer_redeemed_successfully'),
                'data' => $this->formatOfferData($offer)
            ]);

        } catch (\Exception $e) {
            // Rollback transaction on any error
            DB::rollBack();
            Log::error("QR Scan Error: {$e->getMessage()}", ['trace' => $e->getTrace()]);
            return response()->json(['error' => __('admin_labels.server_error')], 500);
        }
    }
    /**
     * Validate offer conditions before allowing redemption
     *
     * Performs comprehensive validation checks on the offer and claim:
     * - Ensures claim exists for the offer code
     * - Checks if offer has expired
     * - Verifies offer is currently active
     * - Confirms offer hasn't already been redeemed
     *
     * @param Offer $offer The offer to validate
     * @param OfferClaim|null $claim The associated claim to validate
     * @return JsonResponse|null Returns error response if validation fails, null if valid
     */
    protected function validateOffer($offer, $claim): ?JsonResponse
    {
        // Check if claim exists for this offer code
        if (!$claim) {
            return $this->logAndRespond(
                __('admin_labels.no_claim_found'),
                404,
                ScanStatus::INVALID
            );
        }

        // Check if offer has expired
        if ($offer->expire_date < now()) {
            return $this->logAndRespond(
                __('admin_labels.offer_expired'),
                400,
                ScanStatus::EXPIRED
            );
        }

        // Check if offer is currently active
        if (!$offer->is_active) {
            return $this->logAndRespond(
                __('admin_labels.offer_not_active'),
                400,
                ScanStatus::INVALID
            );
        }

        // Check if offer has already been redeemed
        if ($claim->is_redeemed) {
            return $this->logAndRespond(
                __('admin_labels.offer_already_redeemed'),
                400,
                ScanStatus::USED
            );
        }

        // All validations passed
        return null;
    }

    /**
     * Process the offer redemption logic
     *
     * Handles the core redemption workflow:
     * - Increments the usage count for the offer code
     * - Marks claim as redeemed if usage limit is reached
     * - Updates offer code status to USED if fully utilized
     * - Creates a scan record for audit trail
     *
     * @param OfferCode $offerCode The offer code being redeemed
     * @param OfferClaim $claim The associated claim being processed
     * @return void
     */
    protected function processRedemption(OfferCode $offerCode, OfferClaim $claim): void
    {
        // Increment the usage count for this offer code
        $offerCode->used_count += 1;

        // Check if usage limit has been reached
        if ($offerCode->used_count >= $offerCode->usage_limit) {
            $claim->is_redeemed = true;           // Mark claim as fully redeemed
            $offerCode->status = CodeStatus::USED; // Mark code as used up
        }

        // Save the updated offer code and claim
        $offerCode->save();
        $claim->save();

        // Create audit record of the successful scan
        OfferScan::create([
            'user_id' => auth()->id(),        // Store employee who scanned
            'code' => $offerCode->code,       // The scanned code
            'status' => ScanStatus::SUCCESS,  // Successful scan status
            'ip' => request()->ip(),          // IP address for security tracking
        ]);
    }
    /**
     * Send push notification and create database notification for redeemed offer
     *
     * This method handles both push notification delivery and database notification
     * creation when an offer is successfully redeemed:
     * - Sends push notification to user's device via FCM
     * - Creates a database notification record for history
     *
     * @param Offer $offer The offer that was redeemed
     * @param int $userId The ID of the user who redeemed the offer
     * @return void
     */
    protected function sendOfferRedeemedNotification($offer, $userId): void
    {
        // Prepare notification content
        $title = __('notifications.offer_redeemed_title');
        $message = __('notifications.offer_redeemed_message', ['offer' => $offer->title_ar]);

        // Get user's FCM tokens for push notification
        $tokens = get_users_with_fcm_tokens([$userId])->pluck('fcm_token')->toArray();

        // Dispatch push notification job to queue
        PushNotificationJob::dispatch($tokens, $title, $message, [
            'offer_id' => $offer->id,
        ]);

        // Create database notification record for history
        Notification::create([
            'title' => $title,
            'message' => $message,
            'type' => NotificationType::OFFER,      // Notification type
            'type_id' => $offer->id,                // Related offer ID
            'send_to' => NotificationSendTo::SPECIFIC, // Send to specific user
            'users_id' => $userId,                  // Target user ID
        ]);
    }

    /**
     * Format offer data for API response
     *
     * Transforms offer model data into a standardized format for API responses,
     * including essential offer information and properly formatted image URLs.
     *
     * @param Offer $offer The offer to format
     * @return array Formatted offer data
     */
    protected function formatOfferData($offer): array
    {
        return [
            'offer_id' => $offer->id,
            'name' => $offer->title_ar,                                                    // Arabic title
            'image' => getMediaImageUrl($offer->images->first()->image_url ?? null),      // Main image URL
            'price' => $offer->price,                                                      // Original price
            'discounted_price' => $offer->discounted_price,                               // Discounted price
        ];
    }

    /**
     * Get the store ID for a given employee
     *
     * Retrieves the store ID associated with the specified employee/user ID.
     * This is used to ensure employees can only scan codes for their own store's offers.
     *
     * @param int $employeeId The employee's user ID
     * @return int|null The store ID or null if not found
     */
    protected function getEmployeeStoreId($employeeId): ?int
    {
        return StoreUser::where('user_id', $employeeId)->value('store_id');
    }

    /**
     * Log scan attempt and return standardized error response
     *
     * This method serves dual purposes:
     * 1. Creates an audit record of the scan attempt (successful or failed)
     * 2. Returns a standardized JSON error response
     *
     * All scan attempts are logged for security and audit purposes.
     *
     * @param string $error The error message to return
     * @param int $status HTTP status code
     * @param ScanStatus $scanStatus The scan status enum value
     * @return JsonResponse Standardized error response
     */
    protected function logAndRespond(string $error, int $status, $scanStatus): JsonResponse
    {
        // Create audit record of the scan attempt
        OfferScan::create([
            'user_id' => auth()->id(),           // Employee who attempted the scan
            'code' => request()->input('code'),  // The scanned code (for audit)
            'status' => $scanStatus,             // Status of the scan attempt
            'ip' => request()->ip(),             // IP address for security tracking
        ]);

        // Return standardized error response
        return response()->json(['error' => $error], $status);
    }
}
