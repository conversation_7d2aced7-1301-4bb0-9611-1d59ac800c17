<?php

namespace App\Http\Controllers\Store_employee;

use App\Enums\CodeStatus;
use App\Enums\NotificationSendTo;
use App\Enums\NotificationType;
use App\Enums\ScanStatus;
use App\Http\Controllers\Controller;
use App\Jobs\PushNotificationJob;
use App\Models\Notification;
use App\Models\Offer;
use App\Models\OfferClaim;
use App\Models\OfferCode;
use App\Models\OfferScan;
use App\Models\StoreUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class HomeController extends Controller
{

    public function index()
    {
        $offer = Offer::find(4);
        $title = 'تم مسح رمز QR ';
        $message = "تم مسح العرض '{$offer->title_ar}'.";
        $tokens = get_users_with_fcm_tokens([10])->pluck('fcm_token')->toArray();
        Log::info('tokens', [$tokens]);
        PushNotificationJob::dispatch($tokens, $title, $message, [
            'offer_id' => 1,
        ]);
        $user = StoreUser::with(['user', 'store', 'branch'])->where('user_id', auth()->id())->first();
        $recentScans = OfferScan::where('user_id', auth()->id())
            ->whereDate('created_at', today())
            ->orderBy('created_at', 'desc')
            ->get();

        return view('store_employee.pages.forms.dashboard', compact('recentScans', 'user'));
    }
    public function scan(Request $request)
    {
        DB::beginTransaction();
        
        try {
            $validated = $request->validate(['code' => 'required|string']);
            $code = $validated['code'];
            
            // Extract user and code from QR
            if (!preg_match('/USER:(\d+)-CODE:(\w+)/', $code, $matches)) {
                return $this->logAndRespond(
                    __('admin_labels.invalid_qr_code_format'),
                    400,
                    ScanStatus::INVALID
                );
            }
    
            [$userId, $codeValue] = [$matches[1], $matches[2]];
            $employeeId = auth()->id();
            $storeId = $this->getEmployeeStoreId($employeeId);
    
            // Find and validate offer code with eager loading
            $offerCode = OfferCode::with([
                'offer' => fn($q) => $q->where('store_id', $storeId),
                'claims' => fn($q) => $q->with([
                    'offer.images' => fn($q) => $q->where('is_main', 1)
                ])
            ])->where('code', $codeValue)->first();
    
            if (!$offerCode) {
                return $this->logAndRespond(
                    __('admin_labels.invalid_qr_code'),
                    404,
                    ScanStatus::INVALID
                );
            }
    
            $claim = $offerCode->claims->first();
            $offer = $offerCode->offer;
    
            // Validate offer status
            if ($validationError = $this->validateOffer($offer, $claim)) {
                return $validationError;
            }
    
            // Process redemption
            $this->processRedemption($offerCode, $claim);
    
            // Send notification
            $this->sendOfferRedeemedNotification($offer, $userId);
    
            DB::commit();
    
            return response()->json([
                'message' => __('admin_labels.offer_redeemed_successfully'),
                'data' => $this->formatOfferData($offer)
            ]);
    
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("QR Scan Error: {$e->getMessage()}", ['trace' => $e->getTrace()]);
            return response()->json(['error' => __('admin_labels.server_error')], 500);
        }
    }
    
    /**
     * Helper method to validate offer conditions
     */
    protected function validateOffer($offer, $claim): ?JsonResponse
    {
        if (!$claim) {
            return $this->logAndRespond(
                __('admin_labels.no_claim_found'),
                404,
                ScanStatus::INVALID
            );
        }
    
        if ($offer->expire_date < now()) {
            return $this->logAndRespond(
                __('admin_labels.offer_expired'),
                400,
                ScanStatus::EXPIRED
            );
        }
    
        if (!$offer->is_active) {
            return $this->logAndRespond(
                __('admin_labels.offer_not_active'),
                400,
                ScanStatus::INVALID
            );
        }
    
        if ($claim->is_redeemed) {
            return $this->logAndRespond(
                __('admin_labels.offer_already_redeemed'),
                400,
                ScanStatus::USED
            );
        }
    
        return null;
    }
    
    /**
     * Process the redemption logic
     */
    protected function processRedemption(OfferCode $offerCode, OfferClaim $claim): void
    {
        $offerCode->used_count += 1;
        
        if ($offerCode->used_count >= $offerCode->usage_limit) {
            $claim->is_redeemed = true;
            $offerCode->status = CodeStatus::USED;
        }
    
        $offerCode->save();
        $claim->save();
    
        OfferScan::create([
            'user_id' => auth()->id(),
            'code' => $offerCode->code,
            'status' => ScanStatus::SUCCESS,
            'ip' => request()->ip(),
        ]);
    }
    
    /**
     * Send notification about redeemed offer
     */
    protected function sendOfferRedeemedNotification($offer, $userId): void
    {
        $title = __('notifications.offer_redeemed_title');
        $message = __('notifications.offer_redeemed_message', ['offer' => $offer->title_ar]);
        
        $tokens = get_users_with_fcm_tokens([$userId])->pluck('fcm_token')->toArray();
        
        PushNotificationJob::dispatch($tokens, $title, $message, [
            'offer_id' => $offer->id,
        ]);
    
        Notification::create([
            'title' => $title,
            'message' => $message,
            'type' => NotificationType::OFFER,
            'type_id' => $offer->id,
            'send_to' => NotificationSendTo::SPECIFIC,
            'users_id' => $userId,
        ]);
    }
    
    /**
     * Format offer data for response
     */
    protected function formatOfferData($offer): array
    {
        return [
            'offer_id' => $offer->id,
            'name' => $offer->title_ar,
            'image' => getMediaImageUrl($offer->images->first()->image_url ?? null),
            'price' => $offer->price,
            'discounted_price' => $offer->discounted_price,
        ];
    }
    
    /**
     * Get employee's store ID
     */
    protected function getEmployeeStoreId($employeeId): ?int
    {
        return StoreUser::where('user_id', $employeeId)->value('store_id');
    }
    
    /**
     * Log scan attempt and return error response
     */
    protected function logAndRespond(string $error, int $status,  $scanStatus): JsonResponse
    {
        OfferScan::create([
            'user_id' => auth()->id(),
            'code' => request()->input('code'),
            'status' => $scanStatus,
            'ip' => request()->ip(),
        ]);
    
        return response()->json(['error' => $error], $status);
    }
}
