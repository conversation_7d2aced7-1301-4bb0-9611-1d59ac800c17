{"__meta": {"id": "Xb89efbd1a2cf45c81d7544cc39e7b0b8", "datetime": "2025-06-28 18:34:50", "utime": 1751135690.000998, "method": "PUT", "uri": "/seller/offers/update/96", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 7, "messages": [{"message": "[18:34:49] LOG.info: Offer Update Request [\n    {\n        \"_token\": \"ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz\",\n        \"_method\": \"PUT\",\n        \"pro_input_name_ar\": \"saa\",\n        \"pro_input_name_en\": \"asas\",\n        \"description_ar\": \"asa\",\n        \"description_en\": \"asas\",\n        \"tags\": null,\n        \"pro_input_price_before_discount\": \"11.00\",\n        \"pro_input_discounted_price\": \"1.00\",\n        \"expire_date\": \"2025-07-05T20:32\",\n        \"branches\": [\n            \"31\"\n        ],\n        \"code_count\": \"011\",\n        \"pro_input_image\": null,\n        \"other_images\": [\n            \"138\",\n            \"139\"\n        ]\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.875327, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:49] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 400", "message_html": null, "is_string": false, "label": "warning", "time": **********.893199, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:49] LOG.info: code count [\n    \"011\"\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.933678, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:49] LOG.info: from string", "message_html": null, "is_string": false, "label": "info", "time": **********.934028, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:49] LOG.info: branch id [\n    \"31\"\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.93431, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:49] LOG.info: available [\n    0\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.944974, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:49] LOG.info: to create [\n    11\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.945203, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751135687.90459, "end": 1751135690.001043, "duration": 2.0964531898498535, "duration_str": "2.1s", "measures": [{"label": "Booting", "start": 1751135687.90459, "relative_start": 0, "end": **********.745363, "relative_end": **********.745363, "duration": 1.840773105621338, "duration_str": "1.84s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.745374, "relative_start": 1.8407840728759766, "end": 1751135690.001046, "relative_end": 2.86102294921875e-06, "duration": 0.25567197799682617, "duration_str": "256ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31487528, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT seller/offers/update/{id}", "middleware": "web, CheckInstallation, auth, role:seller", "controller": "App\\Http\\Controllers\\Seller\\OfferController@update", "namespace": null, "prefix": "", "where": [], "as": "seller.products.update", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=387\" onclick=\"\">app/Http/Controllers/Seller/OfferController.php:387-512</a>"}, "queries": {"nb_statements": 13, "nb_visible_statements": 15, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02711, "accumulated_duration_str": "27.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.806505, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 5.349}, {"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.82074, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 5.349, "width_percent": 4.722}, {"sql": "select * from `users` where `users`.`id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.828508, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 10.07, "width_percent": 2.213}, {"sql": "select * from `roles` where `roles`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.8360848, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 12.283, "width_percent": 4.058}, {"sql": "select * from `offers` where `offers`.`id` = '96' limit 1", "type": "query", "params": [], "bindings": ["96"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 392}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.877214, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "OfferController.php:392", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 392}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=392", "ajax": false, "filename": "OfferController.php", "line": "392"}, "connection": "eshop", "explain": null, "start_percent": 16.341, "width_percent": 3.32}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.887198, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "OfferController.php:394", "source": {"index": 10, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=394", "ajax": false, "filename": "OfferController.php", "line": "394"}, "connection": "eshop", "explain": null, "start_percent": 19.661, "width_percent": 0}, {"sql": "select `id` from `stores` where `user_id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.887685, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:9251", "source": {"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=9251", "ajax": false, "filename": "function_helper.php", "line": "9251"}, "connection": "eshop", "explain": null, "start_percent": 19.661, "width_percent": 4.39}, {"sql": "update `offers` set `short_description` = null, `is_for_all_branches` = 0, `offers`.`updated_at` = '2025-06-28 18:34:49' where `id` = 96", "type": "query", "params": [], "bindings": [null, 0, "2025-06-28 18:34:49", 96], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 403}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.896142, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:403", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=403", "ajax": false, "filename": "OfferController.php", "line": "403"}, "connection": "eshop", "explain": null, "start_percent": 24.05, "width_percent": 14.607}, {"sql": "delete from `offer_categories` where `offer_categories`.`offer_id` = 96", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 449}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.909838, "duration": 0.00826, "duration_str": "8.26ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:449", "source": {"index": 12, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=449", "ajax": false, "filename": "OfferController.php", "line": "449"}, "connection": "eshop", "explain": null, "start_percent": 38.657, "width_percent": 30.468}, {"sql": "select * from `branch_offers` where `branch_offers`.`offer_id` = 96", "type": "query", "params": [], "bindings": [96], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 456}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.924092, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "OfferController.php:456", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 456}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=456", "ajax": false, "filename": "OfferController.php", "line": "456"}, "connection": "eshop", "explain": null, "start_percent": 69.126, "width_percent": 3.209}, {"sql": "insert into `branch_offers` (`created_at`, `offer_id`, `store_branch_id`, `updated_at`) values ('2025-06-28 18:34:49', 96, 31, '2025-06-28 18:34:49')", "type": "query", "params": [], "bindings": ["2025-06-28 18:34:49", 96, 31, "2025-06-28 18:34:49"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 456}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9291642, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "OfferController.php:456", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 456}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=456", "ajax": false, "filename": "OfferController.php", "line": "456"}, "connection": "eshop", "explain": null, "start_percent": 72.335, "width_percent": 2.398}, {"sql": "select count(*) as aggregate from `offer_codes` where `offer_id` = 96 and `store_branch_id` = '31' and `status` = 'AVAILABLE'", "type": "query", "params": [], "bindings": [96, "31", "AVAILABLE"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 479}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.935949, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:479", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 479}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=479", "ajax": false, "filename": "OfferController.php", "line": "479"}, "connection": "eshop", "explain": null, "start_percent": 74.733, "width_percent": 13.611}, {"sql": "select `code` from `offer_codes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 320}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.945485, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:320", "source": {"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 320}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=320", "ajax": false, "filename": "function_helper.php", "line": "320"}, "connection": "eshop", "explain": null, "start_percent": 88.344, "width_percent": 4.463}, {"sql": "insert into `offer_codes` (`code`, `created_at`, `expire_date`, `is_active`, `offer_id`, `store_branch_id`, `updated_at`, `usage_limit`, `used_count`) values ('QSQXNXO2', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0), ('MDL3PYTJ', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0), ('IBHFKENH', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0), ('BIJED5YC', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0), ('WGRM1F2N', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0), ('1IFOTKYF', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0), ('3YQTNARS', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0), ('3DRMN4DZ', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0), ('FGM23RNE', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0), ('MZ0Y2SBQ', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0), ('BB7M0UVB', '2025-06-28 18:34:49', '2025-07-05 20:32:00', 1, 96, '31', '2025-06-28 18:34:49', 1, 0)", "type": "query", "params": [], "bindings": ["QSQXNXO2", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0, "MDL3PYTJ", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0, "IBHFKENH", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0, "BIJED5YC", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0, "WGRM1F2N", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0, "1IFOTKYF", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0, "3YQTNARS", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0, "3DRMN4DZ", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0, "FGM23RNE", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0, "MZ0Y2SBQ", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0, "BB7M0UVB", "2025-06-28 18:34:49", "2025-07-05 20:32:00", 1, 96, "31", "2025-06-28 18:34:49", 1, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 344}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.951542, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:344", "source": {"index": 14, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=344", "ajax": false, "filename": "function_helper.php", "line": "344"}, "connection": "eshop", "explain": null, "start_percent": 92.807, "width_percent": 7.193}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.966708, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "OfferController.php:394", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 394}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=394", "ajax": false, "filename": "OfferController.php", "line": "394"}, "connection": "eshop", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Store": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\awfarly\\Offer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FOffer.php&line=1", "ajax": false, "filename": "Offer.php", "line": "?"}}}, "count": 6, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/seller/offers/96/edit\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "store_id": "26", "store_name": "<PERSON><PERSON>", "store_image": "stores/1750281403_logo.png", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "403", "system_settings": "{\"app_name\":\"\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a\",\"support_number\":\"919876543210\",\"support_email\":\"<EMAIL>\",\"logo\":\"media\\/1751134451_logo.png\",\"favicon\":null,\"storage_type\":\"local\",\"current_version_of_android_app\":\"1.1.0\",\"current_version_of_ios_app\":\"1.0.0\",\"version_system_status\":0,\"expand_product_image\":0,\"customer_app_maintenance_status\":1,\"offer_request_status\":0,\"message_for_customer_app\":\"sdsa\",\"sidebar_color\":null,\"sidebar_type\":null,\"navbar_fixed\":0,\"theme_mode\":0,\"currency_setting\":\"\\u062f.\\u0644\"}", "success": "تم تحديث العرض بنجاح.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/seller/offers/update/96", "status_code": "<pre class=sf-dump id=sf-dump-676498959 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-676498959\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-424297131 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-424297131\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1742946172 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>pro_input_name_ar</span>\" => \"<span class=sf-dump-str title=\"3 characters\">saa</span>\"\n  \"<span class=sf-dump-key>pro_input_name_en</span>\" => \"<span class=sf-dump-str title=\"4 characters\">asas</span>\"\n  \"<span class=sf-dump-key>description_ar</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asa</span>\"\n  \"<span class=sf-dump-key>description_en</span>\" => \"<span class=sf-dump-str title=\"4 characters\">asas</span>\"\n  \"<span class=sf-dump-key>tags</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>pro_input_price_before_discount</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"\n  \"<span class=sf-dump-key>pro_input_discounted_price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1.00</span>\"\n  \"<span class=sf-dump-key>expire_date</span>\" => \"<span class=sf-dump-str title=\"16 characters\">2025-07-05T20:32</span>\"\n  \"<span class=sf-dump-key>branches</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">31</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>code_count</span>\" => \"<span class=sf-dump-str title=\"3 characters\">011</span>\"\n  \"<span class=sf-dump-key>pro_input_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>other_images</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">138</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"3 characters\">139</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742946172\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-556959202 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1972</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryR1QiH7b816zPW9fk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/seller/offers/96/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ink3VjBadWwvNk5rdjZJNUlZbHNqOFE9PSIsInZhbHVlIjoiVWlDSWs2TmhqRnVkbVZnajc0UFh2M2dGQzMrQS9PSkhyZytpYVhSUVgzUER1akZYRVVTNUNsTUZ0NXdEVmtjM1o4cVAyZHNSUElQTktZQjNoejN2ZCtvZ3Exa3poWDVJY215d2pvOFRGRE42S0V2OVlMdTQ3YzNlVkJZTUFreEciLCJtYWMiOiIxY2UwOGI1MjAzYzcyMzc3ODVhNTM4NjZjMGYyZDI4NTNiM2IyZTk1M2Q3NWQ3OGNlYjEyYzBlOWM5YjljMWY5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkUzUlErTnpnM3NvV212OFlLMGlOMVE9PSIsInZhbHVlIjoieVdEbHgzZjUxN1NjaGtrcmpZR2MwTFpIdTNZSEJLOE1BUDUyWXRyTmlFNTZkMVNQR2twaGdrYWtJcytmSE9OQW1wemRLbzR3SUhKUXpIUlJVdEZNb3JHb1ZVbXVtWGYwQS8xaUhrUkFSdEZBSURPRGduMERmZlNRQldlZG9nSTgiLCJtYWMiOiI0NWQwZGYzYmZmMDcxOGRlZDY3Mjc0NDBlODgyMjA0M2U1NTAyNmZiZGI1Mjg4MmJiZTEwNDQ3OGZhZTlhY2U0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556959202\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1203573376 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mVUiKCS25MLlribzCWoFa9ZEN0oL6qkA0JoxpOT0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203573376\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1979306874 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 18:34:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/seller/offers/manage_offers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjNvbzNvTWo4VVgwcWJaS3FjRkJwWEE9PSIsInZhbHVlIjoiQ0RJWjZoSFpoM0lsTWFCR0F6L1JWb01YcHBDVTZwcndHejNnZDBmQWtnWWhEeUxKOHNNaVJQMHZibWNTNDI1UUpnYnRMR0RDcHhMQVk2Z3lUbGFjWkZQTE1SbWNaRmFIZ1N5dy9yNEg0UnFkWERDeitQMTBXbCtyRGdxc3dyUmkiLCJtYWMiOiI5MDgzNjIxNDI3MmY1NDZmODBjY2ExMDdlMDQyOWIyZGFjYjY3MDNlODhlMDQzODg3YTcxM2FlOTNhOTI0MDJkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:34:49 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjZOTnl5UlVVbG1qWk9wQmROUTNSYWc9PSIsInZhbHVlIjoiMHFTWW9ISVVBODRlT0FjdDJvWUxoNXB1S0VzVmc0RHBPejM4Q3FMcWhtaTd2ak1BQXgwWDFyREFaVEFmbXF1N3pPcE10QnlrVDRvSG1wNFY2cU14ank0TlM2QlhCNnBUeWM0c0NXSVg0YXFqQmpDYndJVmh1a0xUZGtXdndlRDciLCJtYWMiOiJkM2VkYzJlM2IwZGZiZmQyMzE5NTJmNDZmOGQ3MDI0N2M3OWFmYWU4ZjUxNDZlOWEzZjYzN2FiNTQ0MTQ3NjEwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:34:49 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjNvbzNvTWo4VVgwcWJaS3FjRkJwWEE9PSIsInZhbHVlIjoiQ0RJWjZoSFpoM0lsTWFCR0F6L1JWb01YcHBDVTZwcndHejNnZDBmQWtnWWhEeUxKOHNNaVJQMHZibWNTNDI1UUpnYnRMR0RDcHhMQVk2Z3lUbGFjWkZQTE1SbWNaRmFIZ1N5dy9yNEg0UnFkWERDeitQMTBXbCtyRGdxc3dyUmkiLCJtYWMiOiI5MDgzNjIxNDI3MmY1NDZmODBjY2ExMDdlMDQyOWIyZGFjYjY3MDNlODhlMDQzODg3YTcxM2FlOTNhOTI0MDJkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:34:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjZOTnl5UlVVbG1qWk9wQmROUTNSYWc9PSIsInZhbHVlIjoiMHFTWW9ISVVBODRlT0FjdDJvWUxoNXB1S0VzVmc0RHBPejM4Q3FMcWhtaTd2ak1BQXgwWDFyREFaVEFmbXF1N3pPcE10QnlrVDRvSG1wNFY2cU14ank0TlM2QlhCNnBUeWM0c0NXSVg0YXFqQmpDYndJVmh1a0xUZGtXdndlRDciLCJtYWMiOiJkM2VkYzJlM2IwZGZiZmQyMzE5NTJmNDZmOGQ3MDI0N2M3OWFmYWU4ZjUxNDZlOWEzZjYzN2FiNTQ0MTQ3NjEwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:34:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979306874\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-6185214 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/seller/offers/96/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>26</span>\n  \"<span class=sf-dump-key>store_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">bothina</span>\"\n  \"<span class=sf-dump-key>store_image</span>\" => \"<span class=sf-dump-str title=\"26 characters\">stores/1750281403_logo.png</span>\"\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>403</span>\n  \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"528 characters\">{&quot;app_name&quot;:&quot;\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:&quot;media\\/1751134451_logo.png&quot;,&quot;favicon&quot;:null,&quot;storage_type&quot;:&quot;local&quot;,&quot;current_version_of_android_app&quot;:&quot;1.1.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;version_system_status&quot;:0,&quot;expand_product_image&quot;:0,&quot;customer_app_maintenance_status&quot;:1,&quot;offer_request_status&quot;:0,&quot;message_for_customer_app&quot;:&quot;sdsa&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0,&quot;currency_setting&quot;:&quot;\\u062f.\\u0644&quot;}</span>\"\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1575;&#1604;&#1593;&#1585;&#1590; &#1576;&#1606;&#1580;&#1575;&#1581;.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6185214\", {\"maxDepth\":0})</script>\n"}}