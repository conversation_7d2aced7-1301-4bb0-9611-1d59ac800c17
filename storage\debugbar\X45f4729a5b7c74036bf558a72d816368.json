{"__meta": {"id": "X45f4729a5b7c74036bf558a72d816368", "datetime": "2025-06-28 10:56:30", "utime": **********.078766, "method": "GET", "uri": "/admin/orders/list?limit=10&sort=id&order=desc&offset=0", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 12, "messages": [{"message": "[10:56:29] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 72", "message_html": null, "is_string": false, "label": "warning", "time": **********.262404, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:29] LOG.info: offers [\n    [\n        {\n            \"id\": 95,\n            \"name\": \"dx\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T12:00:37.000000Z\",\n            \"expire_date\": \"2025-06-21T14:00:00.000000Z\",\n            \"offer_views\": 0,\n            \"store_id\": 26,\n            \"store\": {\n                \"id\": 26,\n                \"user_id\": 403,\n                \"description_en\": \"fsd\",\n                \"description_ar\": \"\\u0627\\u062e\\u064a\\u0633\",\n                \"name_en\": \"bothina\",\n                \"name_ar\": \"bothina\",\n                \"logo\": \"stores\\/1750281403_logo.png\",\n                \"cover\": \"stores\\/1750281403_cover.jpg\",\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"created_at\": \"2025-06-18T17:13:27.000000Z\",\n                \"updated_at\": \"2025-06-18T21:16:43.000000Z\",\n                \"store_views\": 0,\n                \"primary_color\": \"#e46262\",\n                \"secondary_color\": \"#e46262\",\n                \"hover_color\": \"#e46262\",\n                \"active_color\": \"#e46262\",\n                \"background_color\": \"#e46262\",\n                \"is_approved\": 0\n            },\n            \"images\": [\n                {\n                    \"offer_id\": 95,\n                    \"image\": \"offers\\/rj56t1uLoCsfeBKauPYRBUpLFYC5MnicnxIzUuWJ.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 94,\n            \"name\": \"dx\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T11:59:42.000000Z\",\n            \"expire_date\": \"2025-06-21T13:59:00.000000Z\",\n            \"offer_views\": 0,\n            \"store_id\": 26,\n            \"store\": {\n                \"id\": 26,\n                \"user_id\": 403,\n                \"description_en\": \"fsd\",\n                \"description_ar\": \"\\u0627\\u062e\\u064a\\u0633\",\n                \"name_en\": \"bothina\",\n                \"name_ar\": \"bothina\",\n                \"logo\": \"stores\\/1750281403_logo.png\",\n                \"cover\": \"stores\\/1750281403_cover.jpg\",\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"created_at\": \"2025-06-18T17:13:27.000000Z\",\n                \"updated_at\": \"2025-06-18T21:16:43.000000Z\",\n                \"store_views\": 0,\n                \"primary_color\": \"#e46262\",\n                \"secondary_color\": \"#e46262\",\n                \"hover_color\": \"#e46262\",\n                \"active_color\": \"#e46262\",\n                \"background_color\": \"#e46262\",\n                \"is_approved\": 0\n            },\n            \"images\": [\n                {\n                    \"offer_id\": 94,\n                    \"image\": \"offers\\/IDVLNpoer64nKk1UF9SQKsHEuao1KrhH2ok36iKF.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 93,\n            \"name\": \"asdasd\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T11:57:38.000000Z\",\n            \"expire_date\": \"2025-06-26T13:57:00.000000Z\",\n            \"offer_views\": 0,\n            \"store_id\": 26,\n            \"store\": {\n                \"id\": 26,\n                \"user_id\": 403,\n                \"description_en\": \"fsd\",\n                \"description_ar\": \"\\u0627\\u062e\\u064a\\u0633\",\n                \"name_en\": \"bothina\",\n                \"name_ar\": \"bothina\",\n                \"logo\": \"stores\\/1750281403_logo.png\",\n                \"cover\": \"stores\\/1750281403_cover.jpg\",\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"created_at\": \"2025-06-18T17:13:27.000000Z\",\n                \"updated_at\": \"2025-06-18T21:16:43.000000Z\",\n                \"store_views\": 0,\n                \"primary_color\": \"#e46262\",\n                \"secondary_color\": \"#e46262\",\n                \"hover_color\": \"#e46262\",\n                \"active_color\": \"#e46262\",\n                \"background_color\": \"#e46262\",\n                \"is_approved\": 0\n            },\n            \"images\": [\n                {\n                    \"offer_id\": 93,\n                    \"image\": \"offers\\/0OAugPqrHnYJtTRDTTTkS82J66NZhmS803K6LweV.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 92,\n            \"name\": \"asdasd\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T11:56:44.000000Z\",\n            \"expire_date\": \"2025-06-20T13:56:00.000000Z\",\n            \"offer_views\": 0,\n            \"store_id\": 26,\n            \"store\": {\n                \"id\": 26,\n                \"user_id\": 403,\n                \"description_en\": \"fsd\",\n                \"description_ar\": \"\\u0627\\u062e\\u064a\\u0633\",\n                \"name_en\": \"bothina\",\n                \"name_ar\": \"bothina\",\n                \"logo\": \"stores\\/1750281403_logo.png\",\n                \"cover\": \"stores\\/1750281403_cover.jpg\",\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"created_at\": \"2025-06-18T17:13:27.000000Z\",\n                \"updated_at\": \"2025-06-18T21:16:43.000000Z\",\n                \"store_views\": 0,\n                \"primary_color\": \"#e46262\",\n                \"secondary_color\": \"#e46262\",\n                \"hover_color\": \"#e46262\",\n                \"active_color\": \"#e46262\",\n                \"background_color\": \"#e46262\",\n                \"is_approved\": 0\n            },\n            \"images\": [\n                {\n                    \"offer_id\": 92,\n                    \"image\": \"offers\\/C01EgPgsuGv8klWwXXhzcep2uODdkot0GlUBscf6.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 91,\n            \"name\": \"dsds\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T11:13:29.000000Z\",\n            \"expire_date\": \"2025-06-20T13:13:00.000000Z\",\n            \"offer_views\": 0,\n            \"store_id\": 26,\n            \"store\": {\n                \"id\": 26,\n                \"user_id\": 403,\n                \"description_en\": \"fsd\",\n                \"description_ar\": \"\\u0627\\u062e\\u064a\\u0633\",\n                \"name_en\": \"bothina\",\n                \"name_ar\": \"bothina\",\n                \"logo\": \"stores\\/1750281403_logo.png\",\n                \"cover\": \"stores\\/1750281403_cover.jpg\",\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"created_at\": \"2025-06-18T17:13:27.000000Z\",\n                \"updated_at\": \"2025-06-18T21:16:43.000000Z\",\n                \"store_views\": 0,\n                \"primary_color\": \"#e46262\",\n                \"secondary_color\": \"#e46262\",\n                \"hover_color\": \"#e46262\",\n                \"active_color\": \"#e46262\",\n                \"background_color\": \"#e46262\",\n                \"is_approved\": 0\n            },\n            \"images\": [\n                {\n                    \"offer_id\": 91,\n                    \"image\": \"offers\\/hX1H87hz8muZBwfGu83PsYeQWLHdYYcl53Z6pNwC.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 90,\n            \"name\": \"sa\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T11:01:15.000000Z\",\n            \"expire_date\": \"2025-06-24T13:01:00.000000Z\",\n            \"offer_views\": 0,\n            \"store_id\": 26,\n            \"store\": {\n                \"id\": 26,\n                \"user_id\": 403,\n                \"description_en\": \"fsd\",\n                \"description_ar\": \"\\u0627\\u062e\\u064a\\u0633\",\n                \"name_en\": \"bothina\",\n                \"name_ar\": \"bothina\",\n                \"logo\": \"stores\\/1750281403_logo.png\",\n                \"cover\": \"stores\\/1750281403_cover.jpg\",\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"created_at\": \"2025-06-18T17:13:27.000000Z\",\n                \"updated_at\": \"2025-06-18T21:16:43.000000Z\",\n                \"store_views\": 0,\n                \"primary_color\": \"#e46262\",\n                \"secondary_color\": \"#e46262\",\n                \"hover_color\": \"#e46262\",\n                \"active_color\": \"#e46262\",\n                \"background_color\": \"#e46262\",\n                \"is_approved\": 0\n            },\n            \"images\": [\n                {\n                    \"offer_id\": 90,\n                    \"image\": \"offers\\/VqwhGay4JyJgfSVyZsqgZ9CjVaN7mS1mN4tAMBB2.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 89,\n            \"name\": \"asa\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T10:59:35.000000Z\",\n            \"expire_date\": \"2025-07-02T12:59:00.000000Z\",\n            \"offer_views\": 0,\n            \"store_id\": 26,\n            \"store\": {\n                \"id\": 26,\n                \"user_id\": 403,\n                \"description_en\": \"fsd\",\n                \"description_ar\": \"\\u0627\\u062e\\u064a\\u0633\",\n                \"name_en\": \"bothina\",\n                \"name_ar\": \"bothina\",\n                \"logo\": \"stores\\/1750281403_logo.png\",\n                \"cover\": \"stores\\/1750281403_cover.jpg\",\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"created_at\": \"2025-06-18T17:13:27.000000Z\",\n                \"updated_at\": \"2025-06-18T21:16:43.000000Z\",\n                \"store_views\": 0,\n                \"primary_color\": \"#e46262\",\n                \"secondary_color\": \"#e46262\",\n                \"hover_color\": \"#e46262\",\n                \"active_color\": \"#e46262\",\n                \"background_color\": \"#e46262\",\n                \"is_approved\": 0\n            },\n            \"images\": [\n                {\n                    \"offer_id\": 89,\n                    \"image\": \"offers\\/Wz7o0Z1QSNDr9kxKqLS8OMPK8Ew9cbMsYjcaJebH.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 88,\n            \"name\": \"asa\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T10:58:44.000000Z\",\n            \"expire_date\": \"2025-06-20T12:58:00.000000Z\",\n            \"offer_views\": 0,\n            \"store_id\": 26,\n            \"store\": {\n                \"id\": 26,\n                \"user_id\": 403,\n                \"description_en\": \"fsd\",\n                \"description_ar\": \"\\u0627\\u062e\\u064a\\u0633\",\n                \"name_en\": \"bothina\",\n                \"name_ar\": \"bothina\",\n                \"logo\": \"stores\\/1750281403_logo.png\",\n                \"cover\": \"stores\\/1750281403_cover.jpg\",\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"created_at\": \"2025-06-18T17:13:27.000000Z\",\n                \"updated_at\": \"2025-06-18T21:16:43.000000Z\",\n                \"store_views\": 0,\n                \"primary_color\": \"#e46262\",\n                \"secondary_color\": \"#e46262\",\n                \"hover_color\": \"#e46262\",\n                \"active_color\": \"#e46262\",\n                \"background_color\": \"#e46262\",\n                \"is_approved\": 0\n            },\n            \"images\": [\n                {\n                    \"offer_id\": 88,\n                    \"image\": \"offers\\/1Eo0szEGbm5roRaxUrt3vNbiTbzhYv1ZCDLTii2j.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 87,\n            \"name\": \"ds\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T10:57:20.000000Z\",\n            \"expire_date\": \"2025-06-26T12:57:00.000000Z\",\n            \"offer_views\": 0,\n            \"store_id\": 26,\n            \"store\": {\n                \"id\": 26,\n                \"user_id\": 403,\n                \"description_en\": \"fsd\",\n                \"description_ar\": \"\\u0627\\u062e\\u064a\\u0633\",\n                \"name_en\": \"bothina\",\n                \"name_ar\": \"bothina\",\n                \"logo\": \"stores\\/1750281403_logo.png\",\n                \"cover\": \"stores\\/1750281403_cover.jpg\",\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"created_at\": \"2025-06-18T17:13:27.000000Z\",\n                \"updated_at\": \"2025-06-18T21:16:43.000000Z\",\n                \"store_views\": 0,\n                \"primary_color\": \"#e46262\",\n                \"secondary_color\": \"#e46262\",\n                \"hover_color\": \"#e46262\",\n                \"active_color\": \"#e46262\",\n                \"background_color\": \"#e46262\",\n                \"is_approved\": 0\n            },\n            \"images\": [\n                {\n                    \"offer_id\": 87,\n                    \"image\": \"offers\\/FiNL1Rr4C3cRlH1jnpdYnYrmLyZj8lK7IzUz2W7g.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 86,\n            \"name\": \"ds\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T10:56:06.000000Z\",\n            \"expire_date\": \"2025-06-20T12:55:00.000000Z\",\n            \"offer_views\": 0,\n            \"store_id\": 26,\n            \"store\": {\n                \"id\": 26,\n                \"user_id\": 403,\n                \"description_en\": \"fsd\",\n                \"description_ar\": \"\\u0627\\u062e\\u064a\\u0633\",\n                \"name_en\": \"bothina\",\n                \"name_ar\": \"bothina\",\n                \"logo\": \"stores\\/1750281403_logo.png\",\n                \"cover\": \"stores\\/1750281403_cover.jpg\",\n                \"is_active\": true,\n                \"is_deleted\": false,\n                \"created_at\": \"2025-06-18T17:13:27.000000Z\",\n                \"updated_at\": \"2025-06-18T21:16:43.000000Z\",\n                \"store_views\": 0,\n                \"primary_color\": \"#e46262\",\n                \"secondary_color\": \"#e46262\",\n                \"hover_color\": \"#e46262\",\n                \"active_color\": \"#e46262\",\n                \"background_color\": \"#e46262\",\n                \"is_approved\": 0\n            },\n            \"images\": [\n                {\n                    \"offer_id\": 86,\n                    \"image\": \"offers\\/3Kf35auLTGlNT11TAGP4KCxXvidzRqje7xVKTFSU.png\"\n                }\n            ]\n        }\n    ]\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.439983, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:29] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 136", "message_html": null, "is_string": false, "label": "warning", "time": **********.456954, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:29] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 136", "message_html": null, "is_string": false, "label": "warning", "time": **********.678036, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:29] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 136", "message_html": null, "is_string": false, "label": "warning", "time": **********.732452, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:29] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 136", "message_html": null, "is_string": false, "label": "warning", "time": **********.785057, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:29] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 136", "message_html": null, "is_string": false, "label": "warning", "time": **********.83252, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:29] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 136", "message_html": null, "is_string": false, "label": "warning", "time": **********.874392, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:29] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 136", "message_html": null, "is_string": false, "label": "warning", "time": **********.914229, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:29] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 136", "message_html": null, "is_string": false, "label": "warning", "time": **********.952249, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:29] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 136", "message_html": null, "is_string": false, "label": "warning", "time": **********.989106, "xdebug_link": null, "collector": "log"}, {"message": "[10:56:30] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php on line 136", "message_html": null, "is_string": false, "label": "warning", "time": **********.027183, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751108187.450566, "end": **********.078844, "duration": 2.6282780170440674, "duration_str": "2.63s", "measures": [{"label": "Booting", "start": 1751108187.450566, "relative_start": 0, "end": **********.149173, "relative_end": **********.149173, "duration": 1.6986069679260254, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.149219, "relative_start": 1.698652982711792, "end": **********.07885, "relative_end": 5.9604644775390625e-06, "duration": 0.9296309947967529, "duration_str": "930ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30699144, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 20, "templates": [{"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.505657, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.643211, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.702597, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.727319, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.757494, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.780646, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.806769, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.828573, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.853915, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.872608, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.891443, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.909055, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.931414, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.948774, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.969624, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.98587, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.005852, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.023587, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.044738, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.066316, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/orders/list", "middleware": "web, CheckInstallation, auth, role:super_admin,admin,editor, CheckStoreNotEmpty", "controller": "App\\Http\\Controllers\\Admin\\OrderController@list", "namespace": null, "prefix": "", "where": [], "as": "admin.orders.list", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOrderController.php&line=68\" onclick=\"\">app/Http/Controllers/Admin/OrderController.php:68-173</a>"}, "queries": {"nb_statements": 34, "nb_visible_statements": 34, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07227999999999998, "accumulated_duration_str": "72.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.2175941, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 1.591}, {"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.227087, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 1.591, "width_percent": 2.158}, {"sql": "select * from `users` where `users`.`id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.237205, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 3.749, "width_percent": 1.854}, {"sql": "select * from `roles` where `roles`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.24961, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 5.603, "width_percent": 1.965}, {"sql": "select count(*) as aggregate from `stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "CheckStoreNotEmpty", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckStoreNotEmpty.php", "line": 19}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}], "start": **********.256145, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "CheckStoreNotEmpty:19", "source": {"index": 19, "namespace": "middleware", "name": "CheckStoreNotEmpty", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckStoreNotEmpty.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FCheckStoreNotEmpty.php&line=19", "ajax": false, "filename": "CheckStoreNotEmpty.php", "line": "19"}, "connection": "eshop", "explain": null, "start_percent": 7.568, "width_percent": 1.245}, {"sql": "select * from `settings` where `variable` = 'system_settings' limit 1", "type": "query", "params": [], "bindings": ["system_settings"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 579}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.263299, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:579", "source": {"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 579}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=579", "ajax": false, "filename": "function_helper.php", "line": "579"}, "connection": "eshop", "explain": null, "start_percent": 8.813, "width_percent": 1.978}, {"sql": "select `id`, `title_ar` as `name`, `is_active`, `created_at`, `expire_date`, `offer_views`, `store_id` from `offers` group by `offers`.`id`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 108}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2834108, "duration": 0.005, "duration_str": "5ms", "memory": 0, "memory_str": null, "filename": "OrderController.php:108", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOrderController.php&line=108", "ajax": false, "filename": "OrderController.php", "line": "108"}, "connection": "eshop", "explain": null, "start_percent": 10.791, "width_percent": 6.918}, {"sql": "select * from `stores` where `stores`.`id` in (1, 20, 21, 22, 23, 24, 26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3100712, "duration": 0.00518, "duration_str": "5.18ms", "memory": 0, "memory_str": null, "filename": "OrderController.php:108", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOrderController.php&line=108", "ajax": false, "filename": "OrderController.php", "line": "108"}, "connection": "eshop", "explain": null, "start_percent": 17.709, "width_percent": 7.167}, {"sql": "select `offer_id`, `image_url` as `image` from `offer_images` where `offer_images`.`offer_id` in (36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 70, 72, 73, 74, 75, 76, 78, 79, 80, 81, 82, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95) and `is_main` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 108}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.338547, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "OrderController.php:108", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOrderController.php&line=108", "ajax": false, "filename": "OrderController.php", "line": "108"}, "connection": "eshop", "explain": null, "start_percent": 24.875, "width_percent": 2.961}, {"sql": "select `id`, `title_ar` as `name`, `is_active`, `created_at`, `expire_date`, `offer_views`, `store_id` from `offers` group by `offers`.`id`, `id` order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 114}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3544009, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "OrderController.php:114", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOrderController.php&line=114", "ajax": false, "filename": "OrderController.php", "line": "114"}, "connection": "eshop", "explain": null, "start_percent": 27.836, "width_percent": 4.676}, {"sql": "select * from `stores` where `stores`.`id` in (26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 114}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.369322, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "OrderController.php:114", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOrderController.php&line=114", "ajax": false, "filename": "OrderController.php", "line": "114"}, "connection": "eshop", "explain": null, "start_percent": 32.512, "width_percent": 3.307}, {"sql": "select `offer_id`, `image_url` as `image` from `offer_images` where `offer_images`.`offer_id` in (86, 87, 88, 89, 90, 91, 92, 93, 94, 95) and `is_main` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 114}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3748, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "OrderController.php:114", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Admin/OrderController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OrderController.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOrderController.php&line=114", "ajax": false, "filename": "OrderController.php", "line": "114"}, "connection": "eshop", "explain": null, "start_percent": 35.819, "width_percent": 3.763}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 402 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [402, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.4723759, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:38", "source": {"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=38", "ajax": false, "filename": "AppServiceProvider.php", "line": "38"}, "connection": "eshop", "explain": null, "start_percent": 39.582, "width_percent": 1.716}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.476932, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:39", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=39", "ajax": false, "filename": "AppServiceProvider.php", "line": "39"}, "connection": "eshop", "explain": null, "start_percent": 41.298, "width_percent": 2.117}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.482906, "duration": 0.00297, "duration_str": "2.97ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 43.414, "width_percent": 4.109}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.6252918, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 47.524, "width_percent": 2.739}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.679451, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 50.263, "width_percent": 4.068}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.703965, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 54.33, "width_percent": 3.362}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.734001, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 57.692, "width_percent": 4.192}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.7582898, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 61.884, "width_percent": 3.016}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.786027, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 64.9, "width_percent": 3.182}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.807652, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 68.082, "width_percent": 3.874}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.8334792, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 71.956, "width_percent": 3.058}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.8553169, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 75.014, "width_percent": 2.214}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.875241, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 77.227, "width_percent": 2.919}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.8927832, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 80.147, "width_percent": 3.044}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.9151058, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 83.19, "width_percent": 2.767}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.9324698, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 85.957, "width_percent": 2.269}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.9531178, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 88.226, "width_percent": 2.49}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.970712, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 90.717, "width_percent": 1.743}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.9896739, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 92.46, "width_percent": 1.508}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.006748, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 93.968, "width_percent": 1.55}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.028091, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 95.517, "width_percent": 2.449}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.0459318, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 97.966, "width_percent": 2.034}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 5540, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\awfarly\\Offer": {"value": 58, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FOffer.php&line=1", "ajax": false, "filename": "Offer.php", "line": "?"}}, "App\\Models\\awfarly\\OfferImages": {"value": 49, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FOfferImages.php&line=1", "ajax": false, "filename": "OfferImages.php", "line": "?"}}, "App\\Models\\awfarly\\Store": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Setting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}}, "count": 5659, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "HIFIBSOF75z10mVeKEpiyurHuBRr9opJ79mlwkSS", "store_id": "20", "store_name": "null", "store_image": "null", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/orders\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "402", "system_settings": "{\"app_name\":\"\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a\",\"support_number\":\"919876543210\",\"support_email\":\"<EMAIL>\",\"logo\":null,\"favicon\":null,\"storage_type\":\"local\",\"current_version_of_android_app\":\"1.1.0\",\"current_version_of_ios_app\":\"1.0.0\",\"version_system_status\":0,\"expand_product_image\":0,\"customer_app_maintenance_status\":1,\"offer_request_status\":0,\"message_for_customer_app\":\"sdsa\",\"sidebar_color\":null,\"sidebar_type\":null,\"navbar_fixed\":0,\"theme_mode\":0,\"currency_setting\":\"\\u062f.\\u0644\"}"}, "request": {"path_info": "/admin/orders/list", "status_code": "<pre class=sf-dump id=sf-dump-2106186278 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2106186278\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2010566805 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010566805\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1255869062 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1255869062\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1396401246 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">HIFIBSOF75z10mVeKEpiyurHuBRr9opJ79mlwkSS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/admin/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InowS3loV2c5ZDV3N0tTakZHRkRmV2c9PSIsInZhbHVlIjoidE0zMW9iWjFSOHRMbm5zR0RDMGlLaXFFc2E3cnZIOVhzMnFqZGg2K21ub2JaNFg1K3NOVHRRbTRIQWRrMjZGTFg5UGhKQjVFcHVJRCtjMHNpSnFzSG04T2FFOEsrWmZRSjRxcGtXdHNBd3hwcGlJaDluWkI1WkVVU0FDeHZzQ1giLCJtYWMiOiI3YjJmYjVmODU3NWNmN2Q0ZDkyODZjNWJmNzJlN2E1NGZlZjZmMzA1NDQwOWFmMGM2NTQ3NGIxZDJhNGQ5M2Q1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IllmMGVtNGxYTVAwOHJoNGFWTndwZVE9PSIsInZhbHVlIjoiUUo0Z2lBeGVaYTNLeW1NSE56YWNUZkhMcm0wZEovZVZKWjlnM1RHdGVkWXFSVXFJOS9UdENaeGFocHFKWlNQbVdRck80Ti9hS2xjVVBpVkcyT1prS0k1M3ZUZTlQMDJER09OZDBYbFBVdko5b1dQZTFTZFVCanpMTjlUU2J3NlUiLCJtYWMiOiIxMzhjNDIwYjljMTJlMjk3MzA5NmVhNzhhYjhhOTAyODQxNTQ0YTA3ZjFiMzQwZTE3NjhhMmIyOTFmNGE0ZGQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1396401246\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1419013844 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HIFIBSOF75z10mVeKEpiyurHuBRr9opJ79mlwkSS</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kG491Qos0uYHdgKgtGYFpA0pwkJgFWuoBTczgOOd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1419013844\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-228354999 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 10:56:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjBjYlhuVG9GR2tnRVNWdVRuakp3eXc9PSIsInZhbHVlIjoiRDRvUFBqaU5BMjkrZWpjaFNFZ2RSTldKSC9QV0M1c1N6bWFtVlp5WktJQ3dWL1h4Vkh3eVZ4d2FxVm95M1Z3NkVLRUNVTkFQd1dzaURMQ3VCakR4dU5rc2RaU2JGRzBIaUZxVmRhbHBSUlBGM0NUNWtOMUc2TzBOVTdoWHNKUGMiLCJtYWMiOiI5OTVkYTg0NDAzNjI5ZTM4MDNkMjVhMzU1ODY4Y2I1MWEyNDMyNDcyODYyNWY2ODdkNTUwZTY5M2VlOTIxZWQyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 12:56:30 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IklKMm9rNnBHZ3laelNGd2RCbEk3NWc9PSIsInZhbHVlIjoiUnR4eW4rbnVPYzZzR252VE5VZTUzVXlIZUdmL0s1cDVETmNLa0d5U2ZpanV4b1ZvTkNVMjh1SEhjRkEyTzVPRE0xaGlxbW9PWkVjWTdDc2piNUQ5QmZMdnpJTGhrS2xWbVRFUXRFODFBWjVVV2tiT2Z1ejhCWXdKWnVWb3JoaHYiLCJtYWMiOiJjODA3MmU2MGU4ZmZjNDFjZDJjMGFhMzUzMzRhOTAzZmMzZWNmODg5MzhkOWZkZTdhMmQwMWYwYTdjMzVlNGY3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 12:56:30 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjBjYlhuVG9GR2tnRVNWdVRuakp3eXc9PSIsInZhbHVlIjoiRDRvUFBqaU5BMjkrZWpjaFNFZ2RSTldKSC9QV0M1c1N6bWFtVlp5WktJQ3dWL1h4Vkh3eVZ4d2FxVm95M1Z3NkVLRUNVTkFQd1dzaURMQ3VCakR4dU5rc2RaU2JGRzBIaUZxVmRhbHBSUlBGM0NUNWtOMUc2TzBOVTdoWHNKUGMiLCJtYWMiOiI5OTVkYTg0NDAzNjI5ZTM4MDNkMjVhMzU1ODY4Y2I1MWEyNDMyNDcyODYyNWY2ODdkNTUwZTY5M2VlOTIxZWQyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 12:56:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IklKMm9rNnBHZ3laelNGd2RCbEk3NWc9PSIsInZhbHVlIjoiUnR4eW4rbnVPYzZzR252VE5VZTUzVXlIZUdmL0s1cDVETmNLa0d5U2ZpanV4b1ZvTkNVMjh1SEhjRkEyTzVPRE0xaGlxbW9PWkVjWTdDc2piNUQ5QmZMdnpJTGhrS2xWbVRFUXRFODFBWjVVV2tiT2Z1ejhCWXdKWnVWb3JoaHYiLCJtYWMiOiJjODA3MmU2MGU4ZmZjNDFjZDJjMGFhMzUzMzRhOTAzZmMzZWNmODg5MzhkOWZkZTdhMmQwMWYwYTdjMzVlNGY3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 12:56:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228354999\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-226728386 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HIFIBSOF75z10mVeKEpiyurHuBRr9opJ79mlwkSS</span>\"\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>20</span>\n  \"<span class=sf-dump-key>store_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/admin/orders</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>402</span>\n  \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"504 characters\">{&quot;app_name&quot;:&quot;\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:null,&quot;favicon&quot;:null,&quot;storage_type&quot;:&quot;local&quot;,&quot;current_version_of_android_app&quot;:&quot;1.1.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;version_system_status&quot;:0,&quot;expand_product_image&quot;:0,&quot;customer_app_maintenance_status&quot;:1,&quot;offer_request_status&quot;:0,&quot;message_for_customer_app&quot;:&quot;sdsa&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0,&quot;currency_setting&quot;:&quot;\\u062f.\\u0644&quot;}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226728386\", {\"maxDepth\":0})</script>\n"}}