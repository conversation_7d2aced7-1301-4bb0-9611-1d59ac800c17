<?php $__env->startSection('title'); ?>
<?php echo e(labels('admin_labels.manage_stores', 'Manage Stores')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<div class="d-flex row align-items-center">
    <div class="col-md-6 col-xl-6 page-info-title">
        <h3><?php echo e(labels('admin_labels.manage_stores', 'Manage Stores')); ?>

        </h3>
        <p class="sub_title">
            <?php echo e(labels('admin_labels.track_and_manage_stores', 'Track and Manage Stores')); ?>

        </p>
    </div>
    <div class="col-md-6 col-xl-6 d-flex justify-content-end">
        <nav aria-label="breadcrumb" class="float-end" dir="ltr">
            <ol class="breadcrumb">
                <i class='bx bx-home-smile'></i>
                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.home')); ?>"><?php echo e(labels('admin_labels.home', 'Home')); ?></a>
                </li>
                <li class="breadcrumb-item second_breadcrumb_item">
                    <?php echo e(labels('admin_labels.stores', 'Stores')); ?>

                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <?php echo e(labels('admin_labels.manage_stores', 'Manage Stores')); ?>

                </li>
            </ol>
        </nav>
    </div>
</div>




<section class="overview-data">
    <div class="card content-area p-4 ">
        <div class="row align-items-center d-flex heading mb-5">
            <div class="col-md-12">
                <div class="row">
                    
                    <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bx bx-check-circle me-2"></i> <?php echo e(session('success')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bx bx-error-circle me-2"></i> <?php echo e(session('error')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>
                    <div class="col-md-6">
                        <h4><?php echo e(labels('admin_labels.manage_stores', 'Manage Stores')); ?>

                        </h4>
                    </div>
                    <div class="col-md-6 d-flex justify-content-end " dir="ltr">
                        <div class="input-group me-2 search-input-grp ">
                            <span class="search-icon"><i class='bx bx-search-alt'></i></span>
                            <input type="text" data-table="admin_stores_list" class="form-control searchInput"
                                placeholder="Search...">
                            <span class="input-group-text"><?php echo e(labels('admin_labels.search', 'Search')); ?></span>
                        </div>
                        <a class="btn me-2" id="tableFilter" data-bs-toggle="offcanvas"
                            data-bs-target="#columnFilterOffcanvas" data-table="admin_stores_list" dateFilter='false'
                            orderStatusFilter='false' paymentMethodFilter='false' StatusFilter='true'
                            orderTypeFilter='false'><i class='bx bx-filter-alt'></i></a>
                        <a class="btn me-2" id="tableRefresh" data-table="admin_stores_list"><i
                                class='bx bx-refresh'></i></a>
                        <div class="dropdown">
                            <a class="btn dropdown-toggle export-btn" type="button" id="exportOptionsDropdown"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <i class='bx bx-download'></i>
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="exportOptionsDropdown">
                                <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_stores_list','csv')">CSV</button></li>
                                <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_stores_list','json')">JSON</button></li>
                                <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_stores_list','sql')">SQL</button></li>
                                <li><button class="dropdown-item" type="button" onclick="exportTableData('admin_stores_list','excel')">Excel</button></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="pt-0">
                    <div class="table-responsive">
                        <table class='table' id="admin_stores_list" data-toggle="table"
                            data-loading-template="loadingTemplate" data-url="<?php echo e(route('admin.stores.list')); ?>"
                            data-click-to-select="true" data-side-pagination="server" data-pagination="true"
                            data-page-list="[5, 10, 20, 50, 100, 200]" data-search="false" data-show-columns="false"
                            data-show-refresh="false" data-trim-on-search="false" data-sort-name="id"
                            data-sort-order="desc" data-mobile-responsive="true" data-toolbar="" data-show-export="false"
                            data-maintain-selected="true" data-export-types='["txt","excel"]'
                            data-query-params="store_query_params">
                            <thead>
                                <tr>
                                    <th data-field="id" data-sortable="true" data-visible='true'>
                                        <?php echo e(labels('admin_labels.id', 'ID')); ?>

                                    <th data-field="name" data-disabled="1" data-sortable="false">
                                        <?php echo e(labels('admin_labels.name', 'Name')); ?>

                                    </th>
                                    <th class="d-flex justify-content-center" data-field="image" data-sortable="false">
                                        <?php echo e(labels('admin_labels.image', 'Image')); ?>

                                    </th>
                                    <th data-field="banner" data-sortable="false">
                                        <?php echo e(labels('admin_labels.banner_image', 'Banner Image')); ?>

                                    </th>
                                    <th data-field="status" data-sortable="false">
                                        <?php echo e(labels('admin_labels.status', 'Status')); ?>

                                    </th>
                                    <th data-field="last_login" data-sortable="false">
                                        <?php echo e(__('admin_labels.last_login')); ?>

                                    </th>
                                    <th data-field="operate" data-sortable="false">
                                        <?php echo e(labels('admin_labels.action', 'Action')); ?>

                                    </th>
                                    
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin/layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/pages/tables/manage_stores.blade.php ENDPATH**/ ?>