<?php

namespace App\Http\Controllers\Seller;

use App\Enums\CodeStatus;
use App\Enums\FavoritesType;
use App\Http\Requests\AddOfferRequest;
use App\Jobs\SendOfferNotifications;
use App\Models\Tax;
use App\Models\User;
use App\Models\Brand;
use App\Models\Seller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Attribute;
use Illuminate\Http\Request;
use App\Models\Attribute_values;
use App\Models\awfarly\Favorite;
use App\Models\awfarly\Offer;
use App\Models\awfarly\OfferClaim;
use App\Models\awfarly\OfferCode;
use App\Models\awfarly\OfferImages;
use App\Models\awfarly\Rating;
use App\Models\awfarly\Store;
use App\Models\awfarly\StoreBranch;
use App\Models\City;
use App\Models\Offers;
use App\Models\Product_variants;
use App\Models\Product_attributes;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Models\OrderItems;
use App\Models\Zipcode;
use App\Models\Zone;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Locale;

use function PHPUnit\Framework\isEmpty;

class OfferController extends Controller
{
    public function index()
    {

        $store_id = getStoreId();
        $branches = StoreBranch::where('store_id', getStoreId())->get();
        return view('seller.pages.forms.offers', compact('branches'));
    }

    /**
     * Store a new offer in the database
     *
     * @param \Illuminate\Http\AddOfferRequest $request The incoming request containing offer data
     * @param bool $fromApp Flag indicating if request comes from mobile app
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request, $fromApp = false)
    {
        try {
            $request->validate([
                'main_image' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048',
                'other_images.*' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
            ]);

            Log::info("req", [$request->all()]);
            // Wrap all database operations in a transaction to ensure data consistency
            DB::transaction(function () use ($request) {
                // Get store ID from request or fallback to default
                $storeId = $request->store_id ?? getStoreId();

                // Determine if offer applies to all branches
                $allBranches = (bool) $request->all_branches;

                // Get branch IDs (empty array if all branches selected)
                $branches = $allBranches ? [] : (array) $request->branches;

                // Get number of codes to generate (0 if not specified)
                $codeCount = $request->add_code ? (int) $request->code_count : 0;

                // Decode JSON tags and convert to collection
                $tags = collect(json_decode($request->tags, true)) ?? [];

                // Extract tag values and implode into comma-separated string
                $tagString = $tags->pluck('value')->implode(',');


                // Save main image
                $mainImagePath = $request->file('main_image')->store('offers', 'public');

                // Save other images (if any)
                $otherImagePaths = [];
                if ($request->hasFile('other_images')) {
                    foreach ($request->file('other_images') as $file) {
                        $path = $file->store('offers', 'public');
                        $otherImagePaths[] = $path;
                    }
                }
                $setting = getSettings('system_settings', true);
                $settings = json_decode($setting, true);
                $requestOfferStatus = $settings['offer_request_status'];
                if ($requestOfferStatus == 1) {
                    $is_approved = 0;
                } else {
                    $is_approved = 1;
                }

                // CREATE OFFER RECORD
                $offer = Offer::create([
                    'title_ar' => $request->pro_input_name_ar,        // Arabic title
                    'title_en' => $request->pro_input_name_en,        // English title
                    'short_description' => $request->short_description, // Short description
                    'slug' => generateSlug($request->pro_input_name_en, 'offers', 'slug'), // Generate SEO-friendly slug
                    'tags' => $tagString,                             // Comma-separated tags
                    'store_id' => $storeId,                           // Associated store
                    'price' => $request->pro_input_price_before_discount, // Original price
                    'discounted_price' => $request->pro_input_discounted_price, // Discounted price
                    'is_for_all_branches' => $allBranches ? 1 : 0,    // All branches flag
                    'is_active' => 1,                                 // Activate immediately
                    'is_approved' => $is_approved,
                    'description_ar' => $request->description_ar,     // Arabic description
                    'description_en' => $request->description_en,      // English description
                    'expire_date' => $request->expire_date            // Offer expiration
                ]);

                // Process main image and any additional images
                process_offer_images(
                    $offer,                          // The created offer
                    $mainImagePath,       // Main image path
                    $otherImagePaths  // Array of additional images
                );

                if ($request->filled('category_id')) {
                    // Decode and split category IDs
                    $categoryIds = array_map('intval', explode(',', $request->category_id));
                    $offer->categories()->attach($categoryIds);
                }

                if (!$allBranches) {
                    // Attach to specific branches with timestamps
                    $offer->storeBranches()->attach($branches, [
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }

                if ($codeCount > 0) {
                    foreach ($branches as $branchId) {
                        // Create specified number of codes for each branch
                        create_offer_codes($offer, $branchId, $codeCount);
                    }
                }
                $storeId = $offer->store_id;
                $categoryIds = $offer->categories->pluck('id')->toArray();
                SendOfferNotifications::dispatch($offer);
            });


            // Return success response
            return response()->json([
                'error' => false,
                'message' => __('admin_labels.offer_added_successfully'), // Translated success message
                'data' => [],
                'location' => route('store.offer.manage_offer') // Redirect location
            ]);
        } catch (Exception $e) {
            Log::error('Error creating offer: ' . $e->getMessage(), [
                'exception' => $e,
                'request_data' => $request->except(['pro_input_image', 'other_images']) // Don't log file data
            ]);

            // Return error response
            return response()->json([
                'error' => true,
                'message' => __('admin_labels.offer_added_failed'),
                'data' => [],
            ], 500);
        }
    }

    public function getBranches(Request $request)
    {
        Log::info('getBranches request');
        $search = trim($request->search) ?? "";
        $store_id = getStoreId();
        $branches = StoreBranch::where('label', 'LIKE', '%' . $search . '%')
            ->where('store_id', $store_id)
            ->orderBy('id', 'desc')
            ->get();
        $data = array();
        foreach ($branches as $branche) {
            $data[] = array("id" => $branche->id, "text" => $branche->label);
        }

        return $data;
    }

    public function manageOffers()
    {
        return view('seller.pages.tables.manage_offers');
    }

    public function list()
    {
        Log::info('list request', [request()->all()]);
        $store_id = getStoreId();
        $search = trim(request('search'));
        $sort = (request('sort')) ? request('sort') : "id";
        $order = (request('order')) ? request('order') : "DESC";
        $limit = request("limit");
        $offset = $search || (request('pagination_offset')) ? (request('pagination_offset')) : 0;
        $settings = getSettings('system_settings', true);
        $settings = json_decode($settings, true);
        $user_id = Auth::id();
        $locale = app()->getLocale();


        $multipleWhere = [];

        if (!empty($search)) {
            $multipleWhere = [
                'offers.id' => $search,
                'offers.title_ar' => $search,
                'offers.title_en' => $search,
                'offers.description_ar' => $search,
                'offers.description_en' => $search,
                'offers.short_description' => $search,
            ];
        }

        $query = Offer::query();
        $query->select('id', 'title_' . $locale . ' as name', 'is_active', 'created_at', 'expire_date', 'offer_views','is_approved')
            ->with(['images' => function ($q) {
                $q->where('is_main', 1)
                    ->select('offer_id', 'image_url as image');
            }])
            ->where('store_id', $store_id);
        $query->where(function ($q) use ($multipleWhere) {
            foreach ($multipleWhere as $column => $value) {
                $q->orWhere($column, 'like', '%' . $value . '%');
            }
        });
        Log::info('offer ', [$query->get()]);

        if (request()->filled('status')) {
            request('status') == 1 ? $query->where('expire_date', '>', Carbon::now()) : $query->where('expire_date', '<', Carbon::now());
            $query->orWhere('is_active', request('status'));
        }
        if (request()->filled('offer_type')) {
            if (request('offer_type') == 'simple_offers') {
                $query->doesntHave('codes');
            } else {
                $query->whereHas('codes');
            }
        }
        if (request()->filled('category_id')) {
            Log::info("category_id", [request('category_id')]);
            $query->Where(function ($q) {
                $q->WhereHas('categories', function ($q) {
                    $q->Where('categories.id', request('category_id'))
                        ->orWhere('categories.parent_id', request('category_id'));
                });
            });
        }


        $total = $query->groupBy('offers.id')->get()->count();

        $offers = $query->groupBy('id')
            ->orderBy($sort, $order)
            ->skip($offset)
            ->take($limit)
            ->get();

        $offers = $offers->map(function ($p) {
            $edit_url = route('seller.offers.edit', $p->id); // Use 'pid' as you've aliased 'products.id as pid' in the select statement.
            $delete_url = route('seller.offers.destroy', $p->id); // Use 'pid' here as well.
            $show_url = route('seller.offer.show', $p->id);

            $action = '<div class="dropdown bootstrap-table-dropdown">
                    <a href="#" class="text-dark" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="bx bx-dots-horizontal-rounded"></i>
                        </a>
                        <div class="dropdown-menu table_dropdown product_action_dropdown" aria-labelledby="dropdownMenuButton">
                            <a class="dropdown-item" href="' . $edit_url . '"><i class="bx bx-pencil"></i> ' . __("admin_labels.edit") . '</a>
                            <a class="dropdown-item delete-data" data-url="' . $delete_url . '"><i class="bx bx-trash"></i> ' . __("admin_labels.delete") . '</a>
                            <a class="dropdown-item" href="' . $show_url . '"><i class="bx bxs-show"></i> ' . __("admin_labels.view") . '</a>

                        </div>
                    </div>';

            $image = isset($p->images[0]->image) ?  getMediaImageUrl($p->images[0]->image) : null;
            if ($p->is_approved ===1) {
                $is_approved = '<label class="badge bg-success">' . __('admin_labels.approved') . '</label>';
            } else if ($p->is_approved == 0) {
                $is_approved = '<label class="badge bg-warning">' . __('admin_labels.pending') . '</label>';

            } else {
                $is_approved = '<label class="badge bg-danger">' . __('admin_labels.rejected') . '</label>';

            }
            return [
                'id' => $p->id,
                'name' => $p->name . '<br><small>' . ucwords(str_replace('_', ' ', $p->type)) . '</small><br><small> By </small><b>' . $p->store_name . '</b>',
                'offer_views' => $p->offer_views,
                'created_at' => $p->created_at->format('d-m-Y H:i:s'),
                'expire_date' => $p->expire_date != null ? $p->expire_date->format('d-m-Y H:i:s') : null,
                'is_approved' => $is_approved,

                // Dropdown to toggle active/inactive status
                'status' => view('components.status-dropdown', [
                    'id'            => $p->id,
                    'url'           => "/seller/offers/update_status/{$p->id}",
                    'isActive'      => $p->is_active,
                    'activeClass'   => 'active_status',
                    'inactiveClass' => 'inactive_status'
                ])->render(),
                // Logo image preview with lightbox
                'image' => view('components.image-lightbox', [
                    'id'        => "image-{$p->id}",
                    'url'       => $image,
                    'thumbnail' => $image,
                    'width'     => 90
                ])->render(),
                'image' => $image == null ? '-' : '<div><a href="' . getMediaImageUrl($p->images[0]->image) . '" data-lightbox="image-' . $p->pid . '"><img src="' . $image . '" alt="Avatar" class="rounded" width="60" height="90"/></a></div>',
                'operate' => $action,

            ];
        });

        return response()->json([
            "rows" => $offers,
            "total" => $total,
        ]);
    }

    public function edit($data)
    {

        $store_id = getStoreId();

        $data = Offer::where('store_id', $store_id)
            ->with([
                'images'
            ])->find($data);
        if ($data === null || empty($data)) {
            return view('admin.pages.views.no_data_found');
        } else {

            $branches = $data->storeBranches()->get();
            $has_code = OfferCode::where('offer_id', $data->id)->exists();
            // Initialize an empty array to hold code counts per branch
            $branch_code_counts = [];

            // Loop through each branch to get its specific offer code count
            foreach ($branches as $branch) {
                $count = OfferCode::where('offer_id', $data->id) // Current offer ID
                    ->where('store_branch_id', $branch->id) // Specific branch ID
                    ->count();
                // Store the count with the branch ID as the key
                $branch_code_counts[$branch->id] = $count;
            }
            $data->branch_code_counts = $branch_code_counts;
            $locale = app()->getLocale();
            $code_count =   $branches->count() > 0 ? OfferCode::where('offer_id', $data->id)->where('store_branch_id', $branches[0]->id)->count() : 0;
            $main_image = $data->images()->where('is_main', 1)->first();
            $other_images = $data->images()->where('is_main', 0)->get();
            $selected_categories = $data->categories()->pluck('categories.id')->toArray();
            $all_categories = Category::where('is_active', 1)
                ->whereNotNull('parent_id')
                ->whereIn('parent_id', function ($query) use ($store_id) {
                    $query->select('category_id')
                        ->from('store_categories')
                        ->where('store_id', $store_id);
                })
                ->select('id', 'name_' . $locale . ' as name', 'image', 'is_active')
                ->get();

            return view('seller.pages.forms.update_offer', compact('data', 'branches', 'all_categories', 'selected_categories', 'has_code', 'code_count', 'main_image', 'other_images'));
        }
    }
    public function update(AddOfferRequest $request, $id)
    {
        try {
            Log::info("Offer Update Request", [$request->all()]);

            $offer = Offer::findOrFail($id);

            DB::transaction(function () use ($request, $offer) {
                $storeId = $request->store_id ?? getStoreId();
                $allBranches = (bool) $request->all_branches;
                $branches = $allBranches ? [] : (array) $request->branches;

                // Tags
                $tags = collect(json_decode($request->tags, true)) ?? [];
                $tagString = $tags->pluck('value')->implode(',');

                $offer->update([
                    'title_ar' => $request->pro_input_name_ar,
                    'title_en' => $request->pro_input_name_en,
                    'short_description' => $request->short_description,
                    'slug' => $request->pro_input_name_en != $offer->title_en
                        ? generateSlug($request->pro_input_name_en, 'offers', 'slug')
                        : $offer->slug,
                    'tags' => $tagString,
                    'store_id' => $storeId,
                    'price' => $request->pro_input_price_before_discount,
                    'discounted_price' => $request->pro_input_discounted_price,
                    'is_for_all_branches' => $allBranches ? 1 : 0,
                    'description_ar' => $request->description_ar,
                    'description_en' => $request->description_en,
                    'expire_date' => $request->expire_date
                ]);
                $mainImagePath = null;
                if ($request->hasFile('pro_input_image_file')) {

                    $mainImagePath = upload_image($request->pro_input_image_file, 'offers', 'offer');
                }
                $otherImagePaths = [];
                if ($request->hasFile('other_images_files')) {
                    foreach ($request->file('other_images_files') as $file) {
                        $path = upload_image($file, 'offers', 'offer');
                        $otherImagePaths[] = $path;
                    }
                }
                // Images
                process_images_if_changed(
                    $offer,
                    $mainImagePath,
                    $otherImagePaths
                );

                // Categories
                if ($request->filled('category_ids')) {
                    $categoryIds = $request->category_ids;
                    Log::info("category ids", [$categoryIds]);
                    foreach ($categoryIds as $key => $categoryId) {
                        if (Category::where('id', $categoryId)->whereNull('parent_id')->exists()) {
                            unset($categoryIds[$key]);
                        }
                    }
                    $offer->categories()->sync($categoryIds);
                } else {
                    $offer->categories()->detach();
                }

                // Branches
                if ($allBranches) {
                    $offer->storeBranches()->detach();
                } else {
                    $offer->storeBranches()->sync(
                        collect($branches)->mapWithKeys(function ($branchId) {
                            return [$branchId => ['created_at' => now(), 'updated_at' => now()]];
                        })
                    );
                }

                // Offer Codes
                if ($request->filled('code_count')) {
                    Log::info("code count", [$request->code_count]);
                    $codeCountInput = $request->code_count;

                    if (is_string($codeCountInput)) {
                        Log::info("from string");
                        // If offer is for all branches, get all store branches
                        $branchIdsToUse = $allBranches
                            ? $offer->store->branches()->pluck('id')->toArray()
                            : (array) $branches;
                        foreach ($branchIdsToUse as $branchId) {
                            Log::info("branch id", [$branchId]);
                            $available = OfferCode::where('offer_id', $offer->id)
                                ->where('store_branch_id', $branchId)
                                ->where('status', CodeStatus::AVAILABLE)
                                ->count();
                            Log::info("available", [$available]);

                            $toCreate = (int)$codeCountInput - $available;
                            Log::info("to create", [$toCreate]);
                            if ($toCreate > 0) {
                                create_offer_codes($offer, $branchId, $toCreate);
                            }
                        }
                    } elseif (is_array($codeCountInput)) {
                        foreach ($codeCountInput as $branchId => $count) {
                            $available = OfferCode::where('offer_id', $offer->id)
                                ->where('store_branch_id', $branchId)
                                ->where('status', CodeStatus::AVAILABLE)
                                ->count();

                            $toCreate = (int)$count - $available;
                            if ($toCreate > 0) {
                                create_offer_codes($offer, $branchId, $toCreate);
                            }
                        }
                    }
                }
            });
            return redirect()->route('store.offer.manage_offer')->with('success', __('admin_labels.offer_updated_successfully'));
        } catch (\Exception $e) {
            Log::error('Offer update failed', [
                'exception' => $e,
                'request_data' => $request->except(['pro_input_image', 'other_images']),
                'offer_id' => $id
            ]);
            return redirect()->back()->withErrors(['error' => __('admin_labels.default_error_message')]);
        }
    }

    /**
     * Deletes a specific offer by its ID.
     *
     * @param int $id The ID of the offer to be deleted.
     * @return JsonResponse Returns a JSON response indicating success or failure.
     */
    public function destroy($id): JsonResponse
    {
        // Attempt to find the offer by its ID.
        $offer = Offer::find($id);

        // Check if the offer exists.
        if ($offer) {
            // If the offer is found, attempt to delete it.
            $offer->delete();

            // Return a success JSON response.
            return response()->json([
                'error' => false,
                'message' => __('admin_labels.offer_deleted_successfully')
            ]);
        } else {
            // If the offer is not found, return an error JSON response.
            return response()->json([
                'error' => __('admin_labels.offer_deleted_failed')
            ]);
        }
    }

    public function deleteImage(Request $request)
    {
        $response['is_deleted'] = deleteImage($request['id'], $request['path'], $request['field'], $request['img_name'], $request['table_name'], false);
        return response()->json([$response]);
    }

    public function bulk_upload()
    {
        return view('seller.pages.forms.product_bulk_upload');
    }
    public function process_bulk_upload(Request $request)
    {

        if (!$request->hasFile('upload_file')) {
            return response()->json(['error' => 'true', 'message' => labels('admin_labels.please_choose_file', 'Please Choose File')]);
        }
        $allowed_mime_types = [
            'text/x-comma-separated-values',
            'text/comma-separated-values',
            'application/x-csv',
            'text/x-csv',
            'text/csv',
            'application/csv',
        ];

        $uploaded_file = $request->file('upload_file');
        $uploaded_mime_type = $uploaded_file->getClientMimeType();

        if (!in_array($uploaded_mime_type, $allowed_mime_types)) {
            return response()->json(['error' => 'true', 'message' => labels('admin_labels.invalid_file_format', 'Invalid File Format')]);
        }

        $csv = $_FILES['upload_file']['tmp_name'];
        $temp = 0;
        $temp1 = 0;
        $handle = fopen($csv, "r");
        $allowed_status = array("received", "processed", "shipped");
        $video_types = array("youtube", "vimeo");
        $type = $request->type;

        if ($type == 'upload') {
            while (($row = fgetcsv($handle, 10000, ",")) != FALSE) //get row values
            {

                if ($temp != 0) {
                    if (empty($row[0])) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.category_id_empty', 'Category id is empty at row ') . $row[0]]);
                    }
                    if ($row[2] != 'simple_product' && $row[2] != 'variable_product') {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.product_type_invalid_at_row', 'Product type is invalid at row') . $temp]);
                    }

                    if (empty($row[4])) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.name_is_empty_at_row', 'Name is empty at row') . $temp]);
                    }

                    if (!empty($row[7]) && $row[7] != 1) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.cod_allowed_invalid_at_row', 'COD allowed is invalid at row') . $temp]);
                    }

                    if (!empty($row[11]) && $row[11] != 1) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.prices_inclusive_tax_invalid_at_row', 'Is prices inclusive tax is invalid at row') . $temp]);
                    }

                    if (!empty($row[12]) && $row[12] != 1) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.returnable_invalid_at_row', 'Is Returnable is invalid at row') . $temp]);
                    }

                    if (!empty($row[13]) && $row[13] != 1) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.cancelable_invalid_at_row', 'Is Cancelable is invalid at row') . $temp]);
                    }

                    if (!empty($row[13]) && $row[13] == 1 && (empty($row[14]) || !in_array($row[14], $allowed_status))) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.cancelable_till_invalid_at_row', 'Cancelable till is invalid at row') . $temp]);
                    }

                    if (empty($row[13]) && !(empty($row[14]))) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.cancelable_till_invalid_at_row', 'Cancelable till is invalid at row') . $temp]);
                    }

                    if (empty($row[15])) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.image_is_empty_at_row', 'Image is empty at row') . $temp]);
                    }

                    if (!empty($row[17]) && !in_array($row[17], $video_types)) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.video_type_invalid', 'Video type is invalid at row ') . $temp]);
                    }

                    if ($row[27] != 0 && $row[27] != 1 && $row[27] != 2 && $row[27] != 3 && $row[27] == "") {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.invalid_deliverable_type', 'Not valid value for deliverable_type at row ') . $temp]);
                    }

                    if ($row[27] == '2' || $row[27] == '3') {
                        if (empty($row[28])) {
                            return response()->json(['error' => 'true', 'message' => labels('admin_labels.deliverable_zones_empty_at_row', 'Deliverable Zipcodes is empty at row') . $temp]);
                        }
                    }

                    if (empty($row[29])) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.store_id_empty_at_row', 'Store id is empty at row') . $temp]);
                    }
                    $user_id = Auth::user()->id;
                    $seller_id = Seller::where('user_id', $user_id)->value('id');
                    // dd($row[29]);
                    $seller_data = fetchdetails('seller_store', ['seller_id' => $seller_id, 'store_id' => $row[29]], ['category_ids', 'permissions']);
                    $permissions = isset($seller_data) && !empty($seller_data) ? json_decode($seller_data[0]->permissions, true) : [];
                    // dd($permissions);
                    if (!isset($seller_data[0]->category_ids) || !in_array($row[0], explode(',', $seller_data[0]->category_ids))) {
                        return response()->json(['error' => 'true', 'message' => 'This Category ID : ' . $row[0] . ' is not assign to seller id:' . $seller_id . ' at row ' . $temp]);
                    }

                    $index1 = 35;
                    $total_variants = 0;
                    for ($j = 0; $j < 70; $j++) {

                        if (!empty($row[$index1])) {
                            $total_variants++;
                        }
                        $index1 = $index1 + 11;
                    }
                    $variant_index = 35;
                    for ($k = 0; $k < $total_variants; $k++) {
                        if ($row[2] == 'variable_product') {
                            if (empty($row[$variant_index])) {
                                return response()->json(['error' => 'true', 'message' => labels('admin_labels.attribute_value_ids_empty', 'Attribute value ids is empty at row ') . $temp]);
                            }
                            $variant_index = $variant_index + 11;
                        }
                    }
                    if ($total_variants == 0) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.variants_not_found', 'Variants not found at row ') . $temp]);
                    } elseif ($row[2] == 'simple_product' && $total_variants > 1) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.too_many_variants_for_simple_product', 'You cannot add variants more than one for simple product at row ') . $temp]);
                    }
                }
                $temp++;
            }

            fclose($handle);
            $handle = fopen($csv, "r");
            while (($row = fgetcsv($handle, 10000, ",")) != FALSE) //get row vales
            {

                if ($temp1 != 0) {

                    $data['category_id'] = $row[0];
                    if (!empty($row[1])) {
                        $data['tax'] = $row[1];
                    }
                    $data['type'] = $row[2];
                    if ($row[3] != '') {
                        $data['stock_type'] = $row[3];
                    }

                    $data['name'] = $row[4];
                    $data['short_description'] = $row[5];
                    $data['slug'] = generateSlug($row[4], 'products');
                    if ($row[6] != '') {
                        $data['indicator'] = $row[6];
                    }
                    if ($row[7] != '') {
                        $data['cod_allowed'] = $row[7];
                    }

                    if ($row[8] != '') {
                        $data['minimum_order_quantity'] = $row[8];
                    }
                    if ($row[9] != '') {
                        $data['quantity_step_size'] = $row[9];
                    }
                    if ($row[10] != '') {
                        $data['total_allowed_quantity'] = $row[10];
                    }
                    if ($row[11] != '') {
                        $data['is_prices_inclusive_tax'] = $row[11];
                    }
                    if ($row[12] != '') {
                        $data['is_returnable'] = $row[12];
                    }
                    if ($row[13] != '') {
                        $data['is_cancelable'] = $row[13];
                    }
                    $data['cancelable_till'] = $row[14];
                    $data['image'] = $row[15];
                    if (isset($row[16]) && $row[16] != '') {
                        $other_images = explode(',', $row[16]);
                        $data['other_images'] = json_encode($other_images, 1);
                    } else {
                        $data['other_images'] = '[]';
                    }
                    $data['video_type'] = $row[17];
                    $data['video'] = $row[18];
                    $data['tags'] = $row[19];
                    $data['warranty_period'] = $row[20];
                    $data['guarantee_period'] = $row[21];
                    $data['made_in'] = $row[22];

                    if (!empty($row[23])) {
                        $data['sku'] = $row[23];
                    }
                    if (!empty($row[24])) {
                        $data['stock'] = $row[24];
                    }
                    if ($row[25] != '') {
                        $data['availability'] = $row[25];
                    }

                    $data['description'] = $row[26];
                    $data['deliverable_type'] = $row[27]; //in csv its 28th
                    $data['deliverable_zones'] = $row[28]; // in csv its 29th
                    $data['store_id'] = $row[29]; // in csv its 29th
                    $data['brand'] = isset($row[30]) ? $row[30] : '';
                    $data['hsn_code'] = isset($row[31]) ? $row[31] : '';
                    $data['pickup_location'] = isset($row[32]) ? $row[32] : '';
                    $data['extra_description'] = isset($row[33]) ? $row[33] : '';
                    $data['seller_id'] = isset($seller_id) ? $seller_id : '';
                    // dd($permissions['require_products_approval']);
                    if ($permissions['require_products_approval'] == 1) {
                        $data['status'] = 2;
                    }



                    $product = Product::create($data);

                    $index1 = 35;
                    $total_variants = 0;
                    for ($j = 0; $j < 70; $j++) {
                        if (!empty($row[$index1])) {
                            $total_variants++;
                        }
                        $index1 = $index1 + 11;
                    }

                    $index1 = 34;
                    $attribute_value_ids = '';
                    for ($j = 0; $j < $total_variants; $j++) {
                        if (!empty($row[$index1])) {
                            if (!empty($attribute_value_ids)) {
                                $attribute_value_ids .= ',' . strval($row[$index1]);
                            } else {
                                $attribute_value_ids = strval($row[$index1]);
                            }
                        }
                        $index1 = $index1 + 11;
                    }
                    $attribute_value_ids = !empty($attribute_value_ids) ? $attribute_value_ids : '';
                    $product_attribute_data = [
                        'product_id' => $product->id,
                        'attribute_value_ids' => $attribute_value_ids,

                    ];
                    $product_attributes = Product_attributes::create($product_attribute_data);

                    $index = 34;
                    for ($i = 0; $i < $total_variants; $i++) {
                        $variant_data[$i]['images'] = '[]';
                        $variant_data[$i]['product_id'] = $product->id;

                        if (strval($data['type']) == 'variable_product') {
                            $variant_data[$i]['attribute_value_ids'] = $row[$index];
                        } else {
                            $variant_data[$i]['attribute_value_ids'] = null;
                        }
                        $index++;
                        $variant_data[$i]['price'] = $row[$index];
                        $index++;
                        if (isset($row[$index]) && !empty($row[$index])) {
                            $variant_data[$i]['special_price'] = $row[$index];
                        } else {
                            $variant_data[$i]['special_price'] = 0;
                        }

                        $index++;
                        if (isset($row[$index]) && !empty($row[$index])) {
                            $variant_data[$i]['sku'] = $row[$index];
                        }
                        $index++;
                        if (isset($row[$index]) && !empty($row[$index])) {
                            $variant_data[$i]['stock'] = $row[$index];
                        }

                        $index++;
                        if (isset($row[$index]) && $row[$index] != '' && !empty($row[$index])) {
                            $images = explode(',', $row[$index]);
                            $variant_data[$i]['images'] = json_encode($images, 1);
                        }

                        $index++;
                        if (isset($row[$index]) && $row[$index] != '') {
                            $variant_data[$i]['availability'] = $row[$index];
                        }

                        $index++;
                        if (isset($row[$index]) && $row[$index] != '') {
                            $variant_data[$i]['weight'] = $row[$index];
                        }

                        $index++;
                        if (isset($row[$index]) && $row[$index] != '') {
                            $variant_data[$i]['height'] = $row[$index];
                        }

                        $index++;
                        if (isset($row[$index]) && $row[$index] != '') {
                            $variant_data[$i]['breadth'] = $row[$index];
                        }

                        $index++;
                        if (isset($row[$index]) && $row[$index] != '') {
                            $variant_data[$i]['length'] = $row[$index];
                        }

                        $index++;
                        $product_attributes = Product_variants::create($variant_data[$i]);
                    }
                }
                $temp1++;
            }
            fclose($handle);
            return response()->json(['error' => 'false', 'message' => labels('admin_labels.products_uploaded_successfully', 'Products uploaded successfully!')]);
        } else { // bulk_update
            while (($row = fgetcsv($handle, 10000, ",")) != FALSE) //get row vales
            {

                if ($temp != 0) {
                    if (empty($row[0])) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.product_id_empty_at_row', 'Product id is empty at row') . $temp]);
                    }

                    if (!empty($row[3]) && $row[3] != 'simple_product' && $row[3] != 'variable_product') {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.product_type_invalid_at_row', 'Product type is invalid at row') . $temp]);
                    }


                    if (!empty($row[8]) && $row[8] != 1) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.cod_allowed_invalid_at_row', 'COD allowed is invalid at row') . $temp]);
                    }

                    if (!empty($row[12]) && $row[12] != 1) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.prices_inclusive_tax_invalid_at_row', 'Is prices inclusive tax is invalid at row') . $temp]);
                    }

                    if (!empty($row[13]) && $row[13] != 1) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.returnable_invalid_at_row', 'Is Returnable is invalid at row') . $temp]);
                    }

                    if (!empty($row[14]) && $row[14] != 1) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.cancelable_invalid_at_row', 'Is Cancelable is invalid at row') . $temp]);
                    }

                    if (!empty($row[14]) && $row[14] == 1 && (empty($row[15]) || !in_array($row[15], $allowed_status))) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.cancelable_till_invalid_at_row', 'Cancelable till is invalid at row') . $temp]);
                    }

                    if (empty($row[14]) && !(empty($row[15]))) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.cancelable_till_invalid_at_row', 'Cancelable till is invalid at row') . $temp]);
                    }

                    if (!empty($row[18]) && !in_array($row[17], $video_types)) {
                        return response()->json(['error' => 'true', 'message' => labels('admin_labels.video_type_invalid', 'Video type is invalid at row ') . $temp]);
                    }
                    if ($row[27] != "") {
                        if ($row[27] != 0 && $row[27] != 1 && $row[27] != 2 && $row[27] != 3) {
                            return response()->json(['error' => 'true', 'message' => labels('admin_labels.invalid_deliverable_type', 'Not valid value for deliverable_type at row ') . $temp]);
                        }
                    }

                    if ($row[27] != "" && ($row[27] == '2' || $row[27] == '3')) {
                        if (empty($row[28])) {
                            return response()->json(['error' => 'true', 'message' => labels('admin_labels.deliverable_zones_empty_at_row', 'Deliverable Zipcodes is empty at row') . $temp]);
                        }
                    }

                    if (!empty($row[1])) {
                        if (empty($row[29])) {
                            return response()->json(['error' => 'true', 'message' => 'Seller ID is empty at row ' . $temp]);
                        }
                        $user_id = Auth::user()->id;
                        $seller_id = Seller::where('user_id', $user_id)->value('id');

                        $seller_data = fetchdetails('seller_store', ['seller_id' => $seller_id], 'category_ids');

                        if (!isset($seller_data[0]->category_ids) || !in_array($row[1], explode(',', $seller_data[0]->category_ids))) {
                            return response()->json(['error' => 'true', 'message' => 'This Category ID : ' . $row[1] . ' is not assign to seller id:' . $seller_id . ' at row ' . $temp]);
                        }

                        if (empty($row[30])) {
                            return response()->json(['error' => 'true', 'message' => labels('admin_labels.variant_id_empty', 'Variant ID is empty at row') . $temp]);
                        }
                    }
                }
                $temp++;
            }

            fclose($handle);
            $handle = fopen($csv, "r");
            while (($row = fgetcsv($handle, 10000, ",")) != FALSE) //get row values
            {


                if ($temp1 != 0) {
                    $product_id = $row[0];
                    $product = fetchDetails('products', ['id' => $product_id], '*');

                    if (isset($product[0]) && !empty($product[0])) {
                        if (!empty($row[1])) {
                            $data['category_id'] = $row[1];
                        } else {
                            $data['category_id'] = $product[0]->category_id;
                        }
                        if (!empty($row[2])) {
                            $data['tax'] = $row[2];
                        } else {
                            $data['tax'] = $product[0]->tax;
                        }
                        if (!empty($row[3])) {
                            $data['type'] = $row[3];
                        } else {
                            $data['type'] = $product[0]->type;
                        }
                        if ($row[4] != '') {
                            $data['stock_type'] = $row[4];
                        } else {
                            $data['stock_type'] = $product[0]->stock_type;
                        }
                        if (!empty($row[5])) {
                            $data['name'] = $row[5];
                            $data['slug'] = generateSlug($row[5], 'products');
                        } else {
                            $data['name'] = $product[0]->name;
                        }
                        if (!empty($row[6])) {
                            $data['short_description'] = $row[6];
                        } else {
                            $data['short_description'] = $product[0]->short_description;
                        }
                        if ($row[7] != '') {
                            $data['indicator'] = $row[7];
                        } else {
                            $data['indicator'] = $product[0]->indicator;
                        }
                        if (!empty($row[8])) {
                            $data['cod_allowed'] = $row[8];
                        } else {
                            $data['cod_allowed'] = $product[0]->cod_allowed;
                        }

                        if (!empty($row[9])) {
                            $data['minimum_order_quantity'] = $row[9];
                        } else {
                            $data['minimum_order_quantity'] = $product[0]->minimum_order_quantity;
                        }
                        if (!empty($row[10])) {
                            $data['quantity_step_size'] = $row[10];
                        } else {
                            $data['quantity_step_size'] = $product[0]->quantity_step_size;
                        }
                        if ($row[11] != '') {
                            $data['total_allowed_quantity'] = $row[11];
                        } else {
                            $data['total_allowed_quantity'] = $product[0]->total_allowed_quantity;
                        }
                        if ($row[12] != '') {
                            $data['is_prices_inclusive_tax'] = $row[12];
                        } else {
                            $data['is_prices_inclusive_tax'] = $product[0]->is_prices_inclusive_tax;
                        }
                        if ($row[13] != '') {
                            $data['is_returnable'] = $row[13];
                        } else {
                            $data['is_returnable'] = $product[0]->is_returnable;
                        }
                        if ($row[14] != '') {
                            $data['is_cancelable'] = $row[14];
                        } else {
                            $data['is_cancelable'] = $product[0]->is_cancelable;
                        }
                        if (!empty($row[15])) {
                            $data['cancelable_till'] = $row[15];
                        } else {
                            $data['cancelable_till'] = $product[0]->cancelable_till;
                        }
                        if (!empty($row[16])) {
                            $data['image'] = $row[16];
                        } else {
                            $data['image'] = $product[0]->image;
                        }
                        if (!empty($row[17])) {
                            $data['video_type'] = $row[17];
                        } else {
                            $data['video_type'] = $product[0]->video_type;
                        }
                        if (!empty($row[18])) {
                            $data['video'] = $row[18];
                        } else {
                            $data['video'] = $product[0]->video;
                        }
                        if (!empty($row[19])) {
                            $data['tags'] = $row[19];
                        } else {
                            $data['tags'] = $product[0]->tags;
                        }
                        if (!empty($row[20])) {
                            $data['warranty_period'] = $row[20];
                        } else {
                            $data['warranty_period'] = $product[0]->warranty_period;
                        }
                        if (!empty($row[21])) {
                            $data['guarantee_period'] = $row[21];
                        } else {
                            $data['guarantee_period'] = $product[0]->guarantee_period;
                        }
                        if (!empty($row[22])) {
                            $data['made_in'] = $row[22];
                        } else {
                            $data['made_in'] = $product[0]->made_in;
                        }
                        if (!empty($row[23])) {
                            $data['sku'] = $row[23];
                        } else {
                            $data['sku'] = $product[0]->sku;
                        }
                        if ($row[24] != '') {
                            $data['stock'] = $row[24];
                        } else {
                            $data['stock'] = $product[0]->stock;
                        }
                        if ($row[25] != '') {
                            $data['availability'] = $row[25];
                        } else {
                            $data['availability'] = $product[0]->availability;
                        }
                        if ($row[26] != '') {
                            $data['description'] = $row[26];
                        } else {
                            $data['description'] = $product[0]->description;
                        }
                        if ($row[27] != '') {
                            $data['deliverable_type'] = $row[27];
                        } else {
                            $data['deliverable_type'] = $product[0]->deliverable_type;
                        }
                        if ($row[27] != '' && ($row[27] == '2' || $row[27] == '3')) {
                            $data['deliverable_zones'] = $row[28];
                        } else {
                            $data['deliverable_zones'] = $product[0]->deliverable_zones;
                        }

                        if ($row[29] != '') {
                            $data['brand'] = $row[29];
                        } else {
                            $data['brand'] = $product[0]->brand;
                        }
                        if ($row[30] != '') {
                            $data['hsn_code'] = $row[30];
                        } else {
                            $data['hsn_code'] = $product[0]->hsn_code;
                        }
                        if ($row[31] != '') {
                            $data['pickup_location'] = $row[31];
                        } else {
                            $data['pickup_location'] = $product[0]->pickup_location;
                        }
                        if ($row[32] != '') {
                            $data['extra_description'] = $row[32];
                        } else {
                            $data['extra_description'] = $product[0]->extra_description;
                        }
                        Product::where('id', $row[0])->update($data);
                    }
                    $index1 = 33;
                    $total_variants = 0;
                    for ($j = 0; $j < 70; $j++) {
                        if (!empty($row[$index1])) {
                            $total_variants++;
                        }
                        $index1 = $index1 + 10;
                    }
                    $index = 33;
                    for ($i = 0; $i < $total_variants; $i++) {
                        $variant_id = $row[$index];
                        $variant = fetchDetails('product_variants', ['id' => $row[$index]], '*');
                        if (isset($variant[0]) && !empty($variant[0])) {
                            $variant_data[$i]['product_id'] = $variant[0]->product_id;
                            $index++;
                            if (isset($row[$index]) && !empty($row[$index])) {
                                $variant_data[$i]['price'] = $row[$index];
                            } else {
                                $variant_data[$i]['price'] = $variant[0]->price;
                            }
                            $index++;
                            if (isset($row[$index]) && $row[$index] != '') {
                                $variant_data[$i]['special_price'] = $row[$index];
                            } else {
                                $variant_data[$i]['special_price'] = $variant[0]->special_price;
                            }
                            $index++;
                            if (isset($row[$index]) && !empty($row[$index])) {
                                $variant_data[$i]['sku'] = $row[$index];
                            } else {
                                $variant_data[$i]['sku'] = $variant[0]->sku;
                            }
                            $index++;
                            if (isset($row[$index]) && $row[$index] != '') {
                                $variant_data[$i]['stock'] = $row[$index];
                            } else {
                                $variant_data[$i]['stock'] = $variant[0]->stock;
                            }

                            $index++;
                            if (isset($row[$index]) && $row[$index] != '') {
                                $variant_data[$i]['availability'] = $row[$index];
                            } else {
                                $variant_data[$i]['availability'] = $variant[0]->availability;
                            }

                            $index++;
                            if (isset($row[$index]) && $row[$index] != '') {
                                $variant_data[$i]['weight'] = $row[$index];
                            }

                            $index++;
                            if (isset($row[$index]) && $row[$index] != '') {
                                $variant_data[$i]['height'] = $row[$index];
                            }

                            $index++;
                            if (isset($row[$index]) && $row[$index] != '') {
                                $variant_data[$i]['breadth'] = $row[$index];
                            }

                            $index++;
                            if (isset($row[$index]) && $row[$index] != '') {
                                $variant_data[$i]['length'] = $row[$index];
                            }
                            $index++;
                            Product_variants::where('id', $variant_id)->update($variant_data[$i]);
                        }
                    }
                }
                $temp1++;
            }
            fclose($handle);
            return response()->json(['error' => 'false', 'message' => labels('admin_labels.products_updated_successfully', 'Products updated successfully!')]);
        }
    }

    /**
     * Toggle the active status of a specific offer.
     *
     * This method switches the `is_active` flag:
     * - If currently active (1), it becomes inactive (0)
     * - If currently inactive (0), it becomes active (1)
     *
     * Returns a JSON response indicating success or failure.
     *
     * @param int $id Offer ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function update_status($id)
    {
        // Attempt to find the offer or fail with 404
        $offer = Offer::findOrFail($id);

        try {
            // Toggle the is_active status
            $offer->is_active = $offer->is_active == 1 ? 0 : 1;

            // Save the updated status
            $offer->save();

            return response()->json([
                'success' => labels('admin_labels.status_updated_successfully', 'Status updated successfully.')
            ]);
        } catch (\Exception $e) {
            // Handle and return any exception that occurs
            return response()->json([
                'error' => labels('admin_labels.something_went_wrong', 'Something went wrong')
            ]);
        }
    }


    public function show($id)
    {
        $locale = app()->getLocale();
        $store_id = getStoreId();

        $data = Offer::where('store_id', $store_id)
            ->with([
                'store:id,name_' . $locale . ' as store_name,logo as store_logo,description_' . $locale . ' as description',
                'store.branches',
                'storeBranches',
                'images',
            ])
            ->withCount([
                // Count how many ratings are 1-star
                'ratings as ratings_1_star' => function ($query) {
                    $query->where('rating', 1);
                },
                // Count how many ratings are 2-stars
                'ratings as ratings_2_star' => function ($query) {
                    $query->where('rating', 2);
                },
                // Count how many ratings are 3-stars
                'ratings as ratings_3_star' => function ($query) {
                    $query->where('rating', 3);
                },
                // Count how many ratings are 4-stars
                'ratings as ratings_4_star' => function ($query) {
                    $query->where('rating', 4);
                },
                // Count how many ratings are 5-stars
                'ratings as ratings_5_star' => function ($query) {
                    $query->where('rating', 5);
                },
                'ratings as ratings_count' => function ($query) {
                    $query->whereNotNull('rating');
                },
            ])
            ->withAvg('ratings', 'rating')
            ->find($id);
        if ($data === null || empty($data)) {
            return view('admin.pages.views.no_data_found');
        } else {

            $store = Store::where('id', $store_id)
                ->first();

            $branches = $data->is_for_all_branches
                ? $data->store->branches
                : StoreBranch::whereIn('id', $data->storeBranches->pluck('id'))
                ->with('city')
                ->get();

            $categories = $data->categories()->get();
            $category_names = [];
            foreach ($categories as $category) {
                $category_names[] = $category->{'name_' . app()->getLocale()};
            }
            $has_code = OfferCode::where('offer_id', $id)->exists();

            // Get reviews for the offer with pagination
            $reviews = Rating::where('offer_id', $id)
                ->with(['user:id,username'])
                ->select(['user_id', 'rating', 'review', 'created_at'])
                ->whereNotNull('review')
                ->orderByDesc('created_at')
                ->get();
            $main_image = $data->images()->where('is_main', 1)->first();
            $other_images = $data->images()->where('is_main', 0)->get();
            $claim_count = OfferClaim::where('offer_id', $id)
                ->count();
            $code_counts = OfferCode::where('offer_id', $id)
                ->where('status', CodeStatus::AVAILABLE)
                ->select('store_branch_id', DB::raw('count(*) as count'))
                ->groupBy('store_branch_id')
                ->pluck('count', 'store_branch_id')
                ->toArray();

            $status = $data->expire_date < Carbon::now() ? __('admin_labels.expired') : __('admin_labels.active');
            Log::info('code_counts', [$code_counts]);
            return view('seller.pages.views.offer', compact('data', 'store', 'branches', 'reviews', 'claim_count', 'category_names', 'main_image', 'other_images', 'has_code', 'code_counts', 'status'));
        }
    }
    public function fetch_rating($product_id = '', $user_id = '', $limit = '', $offset = '', $sort = '', $order = '', $rating_id = '', $has_images = '', $rating = '')
    {
        //     if (!empty($product_id)) {
        //         $query = DB::table('combo_product_ratings')
        //             ->select(
        //                 'combo_product_ratings.*',
        //                 'users.username as user_name',
        //                 // 'users.image as user_profile'
        //             )
        //             ->leftJoin('users', 'users.id', '=', 'combo_product_ratings.user_id');
        //         $query->where('product_id', $product_id);
        //     }

        //     if (!empty($user_id)) {
        //         $query->where('user_id', $user_id);
        //     }

        //     if (!empty($rating_id)) {
        //         $query->where('id', $rating_id);
        //     }
        //     if (!empty($rating)) {
        //         $query->where('rating', $rating);
        //     }
        //     $query->orderBy($sort, $order);

        //     $query->skip($offset)->take($limit);

        //     if (

        //         !empty($has_images) && $has_images == 1
        //     ) {
        //         $query->whereNotNull('combo_product_ratings.images');
        //     }

        //     $combo_product_ratings = DB::table('combo_product_ratings')
        //         ->select(
        //             'combo_product_ratings.*',
        //             'users.username as user_name',
        //             // 'users.image as user_profile'
        //         )
        //         ->leftJoin('users', 'users.id', '=', 'combo_product_ratings.user_id');
        //     if (!empty($product_id)) {
        //         $combo_product_ratings->where('product_id', $product_id);
        //     }
        //     if (!empty($rating)) {
        //         $combo_product_ratings->where('rating', $rating);
        //     }

        //     if (!empty($user_id)) {
        //         $combo_product_ratings->where('user_id', $user_id);
        //     }

        //     if (!empty($rating_id)) {
        //         $combo_product_ratings->where('id', $rating_id);
        //     }

        //     $combo_product_ratings->orderBy($sort, $order);

        //     $combo_product_ratings->skip($offset)->take($limit);


        //     $rating_data =   $combo_product_ratings->get()->toArray();

        //     foreach ($rating_data as $rating) {

        //         if (($rating->images) != null) {
        //             $images = json_decode($rating->images, true);
        //             $images = array_map(function ($image) {
        //                 return asset('storage/' . $image);
        //             }, $images);
        //             $rating->images = $images;
        //         } else {
        //             $rating->images = [];
        //         }

        //         if (!empty($rating->user_profile)) {
        //             $rating->user_profile = asset(config('constants.USER_IMG_PATH') . $rating->user_profile);
        //         }

        //         $rating;
        //     }

        //     $total_rating = ComboProductRating::selectRaw('count(combo_product_ratings.id) as no_of_rating')
        //         ->join('users as u', 'u.id', '=', 'combo_product_ratings.user_id')
        //         ->where('product_id', $product_id)
        //         ->get()
        //         ->toArray();

        //     $total_images = ComboProductRating::selectRaw('ROUND(((LENGTH(`images`) - LENGTH(REPLACE(`images`, ",", ""))) / LENGTH(","))+1) as total')
        //         ->where('product_id', $product_id)
        //         ->get()
        //         ->toArray();

        //     $total_review_with_images = ComboProductRating::selectRaw('count(id) as total')
        //         ->where('product_id', $product_id)
        //         ->whereNotNull('images')
        //         ->get()
        //         ->toArray();

        //     $total_reviews = ComboProductRating::selectRaw('count(id) as total,
        //     sum(case when CEILING(rating) = 1 then 1 else 0 end) as rating_1,
        //     sum(case when CEILING(rating) = 2 then 1 else 0 end) as rating_2,
        //     sum(case when CEILING(rating) = 3 then 1 else 0 end) as rating_3,
        //     sum(case when CEILING(rating) = 4 then 1 else 0 end) as rating_4,
        //     sum(case when CEILING(rating) = 5 then 1 else 0 end) as rating_5')
        //         ->where('product_id', $product_id)
        //         ->get()
        //         ->toArray();

        //     if ($total_images != []) {
        //         $res['total_images'] = $total_rating[0]['no_of_rating'];
        //     }
        //     $res['total_images'] = !empty($total_rating) ? $total_rating[0]['no_of_rating'] : '';
        //     $res['total_images'] = !empty($total_images) ? $total_images[0]['total'] : '';
        //     $res['total_reviews_with_images'] = !empty($total_review_with_images) ? $total_review_with_images[0]['total'] : '';
        //     $res['no_of_rating'] = !empty($total_rating) ? $total_rating[0]['no_of_rating'] : '';
        //     $res['total_reviews'] = !empty($total_reviews) ? $total_reviews[0]['total'] : '';
        //     $res['star_1'] = !empty($total_reviews) ? $total_reviews[0]['rating_1'] : '';
        //     $res['star_2'] = !empty($total_reviews) ? $total_reviews[0]['rating_2'] : '';
        //     $res['star_3'] = !empty($total_reviews) ? $total_reviews[0]['rating_3'] : '';
        //     $res['star_4'] = !empty($total_reviews) ? $total_reviews[0]['rating_4'] : '';
        //     $res['star_5'] = !empty($total_reviews) ? $total_reviews[0]['rating_5'] : '';
        //     $res['product_rating'] = $rating_data;

        //     return $res;
    }
}
