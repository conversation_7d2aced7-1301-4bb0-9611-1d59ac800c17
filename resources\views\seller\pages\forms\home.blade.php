@extends('seller/layout')
@section('title')
{{ labels('admin_labels.home', 'Home') }}
@endsection
@section('content')
<section class="main-content">
    <div class="d-flex row align-items-center">
        <div class="col-md-6 page-info-title">
            <h3>{{ labels('admin_labels.dashboard', 'Dashboard') }}</h3>
            <p class="sub_title">
                {{ labels('admin_labels.all_information_about_your_store', 'All Information About your Store') }}
            </p>
        </div>
        <div class="col-md-6 d-flex justify-content-end">
            <nav aria-label="breadcrumb" class="float-end">
                <ol class="breadcrumb">
                    <i class='bx bx-home-smile'></i>
                    <li class="breadcrumb-item"><a
                            href="{{ route('seller.home') }}">{{ labels('admin_labels.home', 'Home') }}</a>
                    </li>
                </ol>
            </nav>
        </div>
    </div>
    @if (!empty($paymentMessage))
    <div class="alert alert-warning d-flex justify-content-between align-items-center">
        <div>
            <i class="bi bi-exclamation-circle me-2"></i>
            {{ $paymentMessage['text'] }}
        </div>
        <a href="{{ $paymentMessage['route'] }}" class="btn btn-sm ">
            <i class="bi bi-credit-card"></i> {{ __('admin_labels.pay_now') }}
        </a>
    </div>
@endif

    <section class="dashboard overview-data">

        <!-- ============================================ Info cards ======================================== -->
        <div class="row">
            <div class="col-xxl-12">
                <div class="row cols-5 d-flex">

                    <!-- Total Offers -->
                    <div class="col-md-6 col-xl-4 seller_statistics_card">
                        <div class="info-box align-items-center">

                            <div class="content">
                                <p class="body-default">{{ labels('admin_labels.total_offers', 'Total Offers') }}</p>
                                <h5>{{ $totalOffer }}</h5>
                            </div>
                        </div>
                    </div>

                    <!-- Claimed Offers -->
                    <div class="col-md-6 col-xl-4 seller_statistics_card">
                        <div class="info-box align-items-center">

                            <div class="content">
                                <p class="body-default">{{ labels('admin_labels.claimed_offers', 'Claimed Offers') }}</p>
                                <h5>{{ count_offer_claims($store_id) }}</h5>
                            </div>
                        </div>
                    </div>

                    <!-- Expired Offers -->
                    <div class="col-md-6 col-xl-4 seller_statistics_card">
                        <div class="info-box align-items-center">

                            <div class="content">
                                <p class="body-default">{{ labels('admin_labels.expired_offers', 'Expired Offers') }}</p>
                                <h5>{{ count_expired_offers($store_id) }}</h5>
                            </div>
                        </div>
                    </div>

                    <!-- Redeemed Offers -->
                    <div class="col-md-6 col-xl-4 seller_statistics_card">
                        <div class="info-box align-items-center">

                            <div class="content">
                                <p class="body-default">{{ labels('admin_labels.redeemed_offers', 'Redeemed Offers') }}</p>
                                <h5>{{ count_redeemed_offers($store_id) }}</h5>
                            </div>
                        </div>
                    </div>

                    <!-- Offer Views -->
                    <div class="col-md-6 col-xl-4 seller_statistics_card">
                        <div class="info-box align-items-center">

                            <div class="content">
                                <p class="body-default">{{ labels('admin_labels.total_offer_views', 'Offer Views') }}</p>
                                <h5>{{ get_total_offer_views($store_id) }}</h5>
                            </div>
                        </div>
                    </div>

                    <!-- Active Offers -->
                    <div class="col-md-6 col-xl-4 seller_statistics_card">
                        <div class="info-box align-items-center">

                            <div class="content">
                                <p class="body-default">{{ labels('admin_labels.active_offers', 'Active Offers') }}</p>
                                <h5>{{ get_active_offers($store_id) }}</h5>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>


        <!-- ============================================ Statistic Overview  ======================================== -->
        <section class="overview-statistic">


            <div class="row">
                <div class="col-xxl-12">
                    <div class="row cols-5 d-flex">
                        <div class="col col-xxl-7">
                            <div class="chart-card">
                                <div class="align-items-center chart-card-header d-flex justify-content-between">
                                    <h4>{{ labels('admin_labels.overview_statistics', 'Overview Statistic') }}</h4>
                                    <ul class="nav nav-pills nav-pills-rounded chart-action float-right btn-group sale-tabs"
                                        role="group">
                                        <li><a href="#Monthly" class="active">{{ labels('admin_labels.monthly', 'Monthly') }}</a></li>
                                        <li><a href="#Weekly">{{ labels('admin_labels.weekly', 'Weekly') }}</a></li>
                                        <li><a href="#Yearly">{{ labels('admin_labels.yearly', 'Yearly') }}</a></li>
                                    </ul>
                                </div>
                                <div id="chart" class="seller_statistic_chart">
                                </div>
                            </div>
                        </div>
                        <div class="col col-xxl-5">
                            <div class="chart-card">
                                <div class="align-items-center chart-card-header d-flex justify-content-between">
                                    <h4>{{ labels('admin_labels.most_claims_category', 'Most Claims Category') }}
                                    </h4>
                                    <div class="d-flex">
                                        <select class="form-select " id="most_selling_category_filter">
                                            <option value="weekly">{{ labels('admin_labels.weekly', 'Weekly') }}</option>
                                            <option value="monthly">{{ labels('admin_labels.monthly', 'Monthly') }}</option>
                                            <option value="yearly">{{ labels('admin_labels.yearly', 'Yearly') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div id="chart" class="seller_most_claims_category_chart">
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

        </section>

        <section class="customer-review-statistic">


            <div class="row">
                <div class="col-xxl-12">
                    <div class="row cols-5 d-flex">
                        <div class="col col-xxl-3">
                            <div class="chart-card p-0">
                                <div class="chart-card-header d-flex justify-content-between p-5">
                                    <h4>{{ labels('admin_labels.customer_rating', 'Customer Rating') }}</h4>
                                </div>

                                <div class="mt-5 p-5">
                                    <p class="font-display-4 text-center">
                                        {{ isset($avgRating) ? $avgRating : '' }}
                                    </p>
                                    <div class="d-flex justify-content-around justify-content-lg-center">
                                        <div id=""
                                            data-rating="{{ isset($avgRating) ? $avgRating : '' }}"
                                            data-rateyo-read-only="true" class="rateYo px-23"></div>
                                    </div>
                                </div>



                                <div id="chart" class="customer_rating_chart">
                                </div>

                            </div>
                        </div>

                        <div class="col col-xxl-6 recent-review">
                            <div class="chart-card">
                                <div class="chart-card-header d-flex justify-content-between">
                                    <h4>{{ labels('admin_labels.recent_reviews', 'Recent Reviews') }}</h4>
                                </div>
                                <!-- Carousel -->
                                <div id="customer_review" class="carousel slide" data-bs-ride="carousel">
                                    <div class="carousel-inner">
                                        @php $i = 0; @endphp
                                        @foreach ($latestRatings as $row)
                                        <div class="carousel-item {{ $i == 0 ? 'active' : '' }}">
                                            <div class="d-flex mt-5">
                                                <div class=" col-md-2">
                                                    <img src="
                                                        {{ getMediaImageUrl($row->offer->images->first()->image_url)}}
                                                        "
                                                        class="w-100" alt="">
                                                </div>
                                                <div class="col-md-10 ms-3 pt-2">
                                                    <div class="justify-content-between">
                                                        <p class="m-0 lead">{{ $row->offer->name }}</p>
                                                        <h6 class="product-price m-0 mt-1">
                                                            {{ $row->offer->discounted_price > 0 }} د.ل
                                                        </h6>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="customer-review mt-5">
                                                <div class="d-flex">
                                                    
                                                    <div class="col-md-9 ms-3">
                                                        <div class="justify-content-between">
                                                            <h6 class="m-0 customer-name">{{ $row->user?->username }}
                                                            </h6>
                                                            <h6 class="m-0 mt-1">
                                                                <div id="" data-rating="{{ $row->rating }}"
                                                                    data-rateyo-read-only="true"
                                                                    class="rateYo bookrating"></div>
                                                            </h6>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr class="border-1 border-dashed border-light">
                                                <div class="customer-review-text">
                                                    <p>{{ $row->review }}</p>
                                                </div>

                                            </div>
                                        </div>
                                        @php $i++; @endphp
                                        @endforeach
                                    </div>

                                    <!-- Left and right controls/icons -->

                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>

        </section>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        $(document).ready(function() {
            setTimeout(function() {

                    const data = {
                        Monthly: {
                            series: [{
                                    name: 'قسائم العروض',
                                    data: <?php echo json_encode($codesData['monthly']['codes']); ?>
                                },
                                {
                                    name: 'القسائم التي تم جمعها',
                                    data: <?php echo json_encode($codesData['monthly']['claims']); ?>
                                }
                            ],
                            categories: <?php echo json_encode($codesData['monthly']['labels']); ?>
                        },
                        Weekly: {
                            series: [{
                                    name: 'قسائم العروض',
                                    data: <?php echo json_encode($codesData['weekly']['codes']); ?>
                                },
                                {
                                    name: 'القسائم التي تم جمعها',
                                    data: <?php echo json_encode($codesData['weekly']['claims']); ?>
                                }
                            ],
                            categories: <?php echo json_encode($codesData['weekly']['labels']); ?>
                        },
                        Yearly: {
                            series: [{
                                    name: 'قسائم العروض',
                                    data: <?php echo json_encode($codesData['yearly']['codes']); ?>
                                },
                                {
                                    name: 'القسائم التي تم جمعها',
                                    data: <?php echo json_encode($codesData['yearly']['claims']); ?>
                                }
                            ],
                            categories: <?php echo json_encode($codesData['yearly']['labels']); ?>
                        }
                    };

                let chartData = data['Monthly']; // default tab can be Monthly, Weekly, or Yearly

                const options = {
                    chart: {
                        type: 'bar',
                        height: 350
                    },
                    plotOptions: {
                        bar: {
                            horizontal: false,
                            columnWidth: '55%',
                            endingShape: 'rounded'
                        },
                    },
                    series: chartData.series,
                    dataLabels: {
                        enabled: false
                    },
                    stroke: {
                        show: true,
                        width: 2,
                        colors: ['transparent']
                    },
                    xaxis: {
                        categories: chartData.categories
                    },
                    yaxis: {
                        title: {
                            text: 'العدد'
                        }
                    },
                    fill: {
                        opacity: 1,
                    },
                    tooltip: {
                        y: {
                            formatter: function(val) {
                                return val + ' ';
                            }
                        }
                    }
                };

                const chart = new ApexCharts(document.querySelector(".seller_statistic_chart"), options);
                chart.render();

                // Change chart on tab click
                $(".overview-statistic .chart-action li a").on("click", function() {
                    $(".chart-action li a").removeClass('active');
                    $(this).addClass('active');

                    chartData = data[$(this).attr("href").replace('#', '')];

                    chart.updateOptions({
                        series: chartData.series,
                        xaxis: {
                            categories: chartData.categories
                        }
                    });
                });


                // ====================================== Most selling category chart =======================================


                // monthly data
                var monthlyTotalSold = <?php echo json_encode($topCliamedCategories[0]['totalClaims']); ?>;
                var monthlyTotalSoldsum = monthlyTotalSold.reduce(
                    (total, value) => total + value,
                    0
                );
                var monthlyTotalSoldpercentages = monthlyTotalSold.map(
                    (value) => (value / monthlyTotalSoldsum) * 100
                );

                // yearly data
                var yearlyTotalSold = <?php echo json_encode($topCliamedCategories[1]['totalClaims']); ?>;
                var yearlyTotalSoldsum = yearlyTotalSold.reduce(
                    (total, value) => total + value,
                    0
                );
                var yearlyTotalSoldpercentages = yearlyTotalSold.map(
                    (value) => (value / yearlyTotalSoldsum) * 100
                );


                // weekly data

                var weeklyTotalSold = <?php echo json_encode($topCliamedCategories[2]['totalClaims']); ?>;

                var weeklyTotalSoldsum = weeklyTotalSold.reduce(
                    (total, value) => total + value,
                    0
                );
                var weeklyTotalSoldpercentages = weeklyTotalSold.map(
                    (value) => (value / weeklyTotalSoldsum) * 100
                );

                const data1 = {
                    yearly: {
                        series: yearlyTotalSoldpercentages,
                        categories: <?php echo !empty($topCliamedCategories[1]['categoryNames']) ? json_encode($topCliamedCategories[1]['categoryNames']) : '[]'; ?>,
                        originalValues: yearlyTotalSold
                    },
                    monthly: {
                        series: monthlyTotalSoldpercentages,
                        categories: <?php echo !empty($topCliamedCategories[0]['categoryNames']) ? json_encode($topCliamedCategories[0]['categoryNames']) : '[]'; ?>,
                        originalValues: monthlyTotalSold
                    },
                    weekly: {
                        series: weeklyTotalSoldpercentages,
                        categories: <?php echo !empty($topCliamedCategories[2]['categoryNames']) ? json_encode($topCliamedCategories[2]['categoryNames']) : '[]'; ?>,
                        originalValues: weeklyTotalSold
                    }
                };


                let catChartData = data1['weekly'];


                const catOptions = {
                    series: catChartData.series,
                    labels: catChartData.categories,
                    colors: ["#008FFB", "#00E396", "#FEB019", "#FF4560", "#775DD0"],
                    chart: {
                        height: 408,
                        type: "donut",
                    },
                    plotOptions: {
                        pie: {
                            startAngle: -90,
                            endAngle: 270,
                        },
                    },
                    dataLabels: {
                        enabled: false,
                    },
                    fill: {
                        type: "gradient",
                    },
                    legend: {
                        position: "bottom",
                        formatter: function(val, opts) {
                            return catChartData.categories[opts.seriesIndex];
                        },
                    },
                    tooltip: {
                        y: {
                            formatter: function(val, opts) {
                                return catChartData.categories[opts.seriesIndex] + " - " +
                                    catChartData.originalValues[opts.seriesIndex];
                            },
                        },
                    },
                    responsive: [{
                        breakpoint: 480,
                        options: {
                            chart: {
                                width: 200,
                            },
                            legend: {
                                position: "bottom",
                            },
                        },
                    }],
                };

                const catChart = new ApexCharts(document.querySelector(
                    ".seller_most_claims_category_chart"), catOptions);
                catChart.render();

                $("#most_selling_category_filter").on("change", function() {

                    catChartData = data1[$(this).val()];
                    console.log('bd')
                    console.log(catChart);
                    catChart.updateOptions({
                        series: catChartData.series,
                        legend: {
                            position: "bottom",
                            formatter: function(val, opts) {
                                // Use categoryNames instead of opts.w.globals.series
                                return catChartData.categories[
                                    opts.seriesIndex
                                ];
                            },
                        },
                        tooltip: {
                            y: {
                                formatter: function(val, opts) {
                                    return (
                                        catChartData.categories[
                                            opts.seriesIndex
                                        ] +
                                        " - " +
                                        catChartData.originalValues[opts
                                            .seriesIndex]
                                    );
                                },
                            },
                        },
                    });
                });

                // ====================================== Customer Rating chrt ===========================================


                var ratingOptions = {
                    series: [{
                        name: 'Rating',
                        data: <?php echo json_encode($store_rating['total_ratings']); ?>,
                    }, ],
                    colors: ["#FEB019"],
                    chart: {
                        height: 208,
                        type: "area",
                        zoom: {
                            enabled: false,
                        },
                        toolbar: {
                            show: true,
                            tools: {
                                download: false,
                            },
                        },
                    },
                    dataLabels: {
                        enabled: false,
                    },
                    stroke: {
                        curve: "smooth",
                    },
                    grid: {
                        show: false,
                    },
                    yaxis: {
                        labels: {
                            show: false,
                        },
                        lines: {
                            show: false,
                        },
                    },
                    xaxis: {
                        labels: {
                            show: false,
                        },
                        axisBorder: {
                            show: false,
                        },
                        axisTicks: {
                            show: false,
                        },
                        categories: <?php echo json_encode($store_rating['month_name']); ?>,
                    },

                    tooltip: {
                        custom: function({
                            series,
                            seriesIndex,
                            dataPointIndex,
                            w
                        }) {
                            var monthAbbreviation = <?php echo json_encode($store_rating['month_name']); ?>[dataPointIndex];
                            var ratingValue = w.globals.series[seriesIndex][dataPointIndex];
                            return '<div><h6 class="text-center p-1"> ' + monthAbbreviation +
                                ' </h6> <hr><span class="p-2"> Rating: ' + ratingValue +
                                ' </span> </div>'

                        },
                    },
                };

                var ratingChart = new ApexCharts(
                    document.querySelector(".customer_rating_chart"),
                    ratingOptions
                );
                ratingChart.render();

            }, 200);
        });
    </script>
    @endsection