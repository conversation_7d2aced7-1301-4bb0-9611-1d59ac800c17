// user login form
$(".form_authentication").submit(function (e) {
    e.preventDefault();
    var form = $(this);
    var url = form.attr("action");
    var method = form.attr("method");
    var submit_btn = $(".login_button");
    var btn_html = $(".login_button").html();
    var btn_val = $(".login_button").val();
    var button_text =
        btn_html != "" || btn_html != "undefined" ? btn_html : btn_val;
    var data = form.serialize();
    var token = $('meta[name="csrf-token"]').attr("content");
    $.ajax({
        url: url,
        method: method,
        data: data,
        beforeSend: function () {
            submit_btn.html("..الرجاء الانتظار");
            submit_btn.attr("disabled", true);
        },
        success: function (response) {
            // Determine the appropriate redirection URL based on the response
            var location = response.location || "";

            if (response.error_message) {
                if (Array.isArray(response.error_message)) {
                    response.error_message.forEach(function (errorMessage) {
                        iziToast.error({
                            title: "Error",
                            message: errorMessage,
                            position: "topRight",
                        });
                    });
                } else {
                    iziToast.error({
                        title: "Error",
                        message: response.error_message,
                        position: "topRight",
                    });
                }

                submit_btn.html(button_text);
                submit_btn.attr("disabled", false);
            }

            if (response.message) {
                // Success message received
                submit_btn.html(button_text);
                submit_btn.attr("disabled", false);
                iziToast.success({
                    title: "Success",
                    message: response.message,
                    position: "topRight",
                });
                setTimeout(function () {
                    // Redirect to the appropriate URL
                    window.location.href = location;
                }, 1000);
            } else if (response.errors && response.errors.role) {
                // Role-based error message received
                iziToast.error({
                    title: "Error",
                    message: response.errors.role[0],
                    position: "topRight",
                });
                token;
                submit_btn.html(button_text);
                submit_btn.attr("disabled", false);
            }
            token;
            // Redirect to the appropriate URL
        },
        error: function (xhr) {
            var errors = xhr.responseJSON.errors;
            for (var key in errors) {
                if (errors.hasOwnProperty(key)) {
                    var errorMessages = errors[key];
                    for (var i = 0; i < errorMessages.length; i++) {
                        iziToast.error({
                            title: "Error",
                            message: errorMessages[i],
                            position: "topRight",
                        });
                    }
                    token;
                    submit_btn.html(button_text);
                    submit_btn.attr("disabled", false);
                }
            }
        },
    });
});



/////

$(function () {
    // Function to toggle password visibility
    $(".toggle_new_password").click(function () {
        var input = $(".show_new_password");
        var icon = $(this).find("i");
        var type = input.attr("type") == "password" ? "text" : "password";
        input.attr("type", type);
        icon.toggleClass("bx-show bx-low-vision");
    });
});

function validateNumberInput(input) {
    // Remove any non-numeric characters from the input value
    input.value = input.value.replace(/\D/g, "");
}

function show_password() {
    var passwordInput = $("#show_password");
    var eyeIcon = $(".password_show");
    var lowVisionIcon = $(".low_vision");

    if (passwordInput.attr("type") === "password") {
        passwordInput.attr("type", "text");
        eyeIcon.addClass("d-none");
        lowVisionIcon.removeClass("d-none");
    } else {
        passwordInput.attr("type", "password");
        eyeIcon.removeClass("d-none");
        lowVisionIcon.addClass("d-none");
    }
}
$(function () {
    var eyeIcon = $(".password_show");
    eyeIcon.addClass("d-none");
});

function copyCombinedInfo() {
    var mobileInfo = $("#mobileInfo").text();
    var passwordInfo = $("#passwordInfo").text();

    // Display combinedInfo in mobileInput and passwordInput
    $(".copied_mobile").val(mobileInfo);
    $(".copied_password").val(passwordInfo);
}

iziToast.settings({
    position: "topRight",
});

var appUrl = document.getElementById("app_url").dataset.appUrl;
var from = "admin";
if (
    window.location.href.indexOf("seller/") > -1 &&
    window.location.href.indexOf("admin/") == -1
) {
    from = "seller";
}
if (window.location.href.indexOf("delivery_boy/") > -1) {
    from = "delivery_boy";
}
$("#validationForm").submit(function (e) {
    e.preventDefault(); // Prevent form submission

    var form = $(this);
    var url = form.attr("action");
    var method = form.attr("method");
    var formData = new FormData(form[0]);

    // Clear previous error messages
    $(".text-danger").empty();
    iziToast.destroy();

    // Validate other fields

    $.ajax({
        url: url,
        type: method,
        data: formData,
        processData: false,
        contentType: false,
        success: function (response) {
            // Handle successful response
            iziToast.success({
                title: "Success",
                message: response.message,
                position: "topRight",
            });
            // Reload the page or perform other actions as needed
            $("#validationForm")[0].reset();
            setTimeout(function () {
                window.location.reload();
            }, 3000);
        },
        error: function (xhr) {
            // Handle error response
            if (xhr.status === 422) {
                var errors = xhr.responseJSON.errors;
                // Display validation errors
                $.each(errors, function (field, errorMessages) {
                    $.each(errorMessages, function (index, errorMessage) {
                        $("#" + field)
                            .siblings(".text-danger")
                            .append("<p>" + errorMessage + "</p>");
                    });
                });
                var message =
                    xhr.responseJSON.message || "Validation error(s) occurred.";
                iziToast.error({
                    title: "Error",
                    message: message,
                    position: "topRight",
                });
            } else {
                // Handle other types of errors
                var message = xhr.responseJSON.message || "An error occurred.";
                iziToast.error({
                    title: "Error",
                    message: message,
                    position: "topRight",
                });
            }
        },
    });
});

// general event for form submit

$(".submit_form").on("submit", function (e) {
    e.preventDefault(); // Prevent the default form submission

    var form = $(this);
    var url = form.attr("action");
    var method = form.attr("method");

    var submit_btn = form.find(".submit_button"); // scoped to this form only
    var btn_html = $(".submit_button").html();
    var btn_val = $(".submit_button").val();
    var button_text =
        btn_html != "" || btn_html != "undefined" ? btn_html : btn_val;

    var formData = new FormData(form[0]);
    var csrfToken = document.head.querySelector(
        'meta[name="csrf-token"]'
    ).content;
    formData.append("_token", csrfToken);
    // Clear previous error messages
    $(".text-danger").text("");

    $.ajax({
        url: url,
        method: method,
        data: formData,
        beforeSend: function () {
            submit_btn.prop("disabled", true);
            submit_btn.html(`
                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>  
            `);
        },
        contentType: false,
        processData: false,
        success: function (response) {
            var location = response.location || "";
            token = $('meta[name="csrf-token"]').attr("content");
            if (response.error_message) {
                submit_btn.html(button_text);
                submit_btn.attr("disabled", false);
                iziToast.error({
                    title: "",
                    message: response.error_message,
                    position: "topRight",
                });
            } else {
                submit_btn.html(button_text);
                submit_btn.attr("disabled", false);
                iziToast.success({
                    title: "",
                    message: response.message,
                    position: "topRight",
                });
                form[0].reset();
                setTimeout(function () {
                    // Redirect to the appropriate URL
                    window.location.href = location;
                }, 4000);
                $(".product-image-container").remove();
            }

            if (
                response.addAttribute != undefined &&
                response.addAttribute == true
            ) {
                var lastDiv = $(
                    "#attributes_process > div.product-attr-selectbox:last"
                );
                $(lastDiv).empty();

                $(".edit-product-attributes").trigger("click");
            }
            $(".table").bootstrapTable("refresh");
            $(".modal").modal("hide");
            $("#sendMailModal").modal("hide");
            $(".search_stores").empty();
        },
        error: function (xhr, status, error) {
            submit_btn.html(button_text);
            submit_btn.attr("disabled", false);
            if (xhr.responseJSON && xhr.responseJSON.errors) {
                var errors = xhr.responseJSON.errors;

                // Display each error message in a separate toast
                $.each(errors, function (field, errorMessages) {
                    if (Array.isArray(errorMessages)) {
                        $.each(errorMessages, function (index, errorMessage) {
                            iziToast.error({
                                title: "",
                                message: errorMessage,
                                position: "topRight",
                            });
                        });
                    } else {
                        iziToast.error({
                            title: "",
                            message: errorMessages,
                            position: "topRight",
                        });
                    }
                });
            } else {
                iziToast.error({
                    title: "",
                    message: xhr.responseJSON.message,
                    position: "topRight",
                });
            }
        },
    });
});




// change status active or deactive in bootstrap table

$(document).on("change", ".change_toggle_status", function () {
    var id = $(this).data("id");
    var status = $(this).val();
    var url = $(this).data("url");
    $.ajax({
        method: "GET",
        url: url,
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            status: status,
        },
        success: function (response) {
            if (response.error_message) {
                iziToast.error({
                    title: "Error",
                    message: response.error_message,
                    position: "topRight",
                });
                $(".table").bootstrapTable("refresh");
            }
            if (response.status_error) {
                iziToast.error({
                    title: "Error",
                    message: response.status_error,
                    position: "topRight",
                });
                $(".table").bootstrapTable("refresh");
            }
            if (response.success) {
                iziToast.success({
                    title: "",
                    message: "تم التحديث بنجاح",
                    position: "topRight",
                });
                $(".table").bootstrapTable("refresh");
            }
        },
        fail: function (response) {
            iziToast.error({
                title: "",
                message: "Something Went Wrong!!",
                position: "topRight",
            });
        },
    });
});

// general event for deleet bootstrap table data

$(document).on("click", ".delete-data", function (event) {
    event.preventDefault(); // Prevent the default behavior of the link

    var url = $(this).data("url");

    var subString = "media/destroy";

    Swal.fire({
        title: "هل انت متاكد؟",
        text: "لن تتمكن من استعادة البيانات!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "نعم, احذف!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    method: "GET", // Change the method to DELETE
                    url: url,
                    data: {
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },
                    dataType: "json",
                })
                    .done(function (response, textStatus) {
                        if (response.error == false) {
                            Swal.fire("تم الحذف!", response.message, "success");
                            setTimeout(function () {
                                window.location.reload();
                            }, 1000);
                            $("table").bootstrapTable("refresh");
                            csrfName = response["csrfName"];
                            csrfHash = response["csrfHash"];
                        } else {
                            if (response.error_message) {
                                Swal.fire(
                                    "خطأ!",
                                    response.error_message,
                                    "error"
                                );
                            } else {
                                Swal.fire("عفوا...", response.error, "warning");
                            }
                            // Swal.fire("Oops...", response.error, "warning");
                            $("table").bootstrapTable("refresh");
                            csrfName = response["csrfName"];
                            csrfHash = response["csrfHash"];
                        }
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        Swal.fire(
                            "عفوا...",
                            "حدث خطأ أثناء الحذف ",
                            "error"
                        );
                        csrfName = response["csrfName"];
                        csrfHash = response["csrfHash"];
                    });
            });
        },
        allowOutsideClick: false,
    });
});

$(function () {
    promo_code_repeat_usage();
    $("#similar_product").addClass("d-none");
    $(document).on("change", "#repeat_usage", function () {
        promo_code_repeat_usage();
    });

    function promo_code_repeat_usage() {
        var repeat_usage = $("#repeat_usage").val();
        var no_of_repeat_usage = $("#no_of_repeat_usage").val();

        if (
            repeat_usage === "1" ||
            (repeat_usage !== "0" && no_of_repeat_usage !== "")
        ) {
            $("#repeat_usage_html").removeClass("d-none");
        } else {
            $("#repeat_usage_html").addClass("d-none");
        }
    }
});


// query params

function seller_wallet_query_params(p) {
    return {
        transaction_type: "wallet",
        user_type: "seller",
        limit: p.limit,
        sort: p.sort,
        order: p.order,
        offset: p.offset,
        search: p.search,
        start_date: p.start_date,
        end_date: p.end_date,
    };
}

function home_query_params(p) {
    return {
        start_date: $("#start_date").val(),
        end_date: $("#end_date").val(),
        order_status: $("#order_status").val(),
        payment_method: $("#payment_method").val(),
        limit: p.limit,
        sort: "oi.id",
        order: p.order,
        offset: p.offset,
        search: p.search,
    };
}

function brand_query_params(p) {
    return {
        brand_id: $("#brand_id").val(),
        limit: p.limit,
        sort: p.sort,
        order: p.order,
        offset: p.offset,
        search: p.search,
        status: p.status,
    };
}


function PromoqueryParams(p) {
    return {
        limit: p.limit,
        sort: p.sort,
        order: p.order,
        offset: p.offset,
        search: p.search,
        status: p.status,
    };
}

function category_query_params(p) {
    return {
        category_id: $("#category_id").val(),
        status: p.status,
        limit: p.limit,
        sort: p.sort,
        order: p.order,
        offset: p.offset,
        search: p.search,
    };
}

function store_query_params(p) {
    return {
        store_id: $("#store_id").val(),
        status: p.status,
        limit: p.limit,
        sort: p.sort,
        order: p.order,
        offset: p.offset,
        search: p.search,
    };
}

function blog_query_params(p) {
    return {
        category_id: p.blogCategoryId,
        limit: p.limit,
        sort: p.sort,
        order: p.order,
        offset: p.offset,
        search: p.search,
    };
}

function cash_collection_query_params(p) {
    return {
        filter_date: $("#filter_date").val(),
        filter_status: p.cashCollectionType,
        filter_d_boy: p.deliveryBoyFilter,
        start_date: p.start_date,
        end_date: p.end_date,
        limit: p.limit,
        sort: p.sort,
        order: p.order,
        offset: p.offset,
        search: p.search,
    };
}

function address_query_params(p) {
    return {
        user_id: $("#address_user_id").val(),
        limit: p.limit,
        sort: p.sort,
        order: p.order,
        offset: p.offset,
        search: p.search,
    };
}

function transaction_query_params(p) {
    return {
        transaction_type: "transaction",
        user_id: $("#transaction_user_id").val(),
        limit: p.limit,
        sort: p.sort,
        order: p.order,
        offset: p.offset,
        search: p.search,
        start_date: p.start_date,
        end_date: p.end_date,
    };
}

function queryParams(p) {
    return {
        limit: p.limit,
        sort: p.sort,
        order: p.order,
        offset: p.offset,
        search: p.search,
        order_status: p.order_status,
        active_status: p.order_status,
        start_date: p.start_date,
        end_date: p.end_date,
        payment_method: p.payment_method,
        status: p.status,
        category_id: p.category_id,
        offer_type: p.offer_type,
        brand_id: p.brand_id,
        payment_request_status: p.payment_request_status,
        advertisment_status: p.advertisment_status,
        productStatus: p.productStatus,
        seller_id: p.seller_id,
        user_id: p.user_id,
    };
}

$(document).on("show.bs.modal", "#customer-address-modal", function (event) {
    var triggerElement = $(event.relatedTarget);
    current_selected_image = triggerElement;
    var id = $(current_selected_image).data("id");
    var existing_url = $(this).find("#customer-address-table").data("url");

    if (existing_url.indexOf("?") > -1) {
        var temp = $(existing_url).text().split("?");
        var new_url = temp[0] + "?user_id=" + id;
    } else {
        var new_url = existing_url + "?user_id=" + id;
    }
    $("#address_user_id").val(id);
    $("#customer-address-table").bootstrapTable("refreshOptions", {
        url: new_url,
    });
});



$("#swatche_color").hide();
$("#swatche_image").hide();
$(document.body).on("change", ".swatche_type", function (e) {
    e.preventDefault();
    var swatche_type = $(this).val();
    if (swatche_type == "1") {
        $("#swatche_image").hide();
        $("#swatche_color").show();
        $("#swatche_image").val("");
    } else if (swatche_type == "2") {
        $("#swatche_color").hide();
        $("#swatche_image").show();
        $("#swatche_color").val("");
    } else {
        $("#swatche_color").hide();
        $("#swatche_image").hide();
        $("#swatche_color").val("");
        $("#swatche_image").val("");
    }
});

$(document).on("change", ".swatche_type", function () {
    if ($(this).val() == "1") {
        $(this).siblings(".color_picker").show();
        $(this).siblings(".upload_media").hide();
        $(this).siblings(".grow").hide();
    }
    if ($(this).val() == "2") {
        $(this).siblings(".color_picker").hide();
        $(this).siblings(".color_picker").attr("name", null);
        $(this).siblings(".upload_media").show();
        $(this).siblings(".grow").show();
    }
    if ($(this).val() == "0") {
        $(".color_picker").hide();
        $(".upload_media").hide();
        $(".grow").hide();
    }
});


$(document).on("change", ".type_event_trigger", function (e, data) {
    e.preventDefault();
    var type_val = $(this).val();
    if (type_val != "default" && type_val != " ") {
        if (type_val == "categories") {
            $(".slider-categories").removeClass("d-none");
            $(".slider-combo-products").addClass("d-none");
            $(".notification-categories").removeClass("d-none");
            $(".slider-products").addClass("d-none");
            $(".notification-products").addClass("d-none");
            $(".slider-url").addClass("d-none");
            $(".offer-url").addClass("d-none");
            $(".notification-url").addClass("d-none");
        } else if (type_val == "products") {
            $(".slider-products").removeClass("d-none");
            $(".notification-products").removeClass("d-none");
            $(".slider-categories").addClass("d-none");
            $(".slider-combo-products").addClass("d-none");
            $(".notification-categories").addClass("d-none");
            $(".offer-url").addClass("d-none");
            $(".slider-url").addClass("d-none");
            $(".notification-url").addClass("d-none");
            $(".slider-brand").addClass("d-none");
        } else if (type_val == "combo_products") {
            $(".slider-combo-products").removeClass("d-none");
            $(".notification-products").removeClass("d-none");
            $(".slider-categories").addClass("d-none");
            $(".slider-products").addClass("d-none");
            $(".notification-categories").addClass("d-none");
            $(".offer-url").addClass("d-none");
            $(".slider-url").addClass("d-none");
            $(".notification-url").addClass("d-none");
            $(".slider-brand").addClass("d-none");
        } else if (type_val == "slider_url") {
            $(".slider-url").removeClass("d-none");
            $(".slider-combo-products").addClass("d-none");
            $(".slider-categories").addClass("d-none");
            $(".notification-categories").addClass("d-none");
            $(".slider-products").addClass("d-none");
            $(".offer-url").removeClass("d-none");
            $(".notification-products").addClass("d-none");
            $(".notification-url").addClass("d-none");
        } else if (type_val == "offer_url") {
            $(".offer-url").removeClass("d-none");
            $(".slider-combo-products").addClass("d-none");
            $(".slider-categories").addClass("d-none");
            $(".notification-categories").addClass("d-none");
            $(".slider-products").addClass("d-none");
            $(".notification-products").addClass("d-none");
            $(".notification-url").addClass("d-none");
            $(".slider-brand").addClass("d-none");
        } else if (type_val == "notification_url") {
            $(".notification-url").removeClass("d-none");
            $(".slider-combo-products").addClass("d-none");
            $(".offer-url").addClass("d-none");
            $(".slider-categories").addClass("d-none");
            $(".notification-categories").addClass("d-none");
            $(".slider-products").addClass("d-none");
            $(".notification-products").addClass("d-none");
        } else if (type_val == "all_products") {
            $(".slider-combo-products").addClass("d-none");
            $(".slider-all-products").removeClass("d-none");
            $(".notification-all-products").removeClass("d-none");
            $(".slider-categories").addClass("d-none");
            $(".notification-categories").addClass("d-none");
            $(".slider-products").addClass("d-none");
            $(".notification-products").addClass("d-none");
            $(".slider-brand").addClass("d-none");
            $(".offer-url").addClass("d-none");
        } else if (type_val == "brand") {
            $(".slider-combo-products").addClass("d-none");
            $(".slider-all-products").removeClass("d-none");
            $(".notification-all-products").removeClass("d-none");
            $(".slider-categories").addClass("d-none");
            $(".notification-categories").addClass("d-none");
            $(".slider-brand").removeClass("d-none");
            $(".notification-products").removeClass("d-none");
            $(".slider-products").addClass("d-none");
            $(".offer-url").addClass("d-none");
        }
    } else {
        $(".slider-categories").addClass("d-none");
        $(".slider-url").addClass("d-none");
        $(".slider-products").addClass("d-none");
        $(".offer-url").addClass("d-none");
        $(".notification-categories").addClass("d-none");
        $(".notification-products").addClass("d-none");
        $(".notification-url").addClass("d-none");
        $(".slider-brand").addClass("d-none");
    }
});

$(document).on("change", "#send_to", function (e) {
    e.preventDefault();
    var type_val = $(this).val();
    if (type_val == "specific_user") {
        $(".notification-users").removeClass("d-none");
    } else {
        $(".notification-users").addClass("d-none");
    }
});
$(document).on("change", "#send_seller_notification", function (e) {
    e.preventDefault();
    var type_val = $(this).val();
    console.log(type_val);
    if (type_val == "specific_seller") {
        $(".notification-sellers").removeClass("d-none");
    } else {
        $(".notification-sellers").addClass("d-none");
    }
});

var noti_user_id = 0;
$("#select_user_id").on("change", function () {
    noti_user_id = $("#select_user_id").val();
});

// search user

$(".search_user").each(function () {
    $(this).select2({
        ajax: {
            url: appUrl + "admin/user/search_user",
            type: "GET",
            dataType: "json",
            delay: 250,
            data: function (params) {
                return {
                    search: params.term,
                };
            },
            processResults: function (response) {
                return {
                    results: response,
                };
            },
            cache: true,
        },
        minimumInputLength: 1,
        placeholder: "Search for countries",
    });
});
$(".search_seller").each(function () {
    $(this).select2({
        ajax: {
            url: appUrl + "admin/user/search_seller",
            type: "GET",
            dataType: "json",
            delay: 250,
            data: function (params) {
                return {
                    search: params.term,
                };
            },
            processResults: function (response) {
                return {
                    results: response,
                };
            },
            cache: true,
        },
        placeholder: "Search for sellers",
    });
});

if ($("#tags").length) {
    var tags_element = document.querySelector("input[name=tags]");
    new Tagify(tags_element);
}


// Handle storeCategoryForm form submission
$('#storeCategoryForm').on('submit', function (e) {
    e.preventDefault(); // Prevent regular form submit

    let formData = new FormData(this);

    $.ajax({
        url: appUrl + "seller/categories/update", // or your desired route
        method: 'POST',
        data: formData,
        processData: false, // Important for file uploads
        contentType: false,
        success: function (response) {
            iziToast.success({
                title: "نجحت العملية",
                message: "تم حفظ التغييرات بنجاح",
                position: "topRight",
            });
            window.location.href = response.location;
        },
        error: function (xhr) {

            iziToast.error({
                title: "خطا",
                message: "حدث خطأ ما! الرجاء المحاولة مرة أخرى.",
                position: "topRight",
            });

        }
    });
});


var cat_html = "";
var count_view = 0;
$(document).on("click", "#seller_model", function (e) {
    e.preventDefault();
    cat_html = $("#cat_html").html();

    var cat_ids = $(this).data("cat_ids") + ",";
    var cat_array = cat_ids.split(",");
    cat_array = cat_array.filter(function (v) {
        return v !== "";
    });
    cat_array.sort(function (a, b) {
        return a - b;
    });
    // console.log(cat_array);
    var seller_id = $(this).data("seller_id");

    if (
        cat_ids != "" &&
        cat_ids != "," &&
        cat_ids != "undefined" &&
        seller_id != "" &&
        seller_id != "undefined" &&
        count_view == 0
    ) {
        $.ajax({
            type: "POST",
            data: {
                id: seller_id,
                _token: $('meta[name="csrf-token"]').attr("content"),
            },
            url: appUrl + "admin/sellers/get_seller_commission_data",
            dataType: "json",
            success: function (result) {
                console.log(result);
                if (result.error === "false") {
                    var option_html = $("#cat_html").html();

                    result.data.forEach(function (e, i) {
                        var is_selected =
                            e.id == cat_array[i] && e.seller_id == seller_id
                                ? "selected"
                                : "";
                        console.log(is_selected);
                        if (is_selected == "") {
                            load_category_section(cat_html);
                        } else {
                            option_html +=
                                '<option value="' +
                                e.category_id +
                                '" ' +
                                is_selected +
                                ">" +
                                e.name +
                                "</option>";

                            load_category_section(
                                "",
                                true,
                                option_html,
                                e.commission
                            );
                        }
                    });
                }
            },
        });

        count_view = 1;
    } else {
        if (count_view == 0) {
            load_category_section(cat_html);
        }
        count_view = 1;
    }
});

$(document).on("click", "#add_category", function (e) {
    e.preventDefault();
    load_category_section(cat_html, false);
});

function load_packages() {
    console.log('ddd');
    $.ajax({
        type: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
        },
        url: appUrl + "admin/ads/get_packages_filter",
        dataType: "json",
        success: function (result) {
            var html =
                '<select id="ads_package_id" name="ads_package_id" class="form-select ">' +
                '<option value="">اختر الباقة</option>';
            result.forEach(function (e, i) {
                html += '<option value="' + e.id + '">' + e.title + "</option>";
            });
            html += "</select>";
            $(".get_filter_package").html(html);
        },
    });
}

function load_seller_category() {
    $.ajax({
        type: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
        },
        url: appUrl + from + "/categories/get_seller_categories_filter",
        dataType: "json",
        success: function (result) {
            var html =
                '<select id="category_id" name="category_id" class="form-select ">' +
                '<option value="">{{__("admin_labels.select_category")}}</option>';
            result.forEach(function (e, i) {
                html += '<option value="' + e.id + '">' + e.name + "</option>";
            });
            html += "</select>";
            $(".search_seller_category").html(html);
        },
    });
}
function load_blog_category() {
    $.ajax({
        type: "GET",
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
        },
        url: appUrl + "admin/blogs/get_blog_categories",
        dataType: "json",
        success: function (result) {
            var html =
                '<select id="blog_category_id" name="category_id" class="form-select ">' +
                '<option value="">Select category</option>';
            result.forEach(function (e, i) {
                html += '<option value="' + e.id + '">' + e.text + "</option>";
            });
            html += "</select>";
            $(".get_filter_blog_categories").html(html);
        },
    });
}

function load_category_section(
    cat_html,
    is_edit = false,
    option_html = "",
    commission = 0
) {
    if (is_edit == true) {
        var html =
            ' <div class="form-group  row">' +
            '<div class="col-sm-5 category_drop_down">' +
            '<select name="category_id" class="form-select select_multiple w-100 seller_modal_category" data-placeholder=" Select Category">' +
            '<option value="">Select Category </option>' +
            option_html +
            "</select>" +
            "</div>" +
            '<div class="col-sm-5">' +
            '<input type="number" step="any"  min="0" max="100" class="form-control"  placeholder="Enter Commission" name="commission" required value="' +
            commission +
            '">' +
            "</div>" +
            '<div class="col-sm-2"> ' +
            '<button type="button" class="btn btn-tool remove_category_section" > <i class="text-danger bx bx-trash fa-2x "></i> </button>' +
            "</div>" +
            "</div>" +
            "</div>";
    } else {
        var html =
            ' <div class="form-group row">' +
            '<div class="col-sm-5 category_drop_down">' +
            '<select name="category_id" class="form-select select_multiple w-100 test seller_modal_category" data-placeholder="Select Category">' +
            '<option value="">Select Category </option>' +
            cat_html +
            "</select>" +
            "</div>" +
            '<div class="col-sm-5">' +
            '<input type="number" step="any"  min="0" max="100" class="form-control"  placeholder="Enter Commission" name="commission"  value="0">' +
            "</div>" +
            '<div class="col-sm-2"> ' +
            '<button type="button" class="btn btn-tool remove_category_section" > <i class="text-danger bx bx-trash fa-2x "></i> </button>' +
            "</div>" +
            "</div>" +
            "</div>";
    }
    $("#category_section").append(html);
    $(".select_multiple").each(function () {
        $(".select_multiple").select2({
            theme: "bootstrap4",
            width: $(".select_multiple").data("width")
                ? $(".select_multiple").data("width")
                : $(".select_multiple").hasClass("w-100")
                    ? "100%"
                    : "style",
            placeholder: $(".select_multiple").data("placeholder"),
            allowClear: Boolean($(".select_multiple").data("allow-clear")),
            dropdownParent: $("#set_commission_model"),
        });
    });
}

//5.Featured_Section-Module
$(".select_multiple").each(function () {
    $(this).select2({
        theme: "bootstrap4",
        width: $(this).data("width")
            ? $(this).data("width")
            : $(this).hasClass("w-100")
                ? "100%"
                : "style",
        placeholder: $(this).data("placeholder"),
        allowClear: Boolean($(this).data("allow-clear")),
    });
});

$(document).on("click", ".remove_category_section", function () {
    $(this).closest(".row").remove();
});

// category tree view

var edit_id = $('input[name="category_id"]').val();
var store_id = $('input[name="store_id"]').val();
var ignore_status = $.isNumeric(edit_id) && edit_id > 0 ? 1 : 0;

if (
    window.location.href.indexOf("admin/products") !== -1 ||
    window.location.href.indexOf("seller/offers") !== -1
) {
    // Check if seller_id is numeric and greater than 0
    if ($.isNumeric(store_id) && store_id > 0) {
        get_seller_categories(ignore_status, edit_id, from);
    } else {
        if (from !== "seller" && from !== "delivery_boy") {
            $.ajax({
                type: "GET",
                url: appUrl + from + "/categories/getCategories",
                data: {
                    ignore_status: ignore_status,
                },
                dataType: "json",
                success: function (result) {
                    var edit_id = $('input[name="category_id"]').val();
                    $("#offer_category_tree_view_html").jstree({
                        plugins: ["checkbox", "themes"],
                        core: {
                            data: result,
                            multiple: true,
                        },
                        checkbox: {
                            three_state: false,
                            cascade: "down",
                        },
                    });
                    $("#offer_category_tree_view_html").bind(
                        "ready.jstree",
                        function (e, data) {
                            $(this).jstree(true).select_node(edit_id);
                        }
                    );
                },
            });
        }
    }
}
function get_seller_categories(ignore_status, edit_id, from) {
    $.ajax({
        type: "GET",
        url: appUrl + from + "/categories/get_seller_categories",
        data: {
            ignore_status: ignore_status,
        },
        dataType: "json",
        success: function (result) {
            $("#offer_category_tree_view_html").jstree("destroy").empty();
            $("#offer_category_tree_view_html").jstree({
                plugins: ["checkbox", "themes"],
                core: {
                    multiple: true,
                    data: result,
                },
                checkbox: {
                    three_state: false,
                    cascade: "down",
                },
            });

            $("#offer_category_tree_view_html").bind(
                "ready.jstree",
                function (e, data) {
                    var tree = $(this).jstree(true);

                    // If edit_id is a list of IDs (an array), pass it directly to select_node.
                    // If it's a comma-separated string, ensure it's converted to an array first.
                    var idsToSelect = Array.isArray(edit_id) ? edit_id : (edit_id ? String(edit_id).split(",") : []);

                    // Iterate over the array of IDs and select each node.
                    idsToSelect.forEach(function (nodeId) {
                        // Ensure the node exists in the tree before attempting to select it.
                        var node = tree.get_node(nodeId);
                        if (node) {
                            tree.select_node(nodeId);
                        }
                    });
                }
            );
        },
    });
}


$(document).on("change", "#seller_id", function (e) {
    e.preventDefault();
    var edit_id = $('input[name="category_id"]').val();
    var seller_id = $(this).val();
    $("#seller_id").val(seller_id);
    var ignore_status = $.isNumeric(edit_id) && edit_id > 0 ? 1 : 0;
    get_seller_pickup_location(seller_id);
    get_seller_categories(ignore_status, edit_id, "admin");
});


// select 2

$(".select_single , .multiple_values , #product-type, #attribute").each(
    function () {
        $(this).select2({
            width: $(this).data("width")
                ? $(this).data("width")
                : $(this).hasClass("w-100")
                    ? "100%"
                    : "style",
            placeholder: $(this).data("placeholder"),
            allowClear: Boolean($(this).data("allow-clear")),
        });
    }
);
$(document).on("select2:selecting", ".select_single", function (e) {
    if ($.inArray($(this).val(), attributes_values_selected) > -1) {
        //Remove value if further selected
        attributes_values_selected.splice(
            attributes_values_selected.indexOf(
                $(this).select2().find(":selected").val()
            ),
            1
        );
    }
});
// type change event

$(document).on("select2:select", "#product-type", function () {
    var value = $(this).val();

    if ($.trim(value) != "") {
        if (value == "simple_product") {
            $("#variant_stock_level").hide(200);
            $("#general_price_section").show(200);
            $(".simple-product-save").show(700);
            $(".product-attributes").addClass("disabled");
            $(".product-variants").addClass("disabled");
            $("#digital_product_setting").hide(200);
            $(".cod_allowed").removeClass("d-none");
            $(".is_returnable").removeClass("d-none");
            $(".all_branches").removeClass("d-none");
        }
        if (value == "variable_product") {
            $("#general_price_section").hide(200);
            $(".simple-product-level-stock-management").hide(200);
            $(".simple-product-save").hide(200);
            $(".product-attributes").addClass("disabled");
            $(".product-variants").addClass("disabled");
            $("#variant_stock_level").show();
            $("#digital_product_setting").hide(200);
            $(".cod_allowed").removeClass("d-none");
            $(".is_returnable").removeClass("d-none");
            $(".all_branches").removeClass("d-none");
        }
    } else {
        $(".product-attributes").addClass("disabled");
        $(".product-variants").addClass("disabled");
        $("#general_price_section").hide(200);
        $(".simple-product-level-stock-management").hide(200);
        $(".simple-product-save").hide(200);
        $("#variant_stock_level").hide(200);
    }
});
$(function () {
    if ($("#combo_type").val() == "combo_product") {
        $("#variant_stock_level").hide(200);
        $("#general_price_section").show(200);
        $(".simple-product-save").show(700);
        $(".product-attributes").addClass("disabled");
        $(".product-variants").addClass("disabled");
        $(".cod_allowed").removeClass("d-none");
        $(".is_returnable").removeClass("d-none");
        $(".all_branches").removeClass("d-none");
    }
});



$(".multiple_values").select2({
    width: $(this).data("width")
        ? $(this).data("width")
        : $(this).hasClass("w-100")
            ? "100%"
            : "style",
    placeholder: $(this).data("placeholder"),
    allowClear: Boolean($(this).data("allow-clear")),
});

$(document).on("select2:select", ".select_single", function (e) {
    var select = $(this)
        .closest(".row")
        .find(".multiple_values")
        .text(null)
        .trigger("change");

    select.empty();
    var data = $(this).select2().find(":selected").data("values");

    if (data !== null && data !== undefined) {
        if (typeof data === "string") {
            data = JSON.parse(data);
        }

        data.forEach((d) => {
            if (d.text !== undefined) {
                d.text = d.text;
            } else {
                d.text = d.value;
            }
            $(select).append(`<option value="${d.id}">${d.text}</option>`);
        });
    }
});

// Permutation formula

function containsAll(needles, haystack) {
    for (var i = 0; i < needles.length; i++) {
        if ($.inArray(needles[i], haystack) == -1) return false;
    }
    return true;
}

function getPermutation(args) {
    var r = [],
        max = args.length - 1;

    function helper(arr, i) {
        for (var j = 0, l = args[i].length; j < l; j++) {
            var a = arr.slice(0); // clone arr
            a.push(args[i][j]);
            if (i == max) r.push(a);
            else helper(a, i + 1);
        }
    }
    helper([], 0);
    return r;
}



// create ediatable variants

function create_editable_variants(
    data,
    newly_selected_attr = false,
    add_newly_created_variants = false
) {
    if (data.length > 0 && data[0].variant_ids) {
        $("#reset_variants").show();
        var html = "";

        if (
            !$.isEmptyObject(attributes_values) &&
            add_newly_created_variants == true
        ) {
            var permuted_value_result = getPermutation(attributes_values);
        }
        html +=
            '<div ondragstart="return false;" class="d-flex justify-content-end"><button type="button" class="btn btn-primary btn-sm mb-3"  id="expand_all">Expand All</button>' +
            '<button type="button" class="btn btn-primary btn-sm mb-3 ml-4 ms-3" id="collapse_all">Collapse All</button></div>';
        $.each(data, function (a, b) {
            if (
                !$.isEmptyObject(permuted_value_result) &&
                add_newly_created_variants == true
            ) {
                var permuted_value_result_temp = permuted_value_result;
                var varinat_ids = b.variant_ids.split(",");
                $.each(permuted_value_result_temp, function (index, value) {
                    if (containsAll(varinat_ids, value)) {
                        permuted_value_result.splice(index, 1);
                    }
                });
            }

            variant_counter++;
            var attr_name = "pro_attr_" + variant_counter;
            html +=
                '<div class="form-group move p-2 pe-0 product-variant-selectbox ps-0 pt-3 rounded row">';
            html +=
                '<input type="hidden" name="edit_variant_id[]" value=' +
                b.id +
                ">";
            var tmp_variant_value_id = "";
            var varaint_array = [];
            var varaint_ids_temp_array = [];
            var flag = 0;
            var variant_images = "";
            var image_html = "";
            if (b.images) {
                variant_images = JSON.parse(b.images);
            }

            $.each(b.variant_ids.split(","), function (key) {
                varaint_ids_temp_array[key] = $.trim(this);
            });

            $.each(b.variant_values.split(","), function (key) {
                varaint_array[key] = $.trim(this);
            });
            if (variant_images) {
                $.each(variant_images, function (img_key, img_value) {
                    image_html +=
                        '<div class="col-md-3 col-sm-12 shadow bg-white rounded m-3 p-3 text-center grow product-image-container"><div class="image-upload-div"><img src=' +
                        appUrl +
                        "storage" +
                        img_value +
                        ' alt="Image Not Found"></div> <a href="javascript:void(0)" class="delete-img" data-id="' +
                        b.id +
                        '" data-field="images" data-img=' +
                        img_value +
                        ' data-table="product_variants" data-path=' +
                        img_value +
                        ' data-isjson="true"> <span class="btn btn-block bg-gradient-danger text-danger btn-xs"><i class="far fa-trash-alt me-1"></i> Delete</span></a> <input type="hidden" name="variant_images[' +
                        a +
                        '][]"  value=' +
                        img_value +
                        "></div>";
                });
            }
            for (var i = 0; i < varaint_array.length; i++) {
                html +=
                    '<div class="col-md-5 variant_col"> <input type="hidden"  value="' +
                    varaint_ids_temp_array[i] +
                    '"><input type="text" class="col form-control" value="' +
                    varaint_array[i] +
                    '" readonly></div>';
            }
            if (
                newly_selected_attr != false &&
                newly_selected_attr.length > 0
            ) {
                for (var i = 0; i < newly_selected_attr.length; i++) {
                    var tempVariantsIds = [];
                    var tempVariantsValues = [];
                    $.each(
                        newly_selected_attr[i].attribute_values_id.split(","),
                        function () {
                            tempVariantsIds.push($.trim(this));
                        }
                    );
                    html +=
                        '<div class="col-md-2"><select class="col new-added-variant form-control" ><option value="">Select Attribute</option>';
                    $.each(
                        newly_selected_attr[i].attribute_values.split(","),
                        function (key) {
                            tempVariantsValues.push($.trim(this));
                            html +=
                                '<option value="' +
                                tempVariantsIds[key] +
                                '">' +
                                tempVariantsValues[key] +
                                "</option>";
                        }
                    );
                    html += "</select></div>";
                }
            }

            html +=
                '<input type="hidden" name="variants_ids[]" value="' +
                b.attribute_value_ids +
                '"><div class="col-md-1"> <button type="button" data-bs-toggle="collapse" class="btn btn-tool product-variant-expand-btn" data-bs-target="#' +
                attr_name +
                '" aria-expanded="true"><i class="bx bx-chevron-down-circle"></i> </button></div><div class="col-md-1"> <button type="button" class="btn btn-tool remove_variants"> <i class="bx bx-trash"></i> </button></div><div class="col-12" id="variant_stock_management_html"><div id=' +
                attr_name +
                ' style="" class="collapse">';

            if (
                $(".variant_stock_status").is(":checked") &&
                $(".variant-stock-level-type").val() == "variable_level"
            ) {
                var selected = b.availability == "0" ? "selected" : " ";
                html +=
                    '<div class="row mt-5">' +
                    '<div class="col-md-6">' +
                    "<ul>" +
                    "<li><h6>Price Info</h6></li>" +
                    "</ul>" +
                    '<div class="col-md-12">' +
                    '<div class="form-group">' +
                    '<label for="simple_price" min="0.01" class="col-md-6 form-label">Price: <span class="text-asterisks text-sm">*</span></label>' +
                    '<input type="number" name="variant_price[]" class="col form-control price varaint-must-fill-field" min="0.01" step="0.01" value="' +
                    b.price +
                    '">' +
                    "</div>" +
                    "</div>" +
                    '<div class="col-md-12">' +
                    '<div class="form-group">' +
                    '<label for="type" class="col-md-6 form-label">Special Price: <span class="text-asterisks text-sm">*</span></label>' +
                    '<input type="number" name="variant_special_price[]" class="col form-control discounted_price" min="0" step="0.01" value="' +
                    b.special_price +
                    '">' +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    '<div class="col-md-6">' +
                    '<div class="dimensions" id="product-dimensions">' +
                    "<ul>" +
                    "<li><h6>Standard shipping weightage</h6></li>" +
                    "</ul>" +
                    '<div class="form-group row">' +
                    '<div class="col-6">' +
                    '<label for="weight" class="form-label col-md-12">Weight <small>(kg)</small> <span class="text-danger text-xs">*</span></label>' +
                    '<input type="number" class="form-control" name="weight[]" placeholder="Weight" id="weight" value="' +
                    b.weight +
                    '" step="0.01">' +
                    "</div>" +
                    '<div class="col-6">' +
                    '<label for="height" class="form-label col-md-12">Height <small>(cms)</small></label>' +
                    '<input type="number" class="form-control" name="height[]" placeholder="Height" id="height" value="' +
                    b.height +
                    '" step="0.01">' +
                    "</div>" +
                    "</div>" +
                    '<div class="form-group row">' +
                    '<div class="col-6">' +
                    '<label for="breadth" class="form-label col-md-12">Breadth <small>(cms)</small> </label>' +
                    '<input type="number" class="form-control" name="breadth[]" placeholder="Breadth" id="breadth" value="' +
                    b.breadth +
                    '" step="0.01">' +
                    "</div>" +
                    '<div class="col-6">' +
                    '<label for="length" class="form-label col-md-12">Length <small>(cms)</small> </label>' +
                    '<input type="number" class="form-control" name="length[]" placeholder="Length" id="length" value="' +
                    b.length +
                    '" step="0.01">' +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    '<div class="col-md-12">' +
                    "<ul>" +
                    "<li>" +
                    "<h6>Stock Management</h6>" +
                    "</li>" +
                    "</ul>" +
                    '<div class="form-group row">' +
                    '<div class="col-3">' +
                    '<label for="variant_sku" class="form-label col-md-12">SKU <span class="text-asterisks text-sm">*</span></label>' +
                    '<input type="text" name="variant_sku[]" class="col form-control varaint-must-fill-field" value="' +
                    b.sku +
                    '">' +
                    "</div>" +
                    '<div class="col-3">' +
                    '<label for="variant_total_stock" class="form-label col-md-12">Total Stock <span class="text-asterisks text-sm">*</span></label>' +
                    '<input type="number" min="1" name="variant_total_stock[]" class="col form-control varaint-must-fill-field" value="' +
                    b.stock +
                    '">' +
                    "</div>" +
                    '<div class="col-3">' +
                    '<label for="variant_level_stock_status" class="form-label col-md-12">Stock Status</label>' +
                    '<select type="text" name="variant_level_stock_status[]" class="col form-control varaint-must-fill-field">' +
                    '<option value="1" ' +
                    selected +
                    ">In Stock</option>" +
                    '<option value="0" ' +
                    selected +
                    ">Out Of Stock</option>" +
                    "</select>" +
                    "</div>" +
                    "</div>" +
                    "</div>";
            } else {
                var selected = b.availability == "0" ? "selected" : " ";
                html +=
                    '<div class="row mt-5">' +
                    '<div class="col-md-6">' +
                    "<ul>" +
                    "<li><h6>Price Info</h6></li>" +
                    "</ul>" +
                    '<div class="col-md-12">' +
                    '<div class="form-group">' +
                    '<label for="simple_price" min="0.01" class="col-md-6 form-label">Price: <span class="text-asterisks text-sm">*</span></label>' +
                    '<input type="number" name="variant_price[]" class="col form-control price varaint-must-fill-field" min="0.01" step="0.01" value="' +
                    b.price +
                    '">' +
                    "</div>" +
                    "</div>" +
                    '<div class="col-md-12">' +
                    '<div class="form-group">' +
                    '<label for="type" class="col-md-6 form-label">Special Price: <span class="text-asterisks text-sm">*</span></label>' +
                    '<input type="number" name="variant_special_price[]" class="col form-control discounted_price" min="0" step="0.01" value="' +
                    b.special_price +
                    '">' +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    '<div class="col-md-6">' +
                    '<div class="dimensions" id="product-dimensions">' +
                    "<ul>" +
                    "<li><h6>Standard shipping weightage</h6></li>" +
                    "</ul>" +
                    '<div class="form-group row">' +
                    '<div class="col-6">' +
                    '<label for="weight" class="form-label col-md-12">Weight <small>(kg)</small> <span class="text-danger text-xs">*</span></label>' +
                    '<input type="number" class="form-control" name="weight[]" placeholder="Weight" id="weight" value="' +
                    b.weight +
                    '" step="0.01">' +
                    "</div>" +
                    '<div class="col-6">' +
                    '<label for="height" class="form-label col-md-12">Height <small>(cms)</small></label>' +
                    '<input type="number" class="form-control" name="height[]" placeholder="Height" id="height" value="' +
                    b.height +
                    '" step="0.01">' +
                    "</div>" +
                    "</div>" +
                    '<div class="form-group row">' +
                    '<div class="col-6">' +
                    '<label for="breadth" class="form-label col-md-12">Breadth <small>(cms)</small> </label>' +
                    '<input type="number" class="form-control" name="breadth[]" placeholder="Breadth" id="breadth" value="' +
                    b.breadth +
                    '" step="0.01">' +
                    "</div>" +
                    '<div class="col-6">' +
                    '<label for="length" class="form-label col-md-12">Length <small>(cms)</small> </label>' +
                    '<input type="number" class="form-control" name="length[]" placeholder="Length" id="length" value="' +
                    b.length +
                    '" step="0.01">' +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div>";
            }

            html +=
                '<div class="col-md-6 file_upload_box border file_upload_border mt-2">' +
                '<div class="mt-2">' +
                '<div class="col-md-12 text-center">' +
                "<div>" +
                '<a class="media_link uploadFile img btn text-white " data-input="variant_images[' +
                a +
                '][]" data-isremovable="1" data-is-multiple-uploads-allowed="1" data-bs-toggle="modal" data-bs-target="#media-upload-modal" value="Upload Photo">' +
                '<h4><i class="bx bx-upload"></i> Upload</h4>' +
                "</a>" +
                '<p class="image_recommendation">Recommended Size : larger than 400 x 260 & smaller than 600 x 300 pixels.</p>' +
                "</div>" +
                "</div>" +
                "</div>" +
                "</div>" +
                '<div class=" container-fluid row image-upload-section ms-1">' +
                image_html +
                "</div>";

            html += "</div></div></div>";

            $("#variants_process").html(html);
        });

        if (
            !$.isEmptyObject(attributes_values) &&
            add_newly_created_variants == true
        ) {
            create_variants(permuted_value_result, from);
        }
    }
}

// reset variants

$(document).on("click", "#reset_variants", function () {
    Swal.fire({
        title: "Are You Sure To Reset!",
        text: "You won't be able to revert this after update!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, Reset it!",
        showLoaderOnConfirm: true,
        allowOutsideClick: false,
    }).then((result) => {
        if (result.value) {
            $(".additional-info").block({
                message: "<h6>Reseting Variations</h6>",
                css: {
                    border: "3px solid #E7F3FE",
                },
            });
            if (attributes_values.length > 0) {
                $(".no-variants-added").hide();
                create_variants(false, from);
            }
            setTimeout(function () {
                $(".additional-info").unblock();
            }, 2000);
        }
    });
});

$(document).on("click", "#expand_all", function () {
    $(".product-variant-selectbox .collapse").addClass("show");
    $(".product-variant-selectbox .btn.btn-tool.text-primary").attr(
        "aria-expanded",
        "true"
    );
});

$(document).on("click", "#collapse_all", function () {
    $(".product-variant-selectbox .collapse").removeClass("show");
    $(".product-variant-selectbox .btn.btn-tool.text-primary").attr(
        "aria-expanded",
        "false"
    );
});

//expand and collapse all variants

$(document).on("click", "#expand_all", function () {
    $(".product-variant-selectbox .collapse").addClass("show");
    $(".product-variant-selectbox .btn.btn-tool.text-primary").attr(
        "aria-expanded",
        "true"
    );
});

$(document).on("click", "#collapse_all", function () {
    $(".product-variant-selectbox .collapse").removeClass("show");
    $(".product-variant-selectbox .btn.btn-tool.text-primary").attr(
        "aria-expanded",
        "false"
    );
});
if (document.getElementById("system-update-dropzone")) {
    var systemDropzone = new Dropzone("#system-update-dropzone", {
        url: appUrl + "admin/settings/system-update",
        paramName: "update_file",
        autoProcessQueue: false,
        parallelUploads: 1,
        maxFiles: 1,
        timeout: 360000,
        autoDiscover: false,
        addRemoveLinks: true,
        dictRemoveFile: "x",
        dictMaxFilesExceeded: "Only 1 file can be uploaded at a time ",
        dictResponseError: "Error",
        uploadMultiple: true,
        dictDefaultMessage:
            '<p><input type="submit" value="Select Files" class="btn btn-success" /><br> or <br> Drag & Drop System Update / Installable / Plugin\'s .zip file Here</p>',
    });

    systemDropzone.on("addedfile", function (file) {
        var i = 0;
        if (this.files.length) {
            var _i, _len;
            for (_i = 0, _len = this.files.length; _i < _len - 1; _i++) {
                if (
                    this.files[_i].name === file.name &&
                    this.files[_i].size === file.size &&
                    this.files[_i].lastModifiedDate.toString() ===
                    file.lastModifiedDate.toString()
                ) {
                    this.removeFile(file);
                    i++;
                }
            }
        }
    });

    systemDropzone.on("error", function (file, response) { });

    systemDropzone.on("sending", function (file, xhr, formData) {
        xhr.onreadystatechange = function () {
            if (this.readyState == 4 && this.status == 200) {
                var response = JSON.parse(this.response);
                if (response["error"] == false) {
                    iziToast.success({
                        message: response["message"],
                    });
                } else {
                    iziToast.error({
                        title: "Error",
                        message: response["message"],
                    });
                }
                $(file.previewElement)
                    .find(".dz-error-message")
                    .text(response.message);
            }
        };
    });
    $("#system_update_btn").on("click", function (e) {
        e.preventDefault();
        systemDropzone.processQueue();
    });
}

// change event of cancelable allowed or not

$("#is_all_branches_checkbox").change(function () {
    console.log($(this).prop("checked"));
    // If the checkbox is checked (turned on), show the dropdown, otherwise hide it
    if ($(this).prop("checked")) {
        $("#branches_till").hide();
    } else {
        $("#branches_till").show();
    }
});

$("#add_code_checkbox").change(function () {
    // If the checkbox is checked (turned on), show the dropdown, otherwise hide it
    if ($(this).prop("checked") && $("#is_all_branches_checkbox").prop("checked") === false) {
        $("#code_till").show();
        $("#code_till_branche").show();
        $("#code_till_all").hide();
    } else if ($(this).prop("checked") && $("#is_all_branches_checkbox").prop("checked") === true) {
        $("#code_till").show();
        $("#code_till_branche").hide();
        $("#code_till_all").show();
    }
});



// save offer

$(document).on("submit", "#save-offer", function (e) {
    console.log("from submit");
    e.preventDefault();
    save_offer(this);



});

function save_offer(form) {

    var catid = $("#offer_category_tree_view_html").jstree("get_selected");
    var formData = new FormData(form);
    var submit_btn = $(".submit_button");
    var btn_html = $(".submit_button").html();
    var btn_val = $(".submit_button").val();
    var button_text =
        btn_html != "" || btn_html != "undefined" ? btn_html : btn_val;
    token = $('meta[name="csrf-token"]').attr("content");
    formData.append("_token", token);
    formData.append("category_id", catid);
    $.ajax({
        type: "POST",
        url: $(form).attr("action"),
        data: formData,
        beforeSend: function () {
            submit_btn.html("الرجاء انتظر...");
            submit_btn.attr("disabled", true);
        },
        cache: false,
        contentType: false,
        processData: false,
        dataType: "json",
        success: function (result) {
            var location = result["location"] || "";
            token = $('meta[name="csrf-token"]').attr("content");
            if (result["error"] == true) {
                submit_btn.html(button_text);
                submit_btn.attr("disabled", false);
                if (result["error_message"]) {
                    iziToast.error({
                        message: result["error_message"],
                    });
                } else if (result["message"]) {
                    iziToast.error({
                        message: result["message"],
                    });
                }
            } else {
                submit_btn.html(button_text);
                submit_btn.attr("disabled", false);
                iziToast.success({
                    message: result["message"],
                });
                $("#save-offer")[0].reset();
                setTimeout(function () {
                    // Redirect to the appropriate URL
                    window.location.href = location;
                }, 2000);
            }
        },
    });
}
// tinymce editor

$(".editSendMail").on("shown.bs.modal", function (e) {
    if ($(".textarea").length > 0) {
        tinymce.init({
            selector: ".textarea",
            plugins: [
                "a11ychecker",
                "advlist",
                "advcode",
                "advtable",
                "autolink",
                "checklist",
                "export",
                "lists",
                "link",
                "image",
                "charmap",
                "preview",
                "code",
                "anchor",
                "searchreplace",
                "visualblocks",
                "powerpaste",
                "fullscreen",
                "formatpainter",
                "insertdatetime",
                "media",
                "image",
                "directionality",
                "fullscreen",
                "table",
                "help",
                "wordcount",
            ],
            toolbar:
                "undo redo | image media | code fullscreen| formatpainter casechange blocks fontsize | bold italic forecolor backcolor | " +
                "alignleft aligncenter alignright alignjustify | " +
                "bullist numlist checklist outdent indent | removeformat | ltr rtl |a11ycheck table help",

            font_size_formats: "8pt 10pt 12pt 14pt 16pt 18pt 24pt 36pt 48pt",
            image_uploadtab: false,

            relative_urls: false,
            remove_script_host: false,
            file_picker_types: "image media",
            media_poster: false,
            media_alt_source: false,

            setup: function (editor) {
                editor.on("change keyup", function (e) {
                    editor.save(); // updates this instance's textarea
                    $(editor.getElement()).trigger("change"); // for garlic to detect change
                });
            },
        });
    }
});

tinymce.init({
    selector: ".addr_editor",
    menubar: true,
    plugins: [
        "a11ychecker",
        "advlist",
        "advcode",
        "advtable",
        "autolink",
        "checklist",
        "export",
        "lists",
        "link",
        "image",
        "charmap",
        "preview",
        "code",
        "anchor",
        "searchreplace",
        "visualblocks",
        "powerpaste",
        "fullscreen",
        "formatpainter",
        "insertdatetime",
        "media",
        "image",
        "directionality",
        "fullscreen",
        "table",
        "help",
        "wordcount",
    ],
    toolbar:
        "undo redo | image media | code fullscreen| formatpainter casechange blocks fontsize | bold italic forecolor backcolor | " +
        "alignleft aligncenter alignright alignjustify | " +
        "bullist numlist checklist outdent indent | removeformat | ltr rtl |a11ycheck table help",

    font_size_formats: "8pt 10pt 12pt 14pt 16pt 18pt 24pt 36pt 48pt",
    image_uploadtab: false,
    relative_urls: false,
    remove_script_host: false,
    file_picker_types: "image media",
    media_poster: false,
    media_alt_source: false,
    setup: function (editor) {
        editor.on("change keyup", function (e) {
            editor.save(); // updates this instance's textarea
            $(editor.getElement()).trigger("change"); // for garlic to detect change
        });
    },
});

$(".branches_list").select2({
    ajax: {
        url: appUrl + from + "/offers/get_branches",
        type: "GET",
        dataType: "json",
        delay: 250,
        data: function (params) {
            return {
                search: params.term,
                limit: 5,
            };
        },
        processResults: function (response) {
            return {
                results: response,
            };
        },

        cache: true,
    },
    placeholder: "Search for branches",
    multiple: true,  // Enable multi-selection
    allowClear: true,
    closeOnSelect: false,  // Keep dropdown open after selection
});


var storeId = "";
$(document).on("change", ".seller_register_store_id", function () {
    storeId = $(this).val();
});
console.log(storeId);
$(".search_admin_category").each(function () {
    $(this).select2({
        ajax: {
            url: appUrl + from + "/categories/get_category_details",
            dataType: "json",
            delay: 250,
            data: function (data) {
                return {
                    search: data.term,
                    limit: 10,
                };
            },
            processResults: function (response) {
                return {
                    results: response.results,
                };
            },
            cache: true,
        },

        escapeMarkup: function (markup) {
            return markup;
        },
        placeholder: $(this).data("placeholder") || "Search for categories",
        width: $(this).data("width")
            ? $(this).data("width")
            : $(this).hasClass("w-100")
                ? "100%"
                : "style",
        allowClear: Boolean($(this).data("allow-clear")),
    });
});
// search product in sliders and offers and feature section
var combo_seller_id = "";
// $(document).on("change", ".combo_seller_id", function (e) {
//     e.preventDefault();
//     combo_seller_id = $(this).val();
//     $(".main_combo_seller_id").val(combo_seller_id);

// });


// change event for system users

$(document).on("change", ".system-user-role", function () {
    var role = $(this).val();
    if (role > 0) {
        $(".permission-table").removeClass("d-none");
    } else {
        $(".permission-table").addClass("d-none");
    }
});

// update orders using sortable

$(function () {
    $("#sortable").sortable({
        axis: "y",
        opacity: 0.6,
        cursor: "grab",
    });
});

$("#bulk_upload_form").on("submit", function (e) {
    e.preventDefault();

    var type = $("#type").val();
    var submitButton = $(".submit_button"); // Assuming your submit button has this ID

    if (type != "") {
        var formdata = new FormData(this);
        token = $('meta[name="csrf-token"]').attr("content");

        // Disable button and change text to "Please wait..."
        submitButton.prop("disabled", true).text("Please wait...");

        $.ajax({
            type: "POST",
            data: formdata,
            url: $(this).attr("action"),
            dataType: "json",
            cache: false,
            contentType: false,
            processData: false,

            success: function (result) {
                token = $('meta[name="csrf-token"]').attr("content");

                if (result.error == true && result.error_message) {
                    iziToast.show({
                        title: "Error",
                        message: result.error_message,
                        color: "red",
                    });
                } else if (result.error == "false") {
                    iziToast.show({
                        title: "Success",
                        message: result.message,
                        color: "green",
                    });
                    setTimeout(function () {
                        location.reload();
                    }, 2000); // Reload after 2 seconds

                } else {
                    iziToast.show({
                        title: "Error",
                        message: result.message,
                        color: "red",
                    });
                }
            },
            complete: function () {
                // Re-enable button and reset text after the request is complete
                submitButton.prop("disabled", false).text("Upload");
            }
        });
    } else {
        iziToast.error({
            message: "Please select type",
        });
    }
});



// click event of edit data from model

$(function () {
   

    $(document).on("click", ".edit-city", function () {
        var city_id = $(this).data("id");
        var fields_to_update = {
            name_ar: "name_ar",
            name_en:
                "name_en",
        };
        edit_data("/admin/city", "edit_city_id", city_id, fields_to_update);
    });

  

    $(document).on("click", ".edit-blog-category", function () {
        var category_id = $(this).data("id");
        var fields_to_update = {
            name: "name",
            image: "image",
        };
        edit_data(
            "/admin/blog_category",
            "edit_category_id",
            category_id,
            fields_to_update
        );
    });

    $(document).on("click", ".edit-faq", function () {
        var faq_id = $(this).data("id");
        var fields_to_update = {
            edit_question: "question",
            edit_answer: "answer",
        };
        edit_data("/admin/faq", "edit_faq_id", faq_id, fields_to_update);
    });

    $(document).on("click", ".edit-ticket-type", function () {
        var ticket_type_id = $(this).data("id");
        var fields_to_update = {
            title: "title",
        };
        edit_data(
            "/admin/ticket_types",
            "edit_ticket_type_id",
            ticket_type_id,
            fields_to_update
        );
    });

});

// general function for edit data from model

function edit_data(url_prefix, id_field, id, fields_to_update) {
    $.ajax({
        url: url_prefix + "/edit/" + id,
        type: "GET",
        success: function (data) {
            $("." + id_field).val(data.id);
            // Loop through the fields_to_update and set their values
            $.each(fields_to_update, function (field_id, data_field) {
                $("." + field_id).val(data[data_field]);
            });

            // Set the form action dynamically
            $(".submit_form").attr("action", url_prefix + "/update/" + id);

            $("#edit_modal").modal("show");
        },
    });
}

// light box

$(function () {
    $(document).on("click", '[data-toggle="lightbox"]', function (event) {
        event.preventDefault();
        $(this).ekkoLightbox();
    });
});
$(function () {
    // Extract the current URL
    var current_url = window.location.href;

    var category_url;

    // Check if the URL contains the specific strings
    if (current_url.includes("seller/categories")) {
        // If the URL contains "seller/categories/", set the category URL for the seller
        category_url = appUrl + "seller/categories/get_seller_categories";
    } else if (current_url.includes("admin/categories")) {
        // If the URL contains "admin/categories/", set the category URL for the admin
        category_url = appUrl + "admin/categories/getCategories";
    }
    // Load the JavaScript using the determined URL
    if (category_url) {
        $.ajax({
            type: "GET",
            url: category_url,
            dataType: "json",
            success: function (result) {
                // Initialize jsTree with the received data
                $(".tree_view_html").jstree({
                    plugins: ["themes", "checkbox", "types"],
                    core: {
                        themes: {
                            variant: "large",
                        },
                        data: result.categories,
                        multiple: false,
                    },
                    types: {
                        boss: {
                            icon:
                                appUrl +
                                "/storage/app_images_and_videos/checkbox_checked.png",
                        },
                    },
                    checkbox: {
                        three_state: false,
                        cascade: "none",
                        keep_selected_style: false,
                    },
                });
            },
        });
    }
});

// custom message type change event

$(function () {
    var sort_type_val = $(".custom_message_type").val();
    if (sort_type_val == "place_order" && sort_type_val != " ") {
        $(".place_order").removeClass("d-none");
    } else {
        $(".place_order").addClass("d-none");
    }
    if (sort_type_val == "settle_cashback_discount" && sort_type_val != " ") {
        $(".settle_cashback_discount").removeClass("d-none");
    } else {
        $(".settle_cashback_discount").addClass("d-none");
    }
    if (sort_type_val == "settle_seller_commission" && sort_type_val != " ") {
        $(".settle_seller_commission").removeClass("d-none");
    } else {
        $(".settle_seller_commission").addClass("d-none");
    }
    if (sort_type_val == "customer_order_received" && sort_type_val != " ") {
        $(".customer_order_received").removeClass("d-none");
    } else {
        $(".customer_order_received").addClass("d-none");
    }
    if (sort_type_val == "customer_order_processed" && sort_type_val != " ") {
        $(".customer_order_processed").removeClass("d-none");
    } else {
        $(".customer_order_processed").addClass("d-none");
    }
    if (sort_type_val == "customer_order_shipped" && sort_type_val != " ") {
        $(".customer_order_shipped").removeClass("d-none");
    } else {
        $(".customer_order_shipped").addClass("d-none");
    }
    if (sort_type_val == "customer_order_delivered" && sort_type_val != " ") {
        $(".customer_order_delivered").removeClass("d-none");
    } else {
        $(".customer_order_delivered").addClass("d-none");
    }
    if (sort_type_val == "customer_order_cancelled" && sort_type_val != " ") {
        $(".customer_order_cancelled").removeClass("d-none");
    } else {
        $(".customer_order_cancelled").addClass("d-none");
    }
    if (sort_type_val == "customer_order_returned" && sort_type_val != " ") {
        $(".customer_order_returned").removeClass("d-none");
    } else {
        $(".customer_order_returned").addClass("d-none");
    }
    if (
        sort_type_val == "customer_order_returned_request_approved" &&
        sort_type_val != " "
    ) {
        $(".customer_order_returned_request_approved").removeClass("d-none");
    } else {
        $(".customer_order_returned_request_approved").addClass("d-none");
    }
    if (
        sort_type_val == "customer_order_returned_request_decline" &&
        sort_type_val != " "
    ) {
        $(".customer_order_returned_request_decline").removeClass("d-none");
    } else {
        $(".customer_order_returned_request_decline").addClass("d-none");
    }
    if (sort_type_val == "delivery_boy_order_deliver" && sort_type_val != " ") {
        $(".delivery_boy_order_deliver").removeClass("d-none");
    } else {
        $(".delivery_boy_order_deliver").addClass("d-none");
    }
    if (sort_type_val == "wallet_transaction" && sort_type_val != " ") {
        $(".wallet_transaction").removeClass("d-none");
    } else {
        $(".wallet_transaction").addClass("d-none");
    }
    if (sort_type_val == "ticket_status" && sort_type_val != " ") {
        $(".ticket_status").removeClass("d-none");
    } else {
        $(".ticket_status").addClass("d-none");
    }
    if (sort_type_val == "ticket_message" && sort_type_val != " ") {
        $(".ticket_message").removeClass("d-none");
    } else {
        $(".ticket_message").addClass("d-none");
    }
    if (
        sort_type_val == "bank_transfer_receipt_status" &&
        sort_type_val != " "
    ) {
        $(".bank_transfer_receipt_status").removeClass("d-none");
    } else {
        $(".bank_transfer_receipt_status").addClass("d-none");
    }
    if (sort_type_val == "bank_transfer_proof" && sort_type_val != " ") {
        $(".bank_transfer_proof").removeClass("d-none");
    } else {
        $(".bank_transfer_proof").addClass("d-none");
    }
});


$(function () {
    $(document).on("click", ".hashtag", function () {
        var txt = $.trim($(this).text());
        var box = $("#text-box");
        box.val(box.val() + txt);
    });
    $(document).on("click", ".hashtag_input", function () {
        var txt = $.trim($(this).text());
        var box = $("#custom_message_title");
        box.val(box.val() + txt);
    });
});

$(document).on("click", ".update_status_admin_bulk", function (e) {
    var order_item_id = [];
    if ($('input[name="seller_id"]:checked').val() != undefined) {
        var seller_id = $('input[name="seller_id"]:checked').val();
    } else {
        var seller_id = $(this).data("seller_id");
    }

    if ($('input[name="edit_order_id"]').val() != undefined) {
        var order_id = $('input[name="edit_order_id"]').val();
    } else {
        var order_id = $('input[name="order_id"]').val();
    }
    var status = $(".status").val();
    var deliver_by = $("#deliver_by").val();
    var order_item_ids = $(
        'input[name="order_item_id"]:checked'
    ).serializeArray();
    var order_item_ids = $(
        'input[name="order_item_id"]:checked'
    ).serializeArray();
    $.each(order_item_ids, function (i, field) {
        order_item_id.push(field.value);
    });

    Swal.fire({
        title: "Are You Sure?",
        text: "You won't be able to revert this!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, update it!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: "POST",
                    url: appUrl + from + "/orders/update_order_status",
                    data: {
                        seller_id: seller_id,
                        order_id: order_id,
                        status: status,
                        deliver_by: deliver_by,
                        order_item_id: order_item_id,
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },

                    dataType: "json",
                    success: function (result) {
                        if (result["error"] == false) {
                            iziToast.success({
                                message: result["message"],
                            });
                        } else {
                            iziToast.error({
                                message: result["message"],
                            });
                        }
                        swal.close();
                        setTimeout(function () {
                            location.reload();
                        }, 1000);
                    },
                });
            });
        },
        allowOutsideClick: false,
    });
});

$(document).on("change", "input[type=radio][name=seller_id]", function () {
    $("input[type=checkbox]").attr("disabled", true);
    $(".check_create_order").removeAttr("disabled");
    var seller_id = $('input[type=radio][name="seller_id"]:checked').val();
    $("input[type=checkbox][id='" + seller_id + "']").removeAttr("disabled");
});


$(".generate_label").on("click", function (e) {
    e.preventDefault();
    var shipment_id = $(this).attr("name");
    var fromSeller = $(this).data("fromseller");
    var fromAdmin = $(this).data("fromadmin");
    if (fromSeller != "undefined" && fromSeller == 1) {
        var url = appUrl + "seller/orders/generate_label";
    }
    if (fromAdmin != "undefined" && fromAdmin == 1) {
        var url = appUrl + "admin/orders/generate_label";
    }
    Swal.fire({
        title: "Are You Sure !",
        text: "you want to generate label!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, generate label!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: "POST",
                    url: url,
                    data: {
                        shipment_id: shipment_id,
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },
                    dataType: "json",
                })
                    .done(function (result, textStatus) {
                        if (result["error"] == false) {
                            Swal.fire(
                                "Label generated!",
                                result["message"],
                                "success"
                            );
                            location.reload();
                        } else {
                            Swal.fire("Oops...", result["message"], "warning");
                        }
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        Swal.fire(
                            "Oops...",
                            "Something went wrong with ajax !",
                            "error"
                        );
                    });
            });
        },
        allowOutsideClick: false,
    });
});

$(".generate_invoice").on("click", function (e) {
    e.preventDefault();
    var order_id = $(this).attr("name");
    var fromSeller = $(this).data("fromseller");
    var fromAdmin = $(this).data("fromadmin");
    if (fromSeller != "undefined" && fromSeller == 1) {
        var url = appUrl + "seller/orders/generate_invoice";
    }
    if (fromAdmin != "undefined" && fromAdmin == 1) {
        var url = appUrl + "admin/orders/generate_invoice";
    }
    Swal.fire({
        title: "Are You Sure !",
        text: "you want to generate invoice!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, generate invoice!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: "POST",
                    url: url,
                    data: {
                        order_id: order_id,
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },
                    dataType: "json",
                })
                    .done(function (result, textStatus) {
                        if (result["error"] == false) {
                            Swal.fire(
                                "Invoice generated!",
                                result["message"],
                                "success"
                            );
                            location.reload();
                        } else {
                            Swal.fire("Oops...", result["message"], "warning");
                        }
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        Swal.fire(
                            "Oops...",
                            "Something went wrong with ajax !",
                            "error"
                        );
                    });
            });
        },
        allowOutsideClick: false,
    });
});

$("#order_tracking_form").on("submit", function (e) {
    e.preventDefault();
    var csrfToken = document.head.querySelector(
        'meta[name="csrf-token"]'
    ).content;

    var formdata = new FormData(this);
    formdata.append("_token", csrfToken);

    $.ajax({
        type: "POST",
        url: $(this).attr("action"),
        data: formdata,
        beforeSend: function () {
            $("#submit_btn").html("Please Wait..").attr("disabled", true);
        },
        cache: false,
        contentType: false,
        processData: false,
        dataType: "json",
        success: function (result) {
            token = $('meta[name="csrf-token"]').attr("content");
            $("#submit_btn").html("Update Details").attr("disabled", false);
            if (result.error == false) {
                iziToast.success({
                    message: result.message,
                    position: "topRight",
                });
                $("table").bootstrapTable("refresh");
                setTimeout(function () {
                    location.reload();
                }, 1000);
                $("#order_tracking_modal").modal("hide");
            }
            if (result.error === true) {
                const messageToShow = result.message || result.error_message;

                if (Array.isArray(messageToShow)) {
                    $.each(messageToShow, function (index, errorMessage) {
                        iziToast.error({
                            title: "Error",
                            message: errorMessage,
                            position: "topRight",
                        });
                    });
                } else {
                    iziToast.error({
                        title: "Error",
                        message: messageToShow,
                        position: "topRight",
                    });
                }
            }
        },
    });
});

$(document).on("click", ".edit_order_tracking", function () {
    var parcelId = $(this).data("id");

    $.ajax({
        url: appUrl + "seller/orders/get_order_tracking",
        type: "GET",
        data: { parcel_id: parcelId },
        success: function (response) {
            if (!response.error) {
                // Populate the modal fields with existing data
                $("#courier_agency").val(response.data.courier_agency);
                $("#tracking_id").val(response.data.tracking_id);
                $("#url").val(response.data.url);
            } else {
                // If no data, clear the modal fields
                $("#courier_agency").val("");
                $("#tracking_id").val("");
                $("#url").val("");
            }
        },
    });
});

$("#system-update").on("submit", function (e) {
    e.preventDefault();
    var csrfToken = document.head.querySelector(
        'meta[name="csrf-token"]'
    ).content;
    var formdata = new FormData(this);
    formdata.append("_token", csrfToken);
    $.ajax({
        type: "POST",
        url: $(this).attr("action"),
        data: formdata,
        cache: false,
        contentType: false,
        processData: false,
        dataType: "json",
        success: function (response) {
            if (response.error == true) {
                if (response.message instanceof Array) {
                    $.each(response.message, function (key, value) {
                        iziToast.error({
                            message: value,
                            position: "topRight",
                        });
                        return false;
                    });
                } else {
                    iziToast.error({
                        message: response.message,
                        position: "topRight",
                    });
                }
                return false;
            }
            iziToast.success({
                message: response.message,
                position: "topRight",
            });
        },
    });
});


$("input[type=radio][name=status]").on("change", function (e) {
    var status = $('input[type=radio][name="status"]:checked').val();
    if (status == 0) {
        $("#return_request_delivery_by").addClass("d-none");
    } else if (status == 1) {
        $("#return_request_delivery_by").removeClass("d-none");
    } else if (status == 2) {
        $("#return_request_delivery_by").addClass("d-none");
    } else if (status == 3) {
        $("#return_request_delivery_by").addClass("d-none");
    } else if (status == 8) {
        $("#return_request_delivery_by").addClass("d-none");
    }
});

$("#admin_payment_request_table").on(
    "click-cell.bs.table",
    function (field, value, row, $el) {
        $('input[name="payment_request_id"]').val($el.id);
        $("#update_remarks").html($el.remarks);

        if ($el.status_digit == 0) {
            $(".pending").prop("checked", true);
        } else if ($el.status_digit == 1) {
            $(".approved").prop("checked", true);
        } else if ($el.status_digit == 2) {
            $(".rejected").prop("rejected", true);
        }
    }
);


// Handle location selection
$("#store-dropdown").on("click", "li", function () {
    var store_id = $(this).data("store-id");
    var store_name = $(this).data("store-name");
    var store_image = $(this).data("store-image");

    token = $('meta[name="csrf-token"]').attr("content");
    $.ajax({
        type: "POST",
        url: "/" + from + "/set_store",
        data: {
            _token: token,
            store_id: store_id,
            store_name: store_name,
            store_image: store_image,
        },
        success: function (data) {
            if (data) {
            } else {
                iziToast.error({
                    message: "Error In Setting Store",
                    position: "topRight",
                });
            }
            location.reload();
        },
    });
});

// fetch stores

$(".search_stores").each(function () {
    $(this).select2({
        ajax: {
            url: appUrl + "admin/store/get_stores_list",
            dataType: "json",
            delay: 250,
            data: function (data) {
                return {
                    search: data.term,
                    limit: 5,
                };
            },
            processResults: function (response) {
                return {
                    results: response.results,
                };
            },
            cache: true,
        },
        escapeMarkup: function (markup) {
            return markup;
        },

        placeholder: $(this).data("placeholder") || "Search for products",
        width: $(this).data("width")
            ? $(this).data("width")
            : $(this).hasClass("w-100")
                ? "100%"
                : "style",
        allowClear: Boolean($(this).data("allow-clear")),
    });
});

function updateColorCode(colorPickerId) {
    // Get the selected color picker and corresponding input field
    var colorPicker = document.getElementById(colorPickerId);
    var colorCodeInput = document.getElementById(colorPickerId + "_code");

    // Update the corresponding input field with the selected color
    if (colorPicker && colorCodeInput) {
        colorCodeInput.value = colorPicker.value;
    }
}

function updateColorPicker(colorPickerId, colorCode) {
    // Update the corresponding color picker with the value from the input field
    var colorPicker = document.getElementById(colorPickerId);
    if (colorPicker) {
        colorPicker.value = colorCode;
    }
}

// category fetch for category sliders

$(".category_sliders_category").select2({
    ajax: {
        url: appUrl + from + "/categories/categories_data",
        dataType: "json",
        delay: 250,
        data: function (params) {
            return {
                term: params.term,
                page: params.page || 1,
                limit: 10,
            };
        },
        processResults: function (data, params) {
            // Adjust page for infinite scroll
            params.page = params.page || 1;

            return {
                results: data.results,
                pagination: {
                    more: params.page * 10 < data.total,
                },
            };
        },
    },
    templateResult: formatCategories,
    placeholder: "Search for categories",
});

function formatCategories(category) {
    if (!category.id) {
        return category.text;
    }
    if (category.loading) return category.text;
    var image = category.image;
    var $category = $(
        '<div class="row">' +
        '<div class="col-md-1 align-self-center">' +
        '<div class="">' +
        '<img class="img-fluid" src="' +
        image +
        '"></div>' +
        "</div>" +
        '<div class="col-md-4 category-name mt-4">' +
        category.text +
        "</div>" +
        "</div>" +
        "</div>"
    );

    return $category;
}

function formatCategoriesSelection(category) {
    if (category.element.dataset.select2Text === undefined) {
        var image = category.image;
        var $category = $(
            '<div class="row">' +
            '<div class="col-md-1 align-self-center">' +
            '<div class="">' +
            '<img class="img-fluid" src="' +
            image +
            '"></div>' +
            "</div>" +
            '<div class="col-md-4 category-name mt-4">' +
            category.text +
            "</div>" +
            "</div>" +
            "</div>"
        );
    } else {
        $category = category.element.dataset.select2Text;
    }
    return $category;
}

//offer fetch for offer sliders

$(".offer_sliders_offer").select2({
    ajax: {
        url: appUrl + from + "/offers/offers_data",
        dataType: "json",
        delay: 250,
        processResults: function (data) {
            return {
                results: data.results,
            };
        },
    },
    templateResult: formatOffers,
    templateSelection: formatoffersSelection,
    placeholder: "Search for offers",
});

function formatOffers(offer) {
    if (!offer.id) {
        return offer.text;
    }
    if (offer.loading) return offer.text;

    var image = offer.image;

    var $offer = $(
        '<div class="row">' +
        '<div class="col-md-1 align-self-center">' +
        '<div class="">' +
        '<img class="img-fluid" src="' +
        image +
        '"></div>' +
        "</div>" +
        '<div class="align-self-center col-md-10">' +
        '<div class="">' +
        (offer.min_discount != 0 && offer.max_discount != 0
            ? "Min - Max Discount : " +
            offer.min_discount +
            "% - " +
            offer.max_discount +
            "%"
            : "") +
        "</div>" +
        '<small class="">ID - ' +
        offer.id +
        " </small> |" +
        '<small class="">Type - ' +
        offer.text +
        " </small> " +
        "</div>" +
        "</div>"
    );

    return $offer;
}

function formatoffersSelection(offer) {
    if (offer.element.dataset.select2Text === undefined) {
        var $offer = $(
            '<div class="row">' +
            '<div class="col-md-1 align-self-center">' +
            '<div class="">' +
            '<img class="img-fluid" src="' +
            offer.image +
            '"></div>' +
            "</div>" +
            '<div class="align-self-center col-md-10">' +
            '<div class="">' +
            (offer.min_discount != 0 &&
                offer.max_discount != 0 &&
                offer.min_discount != undefined &&
                offer.max_discount != undefined
                ? "Min - Max Discount : " +
                offer.min_discount +
                "% - " +
                offer.max_discount +
                "%"
                : "") +
            "</div>" +
            '<small class="">ID - ' +
            offer.id +
            " </small> |" +
            '<small class="">Type - ' +
            offer.text +
            " </small> " +
            "</div>" +
            "</div>"
        );
    } else {
        $offer = offer.element.dataset.select2Text;
    }
    return $offer;
}

$("#offer_type").on("change", function (e) {
    e.preventDefault();

    if (
        $("#offer_type").val() == "categories" ||
        $("#offer_type").val() == "all_products" ||
        $("#offer_type").val() == "all_combo_products" ||
        $("#offer_type").val() == "brand"
    ) {
        $("#min_max_section").removeClass("d-none");
    } else {
        $("#min_max_section").addClass("d-none");
    }
});

$(function () {
    if (
        $("#offer_type").val() == "categories" ||
        $("#offer_type").val() == "all_products" ||
        $("#offer_type").val() == "all_combo_products" ||
        $("#offer_type").val() == "brand"
    ) {
        $("#min_max_section").removeClass("d-none");
    }
});

// Function to show the image corresponding to the selected style
function show_selected_style(selected_element, selected_image) {
    const selected_style = selected_element.val();

    // Hide all images
    selected_image.hide();

    // Show the image corresponding to the selected style
    $(`.${selected_style}`).show();
}

// Define category styles and category sliders
const category_style = $(".category_style");
const category_style_images = $(".category_style_images img");

const category_slider_style = $(".category_slider_style");
const category_slider_style_images = $(".category_slider_style_images img");

// feature section style

const feature_section_style = $(".feature_section_style");
const feature_section_style_images = $(".feature_section_style_images img");

const feature_section_header_style = $(".feature_section_header_style");

const web_home_page_theme = $(".web_home_page_theme");

const web_product_details_style = $(".web_product_details_style");

const feature_section_header_style_images = $(
    ".feature_section_header_style_images img"
);
const web_home_page_theme_images = $(".web_home_page_theme_images img");
const web_product_details_style_images = $(
    ".web_product_details_style_images img"
);
const product_card_style = $(".product_card_style");
const product_card_style_images = $(".product_card_style_images img");
const categories_style = $(".categories_style");
const categories_style_images = $(".categories_style_images img");
const categories_card_style = $(".categories_card_style");
const categories_card_style_images = $(".categories_card_style_images img");
const brands_style = $(".brands_style");
const brands_style_images = $(".brands_style_images img");
const banner_style = $(".banner_style");
const banner_style_images = $(".banner_style_images img");
const slider_style = $(".slider_style");
const slider_style_images = $(".slider_style_images img");

// Event listeners for category styles and category sliders
category_style.on("change", function () {
    show_selected_style(category_style, category_style_images);
});

category_slider_style.on("change", function () {
    show_selected_style(category_slider_style, category_slider_style_images);
});

feature_section_style.on("change", function () {
    show_selected_style(feature_section_style, feature_section_style_images);
});

feature_section_header_style.on("change", function () {
    show_selected_style(
        feature_section_header_style,
        feature_section_header_style_images
    );
});

web_home_page_theme.on("change", function () {
    show_selected_style(web_home_page_theme, web_home_page_theme_images);
});

web_product_details_style.on("change", function () {
    show_selected_style(
        web_product_details_style,
        web_product_details_style_images
    );
});

product_card_style.on("change", function () {
    show_selected_style(product_card_style, product_card_style_images);
});
categories_style.on("change", function () {
    show_selected_style(categories_style, categories_style_images);
});
categories_card_style.on("change", function () {
    show_selected_style(categories_card_style, categories_card_style_images);
});
brands_style.on("change", function () {
    show_selected_style(brands_style, brands_style_images);
});
banner_style.on("change", function () {
    show_selected_style(banner_style, banner_style_images);
});
slider_style.on("change", function () {
    show_selected_style(slider_style, slider_style_images);
});

// Hide all images on page load (initially)
category_style_images.hide();
category_slider_style_images.hide();
feature_section_style_images.hide();
feature_section_header_style_images.hide();
web_home_page_theme_images.hide();
web_product_details_style_images.hide();
product_card_style_images.hide();
categories_style_images.hide();
categories_card_style_images.hide();
brands_style_images.hide();
banner_style_images.hide();

// Show the selected style image when the page loads
show_selected_style(category_style, category_style_images);
show_selected_style(category_slider_style, category_slider_style_images);
show_selected_style(feature_section_style, feature_section_style_images);
show_selected_style(product_card_style, product_card_style_images);
show_selected_style(categories_style, categories_style_images);
show_selected_style(categories_card_style, categories_card_style_images);
show_selected_style(brands_style, brands_style_images);
show_selected_style(banner_style, banner_style_images);
show_selected_style(slider_style, slider_style_images);
show_selected_style(
    feature_section_header_style,
    feature_section_header_style_images
);
show_selected_style(web_home_page_theme, web_home_page_theme_images);
show_selected_style(
    web_product_details_style,
    web_product_details_style_images
);

// set default store

$(document).on("click", ".set_default_store", function () {
    var id = $(this).data("id");
    var status = $(this).data("store-status");
    var url = $(this).data("url");

    $.ajax({
        method: "GET",
        url: url,
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
        },
        success: function (response) {
            if (response.error_message) {
                iziToast.error({
                    title: "Error",
                    message: response.error_message,
                    position: "topRight",
                });
                $(".table").bootstrapTable("refresh");
            } else {
                iziToast.success({
                    title: "Success",
                    message: "Set as Default Store Successfully",
                    position: "topRight",
                });
                $(".table").bootstrapTable("refresh");
            }
        },
        fail: function (response) {
            iziToast.error({
                title: "Error",
                message: "Something Went Wrong!!",
                position: "topRight",
            });
        },
    });
});

function update_selected_products_count(select_box, output_field) {
    var selected_options = select_box.val();
    output_field.val(selected_options ? selected_options.length : 0);
}

var d_boy_cash = 0;
$("#delivery_boys_details").on("check.bs.table", function (e, row) {
    d_boy_cash = row.cash_received;
    $("#details").val(
        "Id: " +
        row.id +
        " | Name:" +
        row.username +
        " | Mobile: " +
        row.mobile +
        " | Cash: " +
        row.cash_received
    );
    $("#delivery_boy_id").val(row.id);
});

function validate_amount() {
    var cash = $(".delivery_boy_cash_recived").val();
    var amount = $("#amount").val();
    var details_val = $("#details").val();
    if (details_val == "") {
        iziToast.error({
            message:
                "<span>you have to select delivery boy to collect cash.</span> ",
        });
        $("#amount").val("");
    } else {
        if (parseInt(cash) > 0) {
            if (parseInt(amount) > parseInt(cash)) {
                iziToast.error({
                    message:
                        "<span>You Can not enter amount greater than cash</span> ",
                });
                $("#amount").val("");
            }
            if (parseInt(amount) <= 0) {
                iziToast.error({
                    message: "<span>Amount must be greater than zero</span> ",
                });
                $("#amount").val("");
            }
        } else {
            iziToast.error({
                message: "<span>Cash must be greater than zero</span> ",
            });
            $("#amount").val("");
        }
    }
}

$(window).on("load", function () {
    var store_ids = $('input[name="edit_store_ids[]"]').val();
    if (store_ids != null && store_ids != undefined) {
        store_ids = store_ids.split(",");
        var store_id = $('input[name="edit_store_id"]').val();
        var store_name = $('input[name="edit_store_name"]').val();
        var store_url = $('input[name="edit_store_url"]').val();
        var store_description = $('input[name="edit_store_description"]').val();
        var store_logo = $('input[name="edit_store_logo"]').val();
        var store_thumbnail = $('input[name="edit_store_thumbnail"]').val();
        var store_detail = [];
        store_detail = JSON.parse($('input[name="edit_store_detail[]"]').val());

        var html = "";
        for (var i = 0; i < store_ids.length; i++) {
            if (store_ids[i] != store_id) {
                var storeNamne =
                    store_detail[i]["id"] == store_ids[i]
                        ? store_detail[i]["name"]
                        : "";

                if (
                    JSON.parse(store_logo) != "" &&
                    JSON.parse(store_logo) != undefined
                ) {
                    var imageUrl =
                        appUrl +
                        "storage/" +
                        JSON.parse(store_logo)[store_ids[i]]["store_logo"];
                } else {
                    var imageUrl = "";
                }
                if (
                    JSON.parse(store_thumbnail) != "" &&
                    JSON.parse(store_thumbnail) != undefined
                ) {
                    var thumbnailUrl =
                        appUrl +
                        "storage/" +
                        JSON.parse(store_thumbnail)[store_ids[i]][
                        "store_thumbnail"
                        ];
                } else {
                    var thumbnailUrl = "";
                }
                imageUrl = html =
                    '<div class="divider_title">' +
                    storeNamne +
                    " Store Info" +
                    "</div><hr>" +
                    '<div class="row">' +
                    '<div class="mb-3 col-md-6">' +
                    '<label class="form-label" for="store_name">Name <span class="text-danger text-sm">*</span></label>' +
                    '<div class="input-group input-group-merge">' +
                    '<input type="text" name="store_name[]" class="form-control" placeholder="starbucks" value="' +
                    JSON.parse(store_name)[store_ids[i]]["store_name"] +
                    '" />' +
                    "</div>" +
                    "</div>" +
                    '<div class="mb-3 col-md-6">' +
                    '<label class="form-label" for="store_url">Store URL <span class="text-danger text-sm">*</span></label>' +
                    '<div class="input-group input-group-merge">' +
                    '<input type="text" name="store_url[]" class="form-control" placeholder="starbucks" value="' +
                    JSON.parse(store_url)[store_ids[i]]["store_url"] +
                    '" />' +
                    "</div>" +
                    "</div>" +
                    "</div >" +
                    '<div class="row">' +
                    '<div class="form-group col-md-6">' +
                    '<div class="mb-3">' +
                    '<label class="form-label" for="basic-default-phone">Logo <span class="text-danger text-sm">*</span></label>' +
                    '<input type="file" accept="image/*" id="basic-default-phone" name="store_logo[]" class="form-control " placeholder="************">' +
                    '<img src="' +
                    imageUrl +
                    '" alt="user-avatar" class="d-block rounded mt-2" height="100" width="100" id="uploadedAvatar" />' +
                    "</div>" +
                    "</div>" +
                    '<div class="form-group col-md-6">' +
                    '<div class="mb-3">' +
                    '<label class="form-label" for="basic-default-phone">Store Thumbnail <span class="text-danger text-sm">*</span></label>' +
                    '<input type="file" accept="image/*" id="basic-default-phone" name="store_thumbnail[]" class="form-control phone-mask">' +
                    '<img src="' +
                    thumbnailUrl +
                    '" alt="user-avatar" class="d-block rounded mt-2" height="100" width="100" id="uploadedAvatar" />' +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    '<div class="row">' +
                    '<div class="form-group col-md-12">' +
                    '<div class="mb-3">' +
                    '<label class="form-label" for="basic-default-company">Description <span class="text-danger text-sm">*</span></label>' +
                    '<textarea id="basic-default-message" value="" name="description[]" class="form-control" placeholder="Write some description here">' +
                    JSON.parse(store_description)[store_ids[i]][
                    "store_description"
                    ] +
                    "</textarea>" +
                    "</div>" +
                    "</div>" +
                    "</div>" +
                    "</div >";

                $("#edit_store_details").append(html);
            }
        }
    }
});

$(document).on("click", ".delete-img", function () {
    var isJson = false;
    var id = $(this).data("id");
    var path = $(this).data("path");
    var field = $(this).data("field");
    var img_name = $(this).data("img");
    var table_name = $(this).data("table");
    var t = this;
    var isjson = $(this).data("isjson");
    Swal.fire({
        title: "هل انت متاكد؟",
        text: "لن تتمكن من استعادة الصورة !",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "نعم, احذف!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: "POST",
                    url: appUrl + from + "/offers/delete_image",
                    data: {
                        id: id,
                        path: path,
                        field: field,
                        img_name: img_name,
                        table_name: table_name,
                        isjson: isjson,
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },
                    dataType: "json",
                    success: function (result) {
                        token = $('meta[name="csrf-token"]').attr("content");
                        if (result[0]["is_deleted"] == true) {
                            $(t).closest("div").remove();
                            Swal.fire("تم حدف الصورة بنجاح", "", "success");
                        } else {
                            Swal.fire(
                                "عفوا...",
                                "حدث خطأ أثناء حذف الصورة",
                                "error"
                            );
                        }
                    },
                });
            });
        },
        allowOutsideClick: false,
    }).then((result) => {
        if (result.dismiss === Swal.DismissReason.cancel) {
            Swal.fire("تم الغاء!", "", "error");
        }
    });
});

$(document).on("click", ".update-seller-commission", function () {
    Swal.fire({
        title: "Are You Sure !",
        text: "You won't be able to revert this!",
        type: "info",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, settle commission!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: appUrl + "admin/cronjob/settleSellerCommission",
                    type: "GET",
                    data: {
                        is_date: true,
                    },
                    dataType: "json",
                })
                    .done(function (response, textStatus) {
                        if (response.error == false) {
                            Swal.fire("Done!", response.message, "success");
                            $("table").bootstrapTable("refresh");
                        } else {
                            if (response.message) {
                                Swal.fire(
                                    "Oops...",
                                    response.message,
                                    "warning"
                                );
                            } else if (response.error_message) {
                                Swal.fire(
                                    "Oops...",
                                    response.error_message,
                                    "warning"
                                );
                            }
                        }
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        Swal.fire(
                            "Oops...",
                            "Something went wrong with ajax !",
                            "error"
                        );
                    });
            });
        },
        allowOutsideClick: false,
    });
});

$("#customers").on("check.bs.table", function (e, row) {
    $("#customer_dtls").val(row.name + " | " + row.email);
    $("#user_id").val(row.id);
});

$(function () {
    // Initialize Select2
    $("#customerSelect").select2({
        ajax: {
            url: "/customers/list",
            dataType: "json",
            data: {
                status: 1,
            },
            delay: 250,
            processResults: function (data) {
                return {
                    results: data.rows.map(function (row) {
                        return {
                            id: row.id,
                            text: row.name + " | " + row.email,
                        };
                    }),
                };
            },
            cache: true,
        },
        placeholder: "Select a customer",
        minimumInputLength: 1,
        dropdownParent: $(".customer_wallet_transaction_parent"),
    });

    // Handle selection change
    $("#customerSelect").on("select2:select", function (e) {
        var selectedUser = e.params.data;
        $("#user_id").val(selectedUser.id);
    });
});

$(document).on("click", ".view_ticket", function (e, row) {
    e.preventDefault();
    scrolled = 0;
    $(".ticket_msg").data("max-loaded", false);
    ticket_id = $(this).data("id");
    var username = $(this).data("username");
    var date_created = $(this).data("date_created");
    var subject = $(this).data("subject");
    var status = $(this).data("status");
    var ticket_type = $(this).data("ticket_type");
    $('input[name="ticket_id"]').val(ticket_id);
    $("#user_name").html(username);
    $("#date_created").html(date_created);
    $("#subject").html(subject);
    $(".change_ticket_status").data("ticket_id", ticket_id);

    if (status == 1) {
        $("#status").html(
            '<label class="badge bg-secondary ml-2">PENDING</label>'
        );
    } else if (status == 2) {
        $("#status").html('<label class="badge bg-info ml-2">OPENED</label>');
    } else if (status == 3) {
        $("#status").html(
            '<label class="badge bg-success ml-2">RESOLVED</label>'
        );
    } else if (status == 4) {
        $("#status").html('<label class="badge bg-danger ml-2">CLOSED</label>');
    } else if (status == 5) {
        $("#status").html(
            '<label class="badge bg-warning ml-2">REOPENED</label>'
        );
    }
    $("#ticket_type").html(ticket_type);
    $(".ticket_msg").html("");
    $(".ticket_msg").data("limit", 5);
    $(".ticket_msg").data("offset", 0);
    load_messages($(".ticket_msg"), ticket_id);
});

function load_messages(element, ticket_id) {
    var limit = element.data("limit");
    var offset = element.data("offset");

    element.data("offset", limit + offset);
    var max_loaded = element.data("max-loaded");
    if (max_loaded == false) {
        var loader =
            '<div class="loader text-center"><img src="' +
            appUrl +
            'assets/img/pre-loader.gif" alt="Loading. please wait.. ." title="Loading. please wait.. ."></div>';
        $.ajax({
            type: "get",
            data:
                "ticket_id=" +
                ticket_id +
                "&limit=" +
                limit +
                "&offset=" +
                offset,
            url: appUrl + "admin/tickets/get_ticket_messages",
            beforeSend: function () {
                $(".ticket_msg").prepend(loader);
            },
            dataType: "json",
            cache: false,
            contentType: false,
            processData: false,
            success: function (result) {
                if (result.error == false) {
                    if (result.error == false && result.data.length > 0) {
                        var messages_html = "";
                        var is_left = "";
                        var is_right = "";

                        var i = 1;
                        result.data.reverse().forEach((messages) => {
                            var atch_html = "";
                            is_left =
                                messages.user_type == "user" ? "left" : "right";
                            is_right =
                                messages.user_type == "user" ? "right" : "left";

                            if (messages.attachments.length > 0) {
                                messages.attachments.forEach((atch) => {
                                    atch_html +=
                                        "<div class='container-fluid image-upload-section ms-1'>" +
                                        "<a class='btn btn-danger btn-xs me-1 mb-1' href='" +
                                        atch.media +
                                        "'  target='_blank' alt='Attachment Not Found'>Attachment " +
                                        i +
                                        "</a>" +
                                        "<div class='col-md-3 col-sm-12 shadow p-3 mb-5 bg-white rounded m-4 text-center grow image d-none'></div>" +
                                        "</div>";
                                    i++;
                                });
                            }

                            messages_html +=
                                "<div class='direct-chat-msg " +
                                is_left +
                                "'>" +
                                "<div class='direct-chat-infos clearfix'>" +
                                "<span class='direct-chat-name float-" +
                                is_left +
                                "' id='name'>" +
                                " " +
                                messages.name +
                                " " +
                                "</span>" +
                                "<span class='direct-chat-timestamp float-" +
                                is_left +
                                "' id='last_updated'>" +
                                messages.updated_at +
                                " " +
                                "</span>" +
                                "</div>" +
                                "<div class='direct-chat-text float-" +
                                is_left +
                                "' id='message'>" +
                                messages.message +
                                "</br>" +
                                atch_html +
                                "</div>" +
                                "</div>";
                        });
                        $(".ticket_msg").prepend(messages_html);
                        $(".ticket_msg").find(".loader").remove();
                        $(element).animate({
                            scrollTop: $(element).offset().top,
                        });
                    }
                } else {
                    element.data("offset", offset);
                    element.data("max-loaded", true);
                    $(".ticket_msg").find(".loader").remove();
                    $(".ticket_msg").prepend(
                        '<div class="text-center"> <p>You have reached the top most message!</p></div>'
                    );
                }
                $("#element").scrollTop(20); // Scroll alittle way down, to allow user to scroll more
                $(element).animate({
                    scrollTop: $(element).offset().top,
                });
                return false;
            },
        });
    }
}

$("#ticket_send_msg_form").on("submit", function (e) {
    e.preventDefault();
    var formdata = new FormData(this);
    var csrfToken = document.head.querySelector(
        'meta[name="csrf-token"]'
    ).content;
    formdata.append("_token", csrfToken);

    $.ajax({
        type: "POST",
        url: $(this).attr("action"),
        data: formdata,
        cache: false,
        contentType: false,
        processData: false,
        dataType: "json",
        success: function (result) {
            token = $('meta[name="csrf-token"]').attr("content");
            $("#submit_btn").html("Send").attr("disabled", false);
            if (result.error == false) {
                $(".product-image-container").remove();
                if (result.data.id > 0) {
                    var message = result.data;
                    var is_left =
                        message.user_type == "user" ? "left" : "right";
                    var message_html = "";
                    var atch_html = "";
                    var i = 1;
                    if (message.attachments.length > 0) {
                        message.attachments.forEach((atch) => {
                            atch_html +=
                                "<div class='container-fluid image-upload-section ms-1'>" +
                                "<a class='btn btn-danger btn-xs me-1 mb-1' href='" +
                                atch.media +
                                "'  target='_blank' alt='Attachment Not Found'>Attachment " +
                                i +
                                "</a>" +
                                "<div class='col-md-3 col-sm-12 shadow p-3 mb-5 bg-white rounded m-4 text-center grow image d-none'></div>" +
                                "</div>";
                            i++;
                        });
                    }

                    message_html +=
                        "<div class='direct-chat-msg " +
                        is_left +
                        "'>" +
                        "<div class='direct-chat-infos clearfix'>" +
                        "<span class='direct-chat-name float-" +
                        is_left +
                        "' id='name'>" +
                        " " +
                        message.name +
                        " " +
                        "</span>" +
                        "<span class='direct-chat-timestamp float-" +
                        is_left +
                        "' id='last_updated'>" +
                        message.updated_at +
                        " " +
                        "</span>" +
                        "</div>" +
                        "<div class='direct-chat-text float-" +
                        is_left +
                        "'' id='message'>" +
                        message.message +
                        "</br>" +
                        atch_html +
                        "</div>" +
                        "</div>";

                    $(".ticket_msg").append(message_html);
                    $("#message_input").val("");

                    $("#element").scrollTop($("#element")[0].scrollHeight);
                    $('input[name="attachments[]"]').val("");
                } else {
                    // Handle the case when result.data.id is not greater than 0 (e.g., invalid data)
                    iziToast.error({
                        message:
                            '<span style="text-transform:capitalize">' +
                            result.data.message +
                            "</span>",
                    });
                }
            } else {
                // Handle the error case
                $("#element").data("max-loaded", true);
                iziToast.error({
                    message:
                        '<span style="text-transform:capitalize">' +
                        result.error_message +
                        "</span> ",
                });
                return false;
            }
            iziToast.success({
                message:
                    '<span style="text-transform:capitalize">' +
                    result.message +
                    "</span> ",
            });
        },
    });
});

$(function () {
    if ($("#element").length) {
        $("#element").scrollTop($("#element")[0].scrollHeight);
        $("#element").scroll(function () {
            if ($("#element").scrollTop() == 0) {
                load_messages($(".ticket_msg"), ticket_id);
            }
        });

        $("#element").bind("mousewheel", function (e) {
            if (e.originalEvent.wheelDelta / 120 > 0) {
                if ($(".ticket_msg")[0].scrollHeight < 370 && scrolled == 0) {
                    load_messages($(".ticket_msg"), ticket_id);
                    scrolled = 1;
                }
            }
        });
    }
});

$(document).on("change", ".change_ticket_status", function () {
    var status = $(this).val();
    if (status != "") {
        if (
            confirm(
                "Are you sure you want to mark the ticket as " +
                $(".change_ticket_status option:selected").text() +
                "? "
            )
        ) {
            var id = $(this).data("ticket_id");
            var dataString = {
                ticket_id: id,
                status: status,
                _token: $('meta[name="csrf-token"]').attr("content"),
            };
            $.ajax({
                type: "post",
                url: appUrl + "admin/tickets/editTicketStatus",
                data: dataString,
                dataType: "json",
                success: function (result) {
                    token = $('meta[name="csrf-token"]').attr("content");
                    if (result.error == false) {
                        $("#ticket_table").bootstrapTable("refresh");
                        if (status == 1) {
                            $("#status").html(
                                '<label class="badge bg-secondary ml-2">PENDING</label>'
                            );
                        } else if (status == 2) {
                            $("#status").html(
                                '<label class="badge bg-info ml-2">OPENED</label>'
                            );
                        } else if (status == 3) {
                            $("#status").html(
                                '<label class="badge bg-success ml-2">RESOLVED</label>'
                            );
                        } else if (status == 4) {
                            $("#status").html(
                                '<label class="badge bg-danger ml-2">CLOSED</label>'
                            );
                        } else if (status == 5) {
                            $("#status").html(
                                '<label class="badge bg-warning ml-2">REOPENED</label>'
                            );
                        }
                        iziToast.success({
                            message:
                                '<span style="text-transform:capitalize">' +
                                result.message +
                                "</span> ",
                        });
                        $("#ticket_modal").modal("hide");
                        $("#admin_ticket_table").bootstrapTable("refresh");
                    } else {
                        iziToast.error({
                            message: "<span>" + result.message + "</span> ",
                        });
                    }
                },
            });
        }
    }
});

// First register any plugins
FilePond.registerPlugin(
    FilePondPluginImagePreview,
    FilePondPluginFileValidateSize,
    FilePondPluginFileValidateType
);

// Turn input element into a pond

$(".filepond").filepond({
    credits: null,
    allowFileSizeValidation: "true",
    maxFileSize: "25MB",
    labelMaxFileSizeExceeded: "File is too large",
    labelMaxFileSize: "Maximum file size is {filesize}",
    allowFileTypeValidation: true,

    labelFileTypeNotAllowed: "File of invalid type",
    fileValidateTypeLabelExpectedTypes:
        "Expects {allButLastType} or {lastType}",
    storeAsFile: true,
    allowPdfPreview: true,
    pdfPreviewHeight: 320,
    pdfComponentExtraParams: "toolbar=0&navpanes=0&scrollbar=0&view=fitH",
    allowVideoPreview: true,
    allowAudioPreview: true,
    onprocessfile: function (error, file) {
        if (!error) {
            // Clear the image view area
            const pond = FilePond.create(
                document.querySelector(".filepond-input")
            );
            pond.removeFiles();
            $(".filepond--root .filepond--image-preview").html("");
        }
    },
});

// ======================================= Rating Code ========================================

$(".rateYo").each(function (e) {
    var ChngRatevaluesEn = {
        1: "bad",
        2: "poor",
        3: "ok",
        4: "good",
        5: "super",
    };
    var ChngRatevaluesAr = {
        1: "bad-Ar",
        2: "poor-Ar",
        3: "ok-Ar",
        4: "good-Ar",
        5: "super-Ar",
    };
    var language = "english";
    var rating = $(this).attr("data-rating");
    $(this).rateYo({
        onSet: function (rating) {
            if (language === "arabic") {
                $(this).next().val(ChngRatevaluesAr[rating]);
            } else {
                $(this).next().val(ChngRatevaluesEn[rating]);
            }
            ratingFunc(rating, $(this).next().next().val());
        },
        rating: rating,
        starWidth: "20px",
        numStars: 5,
        fullStar: true,
        normalFill: "#A0A0A0",
        spacing: "5px",
        precision: 2,
    });
});

function ratingFunc(rating, bookid, lang) {
    debugger;
    if (lang != null) {
        language = lang;
    }
}

// ================================= seller statistics chart ======================================
if (from == "seller") {
    $(function () {
        function getTopSellingProducts(category_id) {
            $.ajax({
                type: "get",
                url: appUrl + "seller/topSellingProducts",
                data: { category_id: category_id },
                dataType: "json",
                success: function (result) {
                    token = $('meta[name="csrf-token"]').attr("content");
                    var productHtml = "";
                    $.each(result.data, function (index, product) {
                        // Create the product HTML

                        var imageUrl =
                            product.product_image.indexOf("https:") === -1
                                ? product.product_image !== ""
                                    ? appUrl +
                                    "storage/" +
                                    product.product_image
                                    : appUrl + "assets/img/no-image.jpg"
                                : product.product_image;

                        productHtml += `
                        <div class="top-selling-product-list d-flex align-items-center">
                            <p class="body-default m-0">${index + 1}.</p>
                            <div >
                                <img src="${imageUrl}" alt="${product.name
                            }" class="product-img-box">
                            </div>
                            <div>
                                <p class="lead mb-2">${product.name}</p>
                                <div class="d-flex total-product-sale">
                                    <i class='bx bx-badge-check body-default me-1'></i>
                                    <p class="body-default m-0">Sold: ${product.total_sold
                            }</p>
                                </div>
                            </div>
                        </div>
                    `;

                        // Append the product HTML to the container
                    });
                    $(".top-selling-products").html(productHtml);
                },
            });
        }

        function getMostPopularProducts(category_id) {
            $.ajax({
                type: "get",
                url: appUrl + "seller/mostPopularProduct",
                data: { category_id: category_id },
                dataType: "json",
                success: function (result) {
                    token = $('meta[name="csrf-token"]').attr("content");

                    var productHtml = "";
                    $.each(result.data, function (index, product) {
                        // Create the product HTML

                        var imageUrl =
                            product.product_image.indexOf("https:") === -1
                                ? product.product_image !== ""
                                    ? appUrl +
                                    "storage/" +
                                    product.product_image
                                    : appUrl + "assets/img/no-image.jpg"
                                : product.product_image;

                        productHtml += `
                        <div class="most-popular-product-list d-flex align-items-center">
                            <p class="body-default m-0">${index + 1}.</p>
                            <div >
                                <img src="${imageUrl}" alt="${product.name
                            }" class="product-img-box">
                            </div>
                            <div>
                                <p class="lead mb-2">${product.name}</p>
                                <div class="d-flex total-product-sale">
                                    <i class='bx bxs-star body-default me-1'></i>
                                    <p class="body-default me-1 product-rating">${product.average_rating
                            }</p>
                                    <p class="body-default m-0 total-reviews">(${product.total_reviews
                            } Reviews)</p>
                                </div>
                            </div>
                        </div>
                    `;
                    });
                    $(".most-popular-products").html(productHtml);
                },
            });
        }

        // Attach change event handler to the select element
        $("#top_selling_product_filter").on("change", function () {
            // Get the selected filter value
            var selectedCategoryId = $(this).val();
            // Call the function with the selected filter

            getTopSellingProducts(selectedCategoryId);
        });
        $("#most_popular_product_filter").on("change", function () {
            // Get the selected filter value
            var selectedCategoryId = $(this).val();
            // Call the function with the selected filter
            getMostPopularProducts(selectedCategoryId);
        });

        $(function () {
            getMostPopularProducts();
            getTopSellingProducts();
        });
    });
}

/* ---------------------------------------------------------------------------------------------------------
                                        daynamic filter using canvas
--------------------------------------------------------------------------------------------------------- */

var tableName = "";
$(function () {
    $(document).on("click", "#tableFilter", function () {
        var dateFilter = $(this).attr("dateFilter");
        var orderStatusFilter = $(this).attr("orderStatusFilter");
        var advertismentStatusFilter = $(this).attr("advertismentStatusFilter");
        var advertismentPackagesFilter = $(this).attr("advertismentPackagesFilter");
        var paymentMethodFilter = $(this).attr("paymentMethodFilter");
        var orderTypeFilter = $(this).attr("orderTypeFilter");
        var categoryFilter = $(this).attr("categoryFilter");
        var productStatusFilter = $(this).attr("productStatusFilter");
        var offerTypeFilter = $(this).attr("offerTypeFilter");
        var cashCollectionTypeFilter = $(this).attr("cashCollectionTypeFilter");
        var deliveryBoyFilter = $(this).attr("deliveryBoyFilter");
        var paymentRequestStatusFilter = $(this).attr(
            "paymentRequestStatusFilter"
        );
        var sellerFilter = $(this).attr("sellerFilter");
        var StatusFilter = $(this).attr("StatusFilter");
        var blogCategoryFilter = $(this).attr("blogCategoryFilter");

        if (dateFilter == "true") {
            $(".dateRangeFilter").removeClass("d-none");
        } else {
            $(".dateRangeFilter").addClass("d-none");
        }
        if (orderStatusFilter == "true") {
            $(".orderStatusFilter").removeClass("d-none");
        } else {
            $(".orderStatusFilter").addClass("d-none");
        }
        if (advertismentStatusFilter == "true") {
            $(".advertismentStatusFilter").removeClass("d-none");
        } else {
            $(".advertismentStatusFilter").addClass("d-none");
        }
        if (advertismentPackagesFilter == "true") {
            load_packages();
            $(".advertismentPackagesFilter").removeClass("d-none");
        } else {
            $(".advertismentPackagesFilter").addClass("d-none");
        }
        if (paymentMethodFilter == "true") {
            $(".paymentMethodFilter").removeClass("d-none");
        } else {
            $(".paymentMethodFilter").addClass("d-none");
        }
        if (orderTypeFilter == "true") {
            $(".orderTypeFilter").removeClass("d-none");
        } else {
            $(".orderTypeFilter").addClass("d-none");
        }
        if (categoryFilter == "true") {
            load_seller_category();
            $(".categoryFilter").removeClass("d-none");
        } else {
            $(".categoryFilter").addClass("d-none");
        }
        if (productStatusFilter == "true") {
            $(".productStatusFilter").removeClass("d-none");
        } else {
            $(".productStatusFilter").addClass("d-none");
        }
        if (offerTypeFilter == "true") {
            $(".offerTypeFilter").removeClass("d-none");
        } else {
            $(".offerTypeFilter").addClass("d-none");
        }
        if (paymentRequestStatusFilter == "true") {
            $(".paymentRequestStatusFilter").removeClass("d-none");
        } else {
            $(".paymentRequestStatusFilter").addClass("d-none");
        }
        if (sellerFilter == "true") {
            $(".sellerFilter").removeClass("d-none");
        } else {
            $(".sellerFilter").addClass("d-none");
        }
        if (StatusFilter == "true") {
            $(".StatusFilter").removeClass("d-none");
        } else {
            $(".StatusFilter").addClass("d-none");
        }
        if (blogCategoryFilter == "true") {
            load_blog_category();
            $(".blogCategoryFilter").removeClass("d-none");
        } else {
            $(".blogCategoryFilter").addClass("d-none");
        }
        if (cashCollectionTypeFilter == "true") {
            $(".cashCollectionTypeFilter").removeClass("d-none");
        } else {
            $(".cashCollectionTypeFilter").addClass("d-none");
        }
       

        $("#filtersOffcanvas").offcanvas("toggle");
        tableName = $(this).data("table");

        setupColumnCheckboxes(tableName);
    });
});

$(document).on("click", "#tableRefresh", function (e) {
    var table_name = $(this).data("table");
    $("#" + table_name).bootstrapTable("refresh");
});

// when change pagination

$(document).on("page-change.bs.table", function (e, pageNumber, pageSize) {
    const table = $(e.target); // Get the table element using the event target
    const tableName = table.attr("id");
    const dataUrl = $("#" + tableName).attr("data-url");
    const data_query_params = $("#" + tableName).attr("data-query-params");

    // Calculate limit and offset
    const pagination_limit = pageSize;
    const pagination_offset = (pageNumber - 1) * pageSize;

    if (tableName !== "admin_seller_wallet_table") {
        if (dataUrl) {
            try {
                // Create a URL object from the dataUrl
                const urlWithSearchParams = new URL(
                    dataUrl,
                    window.location.origin
                );
                const existingParams = urlWithSearchParams.searchParams;

                // Date range picker logic
                var drp = $("#datepicker").data("daterangepicker");

                if (drp) {
                    var startDate = drp.startDate.format("YYYY-MM-DD");
                    var endDate = drp.endDate.format("YYYY-MM-DD");
                }

                // Add custom parameters
                const paramsToSet = [
                    { key: "order_status", selector: "#order_status" },
                    { key: "search", selector: ".searchInput" },
                    { key: "payment_method", selector: "#payment_method" },
                    { key: "order_type", selector: "#order_type" },
                    { key: "category_id", selector: "#category_id" },
                    { key: "status_filter", selector: "#status_filter" },
                    { key: "seller_id", selector: "#filterSellerId" },
                    { key: "status", selector: "#statusFilter" },
                    {
                        key: "offer_type_filter",
                        selector: "#offer_type_filter",
                    },
                    { key: "admin_brand_list", selector: "#admin_brand_list" },
                    {
                        key: "payment_request_status",
                        selector: "#payment_request_status_filter",
                    },
                    { key: "blog_category_id", selector: "#blog_category_id" },
                    { key: "ads_package_id", selector: "#ads_package_id" },
                    {
                        key: "filter_status",
                        selector: "#cash_collection_status",
                    },
                    { key: "delivery_boy", selector: "#delivery_boy" },
                    { key: "advertisment_status_filter", selector: "#advertisment_status_filter" },
                ];

                // Set parameters based on the input fields
                paramsToSet.forEach((param) => {
                    const value = $(param.selector).val();
                    if (value) {
                        existingParams.set(param.key, value);
                    }
                });

                // Add limit and offset parameters
                existingParams.set("pagination_limit", pagination_limit);
                existingParams.set("pagination_offset", pagination_offset);

                // Refresh the table with the modified URL
                $(table).bootstrapTable("refresh", {
                    url: urlWithSearchParams.toString(), // Convert URL object back to string
                    query: Object.fromEntries(existingParams), // Convert URLSearchParams to a plain object for query
                });
            } catch (error) {
                console.error("Invalid URL:", error); // Log any errors encountered
            }
        } else {
            console.error("data-url is not defined or invalid.");
        }
    }
});

function getTableColumnNames(tableName, propertyName) {
    const columns = $("#" + tableName).bootstrapTable("getOptions").columns[0];

    if (propertyName == "columnTitle") {
        return columns.map((column) => column["passed"].title);
    }
    if (propertyName == "columnField") {
        return columns.map((column) => column.field);
    }
}

function setupColumnCheckboxes(tableName) {
    const columnNames = getTableColumnNames(tableName, "columnField");
    const columnTitles = getTableColumnNames(tableName, "columnTitle");
    const offcanvasBody = document.getElementById("columnFilterOffcanvasBody");
    offcanvasBody.innerHTML = "";
    // Create a container for all rows with padding
    const containerDiv = document.createElement("div");
    containerDiv.classList.add("container-fluid");
    offcanvasBody.appendChild(containerDiv);
    // Create a row container
    rowContainer = document.createElement("div");
    rowContainer.classList.add("row");
    for (let i = 0; i < columnNames.length; i++) {
        const columnName = columnNames[i];
        const columnTitle = columnTitles[i];
        // Create a form-check element

        const thElements = $("#" + tableName).find(
            "th[data-field][data-disabled='1']"
        );
        const disabledColumnNames = [];

        thElements.each(function () {
            const columnName = $(this).attr("data-field");
            disabledColumnNames.push(columnName);
        });

        // Iterate over each column name fetched from disabledColumnNames array
        disabledColumnNames.forEach(function (columnName) {
            const checkboxElement = $("#" + columnName);
            // Use .prop() method to set the disabled property to true
            checkboxElement.prop("disabled", true);
        });

        const checkbox = document.createElement("div");
        checkbox.classList.add("form-check", "col-6"); // Use col-6 to have 2 elements in 1 row
        checkbox.innerHTML = `
            <input class="form-check-input" type="checkbox" id="${columnName}">
            <label class="form-check-label" for="${columnName}">${columnTitle}</label>
        `;

        // Append the form-check to the row container
        rowContainer.appendChild(checkbox);
        // If two elements have been added to the row, create a new row
        if ((i + 1) % 2 === 0 || i === columnNames.length - 1) {
            containerDiv.appendChild(rowContainer);
            // Create a new row container
            rowContainer = document.createElement("div");
            rowContainer.classList.add("row");
        }
    }
    for (let i = 0; i < columnNames.length; i++) {
        const columnName = columnNames[i];
        // Check the checkboxes based on the columns already shown in the table
        const visibleColumns = $("#" + tableName)
            .bootstrapTable("getVisibleColumns")
            .map((column) => column.field);
        if (visibleColumns.includes(columnName)) {
            const checkboxElement = document.querySelector(
                `.form-check-input[id="${columnName}"]`
            );
            const idCheckbox = document.querySelector("#id.form-check-input");
            if (checkboxElement) {
                checkboxElement.checked = true;
            }
            if (checkboxElement == idCheckbox) {
                checkboxElement.disabled = true;
            }
        }
    }
}

// Function to apply dynamic column filters
function applyDynamicColumnFilters(tableName) {
    const checkedCheckboxes = document.querySelectorAll(
        "#filtersOffcanvas input:checked"
    );
    const checkedColumnNames = Array.from(checkedCheckboxes).map(
        (checkbox) => checkbox.id
    );

    for (const columnName of getTableColumnNames(tableName, "columnField")) {
        const isColumnVisible = checkedColumnNames.includes(columnName);
        $("#" + tableName).bootstrapTable(
            isColumnVisible ? "showColumn" : "hideColumn",
            columnName
        );
    }

    // Close the offcanvas

    $("#filtersOffcanvas").offcanvas("hide");
}

function uncheckAllCheckboxes() {
    const checkboxes = document.querySelectorAll(
        '#columnFilterOffcanvasBody input[type="checkbox"]'
    );

    const thElements = $("#" + tableName).find(
        "th[data-field][data-disabled='1']"
    );
    const disabledColumnNames = [];

    thElements.each(function () {
        const columnName = $(this).attr("data-field");
        disabledColumnNames.push(columnName);
    });

    checkboxes.forEach((checkbox) => {
        const idCheckbox = document.querySelector("#id.form-check-input");
        if (
            checkbox !== idCheckbox &&
            !disabledColumnNames.includes(checkbox.id)
        ) {
            // Uncheck the checkbox if it's not the ID checkbox or disabled
            checkbox.checked = false;
        } else {
            // Disable the checkbox if it's the ID checkbox or disabled
            checkbox.disabled = true;
        }
    });

    // Reset other filter dropdown values to blank
    $("#status_filter").val("");
    $("#statusFilter").val("");
    $("#payment_method").val("");
    $("#order_type").val("");
    $("#order_status").val("");
    $("#filterSellerId").val("");
    $("#category_id").val("");
    $("#admin_brand_list").val("").trigger("change");
    $("#offer_type_filter").val("");
    $("#blog_category_id").val("");
    $("#ads_package_id").val("");
    $("#cash_collection_status").val("");
    $("#delivery_boy").val("");
    $("#payment_request_status_filter").val("");
    $("#advertisment_status_filter").val("");
}
var userSelectedDates = false;
$(document).on("click", "#tableFilterBtn", function (e) {
    applyDynamicColumnFilters(tableName);
    var OrderStatus = $("#order_status").val();
    var paymentMethod = $("#payment_method").val();
    var orderType = $("#order_type").val();
    var categoryId = $("#category_id").val();
    var productStatus = $("#status_filter").val();
    var offerType = $("#offer_type_filter").val();
    var brand_id = $("#admin_brand_list").val();
    var payment_request_status = $("#payment_request_status_filter").val();
    var seller_id = $("#filterSellerId").val();
    var status = $("#statusFilter").val();
    var blogCategoryId = $("#blog_category_id").val();
    var adsPackageId = $("#ads_package_id").val();
    var cashCollectionType = $("#cash_collection_status").val();
    var deliveryBoyFilter = $("#delivery_boy").val();
    var advertismentStatus = $("#advertisment_status_filter").val();


    var drp = $("#datepicker").data("daterangepicker");
    if (userSelectedDates) {
        var startDate = drp.startDate.format("YYYY-MM-DD");
        var endDate = drp.endDate.format("YYYY-MM-DD");
        // Perform the filtering using startDate and endDate
    }

    // Use the selected value in the Bootstrap Table refresh
    const dataUrl = $("#" + tableName).attr("data-url");
    $("#" + tableName).bootstrapTable("refresh", {
        url: dataUrl,
        query: {
            order_status: OrderStatus,
            payment_method: paymentMethod,
            order_type: orderType,
            start_date: startDate,
            end_date: endDate,
            category_id: categoryId,
            productStatus: productStatus,
            offer_type: offerType,
            brand_id: brand_id,
            payment_request_status: payment_request_status,
            seller_id: seller_id,
            status: status,
            blogCategoryId: blogCategoryId,
            PackageId: adsPackageId,
            cashCollectionType: cashCollectionType,
            deliveryBoyFilter: deliveryBoyFilter,
            advertisment_status: advertismentStatus,
        },
    });
});

$(document).on("click", ".reset_filter_button", function (e) {
    uncheckAllCheckboxes();
});

$(".searchInput").on("input", function () {
    var searchTable = $(this).data("table");
    const searchText = $(this).val().toLowerCase();
    const dataUrl = $("#" + searchTable).attr("data-url");
    // Create a URL object from the dataUrl
    const urlWithSearchParams = new URL(dataUrl);
    // Get existing query parameters
    const existingParams = urlWithSearchParams.searchParams.toString();
    // Set the search parameter
    urlWithSearchParams.searchParams.set("search", searchText);
    // Add back existing query parameters
    if (existingParams) {
        urlWithSearchParams.search = existingParams;
    }
    // Refresh the table with the modified URL
    $("#" + searchTable).bootstrapTable("refresh", {
        url: urlWithSearchParams.toString(),
    });
});

function exportTableData(ExportTable, exportType) {
    const exportOptions = {
        fileName: ExportTable + "-list",
        ignoreColumn: ["operate"],
    };
    $("#" + ExportTable).tableExport({
        type: exportType,
        escape: "false", // Add this line to prevent HTML entities encoding
        ...exportOptions,
    });
}

/* -----------------------------------------------------------------------------------------------------
                                        offcanvas table filters
----------------------------------------------------------------------------------------------------- */

$(function () {
    userSelectedDates = false;

    function cb(start, end) {
        if (!start || !end || start.isSame(end, "day")) {
            $("#datepicker span").html("اختر التاريخ");
        } else {
            $("#datepicker span").html(
                start.format("MMMM D, YYYY") +
                " - " +
                end.format("MMMM D, YYYY")
            );
            userSelectedDates = true; // Mark that the user has selected dates
        }
    }

    $("#datepicker span").html("اختر التاريخ");

    $("#datepicker").daterangepicker(
        {
            autoUpdateInput: false,
            locale: {
                cancelLabel: "مسح",
            },
            ranges: {

                "اليوم": [moment(), moment()],
                'امس': [moment().subtract(1, "days"), moment().subtract(1, "days")],
                "اخر 7 ايام": [moment().subtract(6, "days"), moment()],
                "اخر 30 يوم": [moment().subtract(29, "days"), moment()],
                "هذا الشهر": [
                    moment().startOf("month"),
                    moment().endOf("month"),
                ],
                "الشهر الماضي": [
                    moment().subtract(1, "month").startOf("month"),
                    moment().subtract(1, "month").endOf("month"),
                ],
            },
        },
        cb
    );

    // Handle the apply event to update the display
    $("#datepicker").on("apply.daterangepicker", function (ev, picker) {
        cb(picker.startDate, picker.endDate);
    });

    // Handle the cancel event to reset the display
    $("#datepicker").on("cancel.daterangepicker", function (ev, picker) {
        $("#datepicker span").html("اختر التاريخ");
        userSelectedDates = false; // Reset the flag as no dates are selected
    });

    // Manually trigger the callback function to set initial state
    cb(null, null);
});

// Use event delegation to handle click events on dynamic elements
$(document).on("click", ".changeLang", function (e) {
    e.preventDefault(); // Prevent the default behavior of the anchor tag

    var url = appUrl + from + "/settings/languages/change";
    var selectedLang = $(this).data("lang-code");

    iziToast.success({
        message: "Language Set Successfully",
    });

    window.location.href = url + "?lang=" + selectedLang;
});

$(function () {
    var systemLang = $("#current-lang").val();
    $("#language-settings").val(systemLang);
    $("#language-settings").on("change", function (e) {
        e.preventDefault();
    });

    $(`#lang-${systemLang}`).addClass("active");

    const languageLinks = document.querySelectorAll(".languages a");

    languageLinks.forEach((link) => {
        link.addEventListener("click", (event) => {
            // Remove the "active" class from all links
            languageLinks.forEach((link) => link.classList.remove("active"));
            // Add the "active" class to the clicked link
            link.classList.add("active");
            // Perform any other actions needed when a language is selected
        });
    });
});

$("#edit_transaction_form").on("submit", function (e) {
    e.preventDefault();
    var formdata = new FormData(this);
    var csrfToken = document.head.querySelector(
        'meta[name="csrf-token"]'
    ).content;
    formdata.append("_token", csrfToken);
    $.ajax({
        type: "POST",
        url: $(this).attr("action"),
        data: formdata,
        beforeSend: function () {
            $("#submit_btn").html("Please Wait..").attr("disabled", true);
        },
        cache: false,
        contentType: false,
        processData: false,
        dataType: "json",
        success: function (result) {
            $("#submit_btn").html("Update Transaction").attr("disabled", false);
            if (result.error == false) {
                $("table").bootstrapTable("refresh");
                iziToast.success({
                    message:
                        '<span style="text-transform:capitalize">' +
                        result.message +
                        "</span> ",
                });
                $("#transaction_modal").modal("hide");
            } else {
                if (result.error_message) {
                    iziToast.error({
                        message: result.error_message,
                    });
                } else if (result.message) {
                    iziToast.error({
                        message: result.message,
                    });
                }
            }
        },
        error: function (xhr, status, error) {
            if (xhr.responseJSON && xhr.responseJSON.errors) {
                var errors = xhr.responseJSON.errors;
                // Display each error message in a separate toast
                $.each(errors, function (field, errorMessages) {
                    if (Array.isArray(errorMessages)) {
                        $.each(errorMessages, function (index, errorMessage) {
                            iziToast.error({
                                title: "Error",
                                message: errorMessage,
                                position: "topRight",
                            });
                        });
                    } else {
                        iziToast.error({
                            title: "Error",
                            message: errorMessages,
                            position: "topRight",
                        });
                    }
                });
                $("#submit_btn")
                    .html("Update Transaction")
                    .attr("disabled", false);
            } else {
                $("#submit_btn")
                    .html("Update Transaction")
                    .attr("disabled", false);
                iziToast.error({
                    title: "Error",
                    message: xhr.responseJSON.message,
                    position: "topRight",
                });
            }
        },
    });
});

$(document).on("click", ".edit_transaction", function (e, row) {
    e.preventDefault();

    var id = $(this).data("id");
    var txn_id = $(this).data("txn_id");
    var status = $(this).data("status");
    var message = $(this).data("message");

    $("#id").val(id);
    $("#txn_id").val(txn_id);
    $("#t_status").val(status);
    $("#transaction_message").val(message);

    $('#t_status option[value="' + status + '"]').prop("selected", true);
});

$("#media-type").on("change", function () {
    var type = $(this).val();

    $.ajax({
        method: "GET",
        url: appUrl + from + "/media",
        data: {
            type: type,
            _token: $('meta[name="csrf-token"]').attr("content"),
        },
        success: function (response) {
            // Replace the content of the .media-card div with the updated data
            $(".media-card-container").html(
                $(response).find(".media-card-container").html()
            );
        },
        error: function (xhr, status, error) {
            iziToast.error({
                title: "Error",
                message: "Failed to fetch media data. Please try again later.",
                position: "topRight",
            });
        },
    });
});

$("#search_products").on("keyup", function (e) {
    e.preventDefault();
    var search = $(this).val();

    $.ajax({
        method: "GET",
        url: appUrl + from + "/media",
        data: {
            search: search,
            _token: $('meta[name="csrf-token"]').attr("content"),
        },
        success: function (response) {
            // Replace the content of the .media-card div with the updated data
            $(".media-card-container").html(
                $(response).find(".media-card-container").html()
            );
        },
        error: function (xhr, status, error) {
            iziToast.error({
                title: "Error",
                message: "Failed to fetch media data. Please try again later.",
                position: "topRight",
            });
        },
    });
});
function salesReport(index, row) {
    var html = [];
    var indexs = 0;

    $.each(row, function (key, value) {
        var columns = $("th:eq(" + (indexs + 1) + ")").data("field");
        if (columns != undefined && columns !== "state") {
            html.push("<p><b>" + columns + " :</b> " + row[columns] + "</p>");
            indexs++;
        }
    });

    return html;
}
$(function () {
    $(".change_variant_status").on("change", function (e) {
        var id = $(this).data("id");
        var status = $(this).data("status");
        var product_id = $(this).data("product-id");

        $.ajax({
            method: "GET",
            url: appUrl + from + "/product/change_variant_status",
            data: {
                _token: $('meta[name="csrf-token"]').attr("content"),
                id: id,
                status: status,
                product_id: product_id,
            },
            success: function (response) {
                if (response.error === true && response.error_message) {
                    iziToast.error({
                        title: "Error",
                        message: response.error_message,
                        position: "topRight",
                    });
                } else {
                    iziToast.success({
                        title: "Success",
                        message: response.message,
                        position: "topRight",
                    });
                    setTimeout(function () {
                        location.reload();
                    }, 2000);
                }
            },
            error: function (xhr, status, error) {
                iziToast.error({
                    title: "Error",
                    message: "Failed to update. Please try again later.",
                    position: "topRight",
                });
            },
        });
    });
});

$(function () {
    $(".delete_variant").on("click", function (e) {
        var id = $(this).data("id");
        var status = $(this).data("status");
        var product_id = $(this).data("product-id");

        $.ajax({
            method: "GET",
            url: appUrl + from + "/product/delete_variant",
            data: {
                _token: $('meta[name="csrf-token"]').attr("content"),
                id: id,
                status: status,
                product_id: product_id,
            },
            success: function (response) {
                if (response.error === true && response.error_message) {
                    iziToast.error({
                        title: "Error",
                        message: response.error_message,
                        position: "topRight",
                    });
                } else {
                    iziToast.success({
                        title: "Success",
                        message: response.message,
                        position: "topRight",
                    });
                    setTimeout(function () {
                        location.reload();
                    }, 2000);
                }
            },
            error: function (xhr, status, error) {
                iziToast.error({
                    title: "Error",
                    message: "Failed to update. Please try again later.",
                    position: "topRight",
                });
            },
        });
    });
});
$(function () {
    $(document).on("click", ".camera_icon_div", function (e) {
        $("#store_logo_file_upload").trigger("click");
    });
});

$(function () {
    $(document).on("click", ".change_banner_button", function (e) {
        $("#store_thumbnail_file_upload").trigger("click");
    });
});

$("#update_receipt_status").on("change", function (e) {
    e.preventDefault();
    var order_id = $(this).data("id");
    var user_id = $(this).data("user_id");
    var status = $(this).val();
    $.ajax({
        type: "POST",
        data: {
            order_id: order_id,
            status: status,
            user_id: user_id,
            _token: $('meta[name="csrf-token"]').attr("content"),
        },
        url: appUrl + "admin/orders/update_receipt_status",
        dataType: "json",
        success: function (result) {
            csrfName = result.csrfName;
            csrfHash = result.csrfHash;
            if (result["error"] == false) {
                iziToast.success({
                    message: result["message"],
                });
                setTimeout(function () {
                    // Redirect to the appropriate URL
                    window.location.reload();
                }, 3000);
            } else {
                iziToast.error({
                    message: result["message"],
                });
            }
        },
    });
});

//settle promocode discount

$(document).on("click", ".add_promo_code_discount", function () {
    Swal.fire({
        title: "Are You Sure !",
        text: "You won't be able to revert this!",
        type: "info",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, settle Discount!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: appUrl + "admin/cronjob/settleCashbackDiscount",
                    type: "GET",
                    data: {
                        is_date: true,
                    },
                    dataType: "json",
                })
                    .done(function (response, textStatus) {
                        if (response.error == false) {
                            Swal.fire("Done!", response.message, "success");
                            $("table").bootstrapTable("refresh");
                        } else {
                            if (response.message) {
                                Swal.fire(
                                    "Oops...",
                                    response.message,
                                    "warning"
                                );
                            } else if (response.error_message) {
                                Swal.fire(
                                    "Oops...",
                                    response.error_message,
                                    "warning"
                                );
                            }
                        }
                    })
                    .fail(function (jqXHR, textStatus, errorThrown) {
                        Swal.fire(
                            "Oops...",
                            "Something went wrong with ajax !",
                            "error"
                        );
                    });
            });
        },
        allowOutsideClick: false,
    });
});
$(document).on("change", ".set_default_storage_type", function () {
    var url = $(this).data("url");
    var id = $(this).data("id");
    $.ajax({
        method: "GET",
        url: url,
        data: {
            _token: $('meta[name="csrf-token"]').attr("content"),
            id: id,
        },
        success: function (response) {
            if (response.error) {
                iziToast.error({
                    title: "Error",
                    message: response.error,
                    position: "topRight",
                });
                $(".table").bootstrapTable("refresh");
            } else {
                iziToast.success({
                    title: "Success",
                    message: "Storage type set Successfully",
                    position: "topRight",
                });
                $(".table").bootstrapTable("refresh");
            }
        },
        fail: function (response) {
            iziToast.error({
                title: "Error",
                message: "Something Went Wrong!!",
                position: "topRight",
            });
        },
    });
});

$(document).on("click", ".delete-onboard-media", function () {
    var path = $(this).data("path");
    var field = $(this).data("field");
    var img_name = $(this).data("img");
    var table_name = $(this).data("table");
    var t = this;
    var isjson = $(this).data("isjson");
    Swal.fire({
        title: "Are You Sure?",
        text: "You won't be able to revert this!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: "POST",
                    url: appUrl + "admin/settings/removeSettingMedia",
                    data: {
                        path: path,
                        field: field,
                        img_name: img_name,
                        table_name: table_name,
                        isjson: isjson,
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },
                    dataType: "json",
                    success: function (result) {
                        token = $('meta[name="csrf-token"]').attr("content");
                        if (result[0]["is_deleted"] == true) {
                            $(t).closest("div").remove();
                            Swal.fire("Success", "Media Deleted !", "success");
                        } else {
                            Swal.fire(
                                "Oops...",
                                "Something went wrong with ajax !",
                                "error"
                            );
                        }
                    },
                });
            });
        },
        allowOutsideClick: false,
    }).then((result) => {
        if (result.dismiss === Swal.DismissReason.cancel) {
            Swal.fire("Cancelled!", "Your data is  safe.", "error");
        }
    });
});



// feature section order

document.addEventListener("DOMContentLoaded", function () {
    var rowSize = 100; // => container height / number of items
    var container = document.querySelector(".section-order-container");
    var listItems = Array.from(document.querySelectorAll(".section-list-item")); // Array of elements
    var sortables = listItems.map(Sortable); // Array of sortables

    if (listItems.length <= 1) {
        $("#save_section_order").addClass("d-none");
    } else {
        $("#save_section_order").removeClass("d-none"); // Optional: show the button if there are more than 1 items
    }

    var total = sortables.length;

    TweenLite.to(container, 0.5, {
        autoAlpha: 1,
    });

    function changeIndex(item, to) {
        // Change position in array
        arrayMove(sortables, item.index, to);

        // Change element's position in DOM. Not always necessary. Just showing how.
        if (to === total - 1) {
            container.appendChild(item.element);
        } else {
            var i = item.index > to ? to : to + 1;
            container.insertBefore(item.element, container.children[i]);
        }

        // Set index for each sortable
        sortables.forEach((sortable, index) => sortable.setIndex(index));
    }

    function Sortable(element, index) {
        var content = element.querySelector(".section-item-content");
        var order = element.querySelector(".section-order");

        var animation = TweenLite.to(content, 0.3, {
            boxShadow: "rgba(0,0,0,0.2) 0px 16px 32px 0px",
            force3D: true,
            scale: 1.1,
            paused: true,
        });

        var dragger = new Draggable(element, {
            onDragStart: downAction,
            onRelease: upAction,
            onDrag: dragAction,
            cursor: "inherit",
            type: "y",
        });

        // Public properties and methods
        var sortable = {
            dragger: dragger,
            element: element,
            index: index,
            setIndex: setIndex,
        };

        TweenLite.set(element, {
            y: index * rowSize,
        });

        function setIndex(index) {
            sortable.index = index;
            order.textContent = index + 1;

            // Don't layout if you're dragging
            if (!dragger.isDragging) layout();
        }

        function downAction() {
            animation.play();
            this.update();
        }

        function dragAction() {
            // Calculate the current index based on element's position
            var index = clamp(Math.round(this.y / rowSize), 0, total - 1);

            if (index !== sortable.index) {
                changeIndex(sortable, index);
            }
        }

        function upAction() {
            animation.reverse();
            layout();
        }

        function layout() {
            TweenLite.to(element, 0.3, {
                y: sortable.index * rowSize,
            });
        }

        return sortable;
    }

    // Changes an elements's position in array
    function arrayMove(array, from, to) {
        array.splice(to, 0, array.splice(from, 1)[0]);
    }

    // Clamps a value to a min/max
    function clamp(value, a, b) {
        return value < a ? a : value > b ? b : value;
    }

    $(document).on("click", "#save_section_order", function () {
        var ids = sortables.map(function (obj) {
            // Extract the id from the element property
            return obj["element"]["id"];
        });

        $.ajax({
            data: { section_id: ids },
            type: "GET",
            url: appUrl + "admin/feature_section/update_section_order",
            dataType: "json",
            success: function (response) {
                if (response.error == false) {
                    iziToast.success({
                        message: response.message,
                    });
                } else {
                    iziToast.error({
                        message: response.message,
                    });
                }
            },
        });
    });
});

// boostrap table loading icon

function loadingTemplate() {
    return '<i class="bx bx-loader-alt bx-spin bx-rotate-180" ></i>';
}

function validateNumberInput(input) {
    // Remove any non-numeric characters from the input value
    input.value = input.value.replace(/\D/g, "");
}

// code for search meny ib side bar

$(function () {
    $(".menuSearch").on("input", function () {
        let searchValue = $(this).val().toLowerCase();
        $(".navbar-nav li").each(function () {
            let $currentItem = $(this);
            let text = $currentItem.text().toLowerCase();
            if (
                text.includes(searchValue) ||
                $currentItem.find("*").filter(function () {
                    return $(this).text().toLowerCase().includes(searchValue);
                }).length > 0
            ) {
                $currentItem.show();
                $currentItem.parents(".sidebar-title").show();
            } else {
                $currentItem.hide();
            }
        });
    });
});

$(function () {
    function checkContentOverflow() {
        var windowHeight = $(window).height();
        var footerHeight = $(".main-footer").outerHeight();
        var headerHeight = $(".header").outerHeight();
        var contentHeight =
            $("#page-content").outerHeight() - headerHeight - footerHeight;

        if (contentHeight > windowHeight) {
            $(".main-footer").css({
                bottom: "0",
                left: "0",
                right: "0",
                padding: "20px",
                color: "#919BAE",
                display: "flex",
                "background-color": "#ffffff",
                "flex-wrap": "nowrap",
                "justify-content": "flex-start",

                "-webkit-box-shadow": "0 2px 3px rgba(0, 0, 0, .04)",
                "box-shadow": "0 2px 3px rgba(0, 0, 0, .04)",
            });
        } else {
            $(".main-footer").css({
                bottom: "0",

                left: "250px",
                right: "0",
                padding: "20px",
                color: "#919BAE",
                display: "flex",
                "background-color": "#ffffff",
                "flex-wrap": "nowrap",
                "justify-content": "flex-start",
                "-webkit-box-shadow": "0 2px 3px rgba(0, 0, 0, .04)",
                "box-shadow": "0 2px 3px rgba(0, 0, 0, .04)",
            });
        }
    }

    // Call the function when the document is ready and when the window is resized
    $(window).on("load resize", function () {
        checkContentOverflow();
    });
});

// authentication setting change event

$("input[type=radio][name=authentication_method]").change(function () {
    var firebase_radio_button = $(
        'input[type=radio][id="firebase_radio_button"]:checked'
    ).val();
    var sms_radio_button = $(
        'input[type=radio][id="sms_radio_button"]:checked'
    ).val();
    if (firebase_radio_button == "firebase") {
        $(".firebase_config").removeClass("d-none");
        $(".sms_gateway").addClass("d-none");
    } else if (sms_radio_button == "sms") {
        $(".sms_gateway").removeClass("d-none");
        $(".firebase_config").addClass("d-none");
    }
});
// sms gateway js

var sms_data = $("#sms_gateway_data").val() ? $("#sms_gateway_data").val() : [];

if (sms_data.length != 0) {
    var sms_data = JSON.parse(sms_data);
}

$(document).on("click", "#add_sms_header", function (e) {
    e.preventDefault();
    load_sms_header_section(cat_html, false);
});

// function load_sms_header_section(
//     cat_html,
//     is_edit = false,
//     key_headers = [],
//     value_headers = []
// ) {
//     var key_headers = sms_data.header_key;
//     var value_headers = sms_data.header_value;
//     if (is_edit == true) {
//         var html = "";

//         if (Array.isArray(key_headers)) {
//             for (var i = 0; i < key_headers.length; i++) {
//                 html += '<div class="form-group row">';
//                 html += '<div class="col-sm-5 mt-4">';
//                 html +=
//                     '<label for="header_key" class="form-label"> Key </label>';
//                 html +=
//                     '<input type="text" class="form-control" placeholder="Enter Key" name="header_key[]" value="' +
//                     key_headers[i] +
//                     '" id="header_key">';
//                 html += "</div>";
//                 html += '<div class="col-sm-5 mt-4">';
//                 html +=
//                     '<label for="header_value" class="form-label"> Value </label>';
//                 html +=
//                     '<input type="text" class="form-control" placeholder="Enter Value" name="header_value[]" value="' +
//                     value_headers[i] +
//                     '" id="header_value">';
//                 html += "</div>";
//                 html += '<div class="col-sm-2 mt-8">';
//                 html +=
//                     '<button type="button" class="btn btn-tool remove_keyvalue_section"> <i class="text-danger bx bx-trash fa-2x"></i> </button>';
//                 html += "</div>";
//                 html += "</div>";
//             }
//         }
//     } else {
//         var html =
//             '<div class="form-group row">' +
//             '<div class="col-sm-5 mt-4">' +
//             '<label for="header_key" class="form-label"> Key </label>' +
//             '<input type="text" class="form-control"  placeholder="Enter Key" name="header_key[]"  value="" id="header_key">' +
//             "</div>" +
//             '<div class="col-sm-5 mt-4">' +
//             '<label for="header_value" class="form-label"> Value </label>' +
//             '<input type="text" class="form-control"  placeholder="Enter value" name="header_value[]" id="header_value"  value="">' +
//             "</div>" +
//             '<div class="col-sm-2 mt-8"> ' +
//             '<button type="button" class="btn btn-tool remove_keyvalue_header_section" > <i class="text-danger bx bx-trash fa-2x"></i> </button>' +
//             "</div>" +
//             "</div>" +
//             "</div>";
//     }
//     $("#formdata_header_section").append(html);
// }

function load_sms_header_section(
    cat_html,
    is_edit = false,
    key_headers = [],
    value_headers = []
) {
    // Ensure sms_data is defined and has header_key and header_value properties
    var key_headers =
        sms_data && Array.isArray(sms_data.header_key)
            ? sms_data.header_key
            : [];
    var value_headers =
        sms_data && Array.isArray(sms_data.header_value)
            ? sms_data.header_value
            : [];

    var html = "";

    if (is_edit === true) {
        if (Array.isArray(key_headers)) {
            for (var i = 0; i < key_headers.length; i++) {
                html += '<div class="form-group row">';
                html += '<div class="col-sm-5 mt-4">';
                html +=
                    '<label for="header_key" class="form-label"> Key </label>';
                html +=
                    '<input type="text" class="form-control" placeholder="Enter Key" name="header_key[]" value="' +
                    key_headers[i] +
                    '" id="header_key">';
                html += "</div>";
                html += '<div class="col-sm-5 mt-4">';
                html +=
                    '<label for="header_value" class="form-label"> Value </label>';
                html +=
                    '<input type="text" class="form-control" placeholder="Enter Value" name="header_value[]" value="' +
                    value_headers[i] +
                    '" id="header_value">';
                html += "</div>";
                html += '<div class="col-sm-2 mt-8">';
                html +=
                    '<button type="button" class="btn btn-tool remove_keyvalue_section"> <i class="text-danger bx bx-trash fa-2x"></i> </button>';
                html += "</div>";
                html += "</div>";
            }
        }
    } else {
        html =
            '<div class="form-group row">' +
            '<div class="col-sm-5 mt-4">' +
            '<label for="header_key" class="form-label"> Key </label>' +
            '<input type="text" class="form-control"  placeholder="Enter Key" name="header_key[]"  value="" id="header_key">' +
            "</div>" +
            '<div class="col-sm-5 mt-4">' +
            '<label for="header_value" class="form-label"> Value </label>' +
            '<input type="text" class="form-control"  placeholder="Enter value" name="header_value[]" id="header_value"  value="">' +
            "</div>" +
            '<div class="col-sm-2 mt-8"> ' +
            '<button type="button" class="btn btn-tool remove_keyvalue_header_section" > <i class="text-danger bx bx-trash fa-2x"></i> </button>' +
            "</div>" +
            "</div>" +
            "</div>";
    }

    // Append generated HTML to the DOM
    $("#formdata_header_section").append(html);
}

// paramas data
$(document).on("click", "#add_sms_params", function (e) {
    e.preventDefault();
    load_sms_params_section(cat_html, false);
});

function load_sms_params_section(
    cat_html,
    is_edit = false,
    key_params = [],
    value_params = []
) {
    var key_params = sms_data.params_key;
    var value_params = sms_data.params_value;
    var key = $().val();
    if (is_edit == true) {
        var html = "";

        if (Array.isArray(key_params)) {
            for (var i = 0; i < key_params.length; i++) {
                html += '<div class="form-group row">';
                html += '<div class="col-sm-5">';
                html +=
                    '<label for="params_key" class="form-label"> Key </label>';
                html +=
                    '<input type="text" class="form-control" placeholder="Enter Key" name="params_key[]" value="' +
                    key_params[i] +
                    '" id="params_key">';
                html += "</div>";
                html += '<div class="col-sm-5">';
                html +=
                    '<label for="params_value" class="form-label"> Value </label>';
                html +=
                    '<input type="text" class="form-control" placeholder="Enter Value" name="params_value[]" value="' +
                    value_params[i] +
                    '" id="params_value">';
                html += "</div>";
                html += '<div class="col-sm-2 mt-5">';
                html +=
                    '<button type="button" class="btn btn-tool remove_keyvalue_section"> <i class="text-danger bx bx-trash fa-2x"></i> </button>';
                html += "</div>";
                html += "</div>";
            }
        }
    } else {
        var html =
            '<div class="form-group row">' +
            '<div class="col-sm-5">' +
            '<label for="params_key" class="form-label"> Key </label>' +
            '<input type="text" class="form-control"  placeholder="Enter Key" name="params_key[]"  value="" id="params_key">' +
            "</div>" +
            '<div class="col-sm-5">' +
            '<label for="params_value" class="form-label"> Value </label>' +
            '<input type="text" class="form-control"  placeholder="Enter value" name="params_value[]" id="params_value"  value="">' +
            "</div>" +
            '<div class="col-sm-2 mt-5"> ' +
            '<button type="button" class="btn btn-tool remove_keyvalue_paramas_section" > <i class="text-danger bx bx-trash fa-2x"></i> </button>' +
            "</div>" +
            "</div>" +
            "</div>";
    }
    $("#formdata_params_section").append(html);
}

$(function () {
    $(document).on("click", "#product-body-tab", function (event) {
        event.preventDefault();
        $("#product-text").addClass("show");
        $("#product-text").addClass("active");
        $("#product-formdata").addClass("show");
    });
    $(document).on("click", "#product-header-tab", function (event) {
        event.preventDefault();
        if ($("#product-formdata").hasClass("show")) {
            $("#product-formdata").removeClass("active");
            $("#product-formdata").removeClass("show");
        }
        if ($("#product-text").hasClass("show")) {
            $("#product-text").removeClass("active");
            $("#product-text").removeClass("show");
        }
    });
    $(document).on("click", "#product-params-tab", function (event) {
        event.preventDefault();
        if ($("#product-formdata").hasClass("show")) {
            $("#product-formdata").removeClass("active");
            $("#product-formdata").removeClass("show");
        }
        if ($("#product-text").hasClass("show")) {
            $("#product-text").removeClass("active");
            $("#product-text").removeClass("show");
        }
    });
});

function createHeader() {
    const username = document.getElementById("converterInputAccountSID").value;
    const password = document.getElementById("converterInputAuthToken").value;

    if (username && password) {
        const stringToEncode = `${username}:${password}`;
        document.getElementById(
            "basicToken"
        ).innerText = `Authorization: Basic ${btoa(stringToEncode)}`;
    }
}
$(document).on("click", ".remove_keyvalue_header_section", function () {
    $(this).closest(".row").remove();
});
$(document).on("click", ".remove_keyvalue_paramas_section", function () {
    $(this).closest(".row").remove();
});

$(function () {
    load_sms_header_section(
        cat_html,
        true,
        sms_data.header_key,
        sms_data.header_value
    );
    load_sms_body_section(
        cat_html,
        true,
        sms_data.body_key,
        sms_data.body_value
    );
    load_sms_params_section(
        cat_html,
        true,
        sms_data.params_key,
        sms_data.params_value
    );
});

// sms gateway form submission

$(function () {
    $(document).on("click", "#sms_gateway_submit", function (event) {
        event.preventDefault();
        const form = $("#smsgateway_setting_form");
        var formData = $(form).serialize();
        $.ajax({
            type: "POST",
            url: "/admin/settings/store_sms_data",
            data: formData,
            success: function (response) {
                if (response.error == false) {
                    iziToast.success({
                        message: response.message,
                    });
                } else {
                    iziToast.error({
                        message: response.message,
                    });
                }
            },
        });

        return;
    });
});

$(document).on("click", "#add_sms_body", function (e) {
    e.preventDefault();
    load_sms_body_section(cat_html, false);
});
function load_sms_body_section(
    cat_html,
    is_edit = false,
    body_keys = [],
    body_values = []
) {
    var body_keys = sms_data.body_key;
    var body_values = sms_data.body_value;

    if (is_edit == true) {
        var html = ""; // Initialize the HTML

        if (Array.isArray(body_keys)) {
            for (var i = 0; i < body_keys.length; i++) {
                html += '<div class="form-group row key-value-pair">';
                html += '<div class="col-sm-5">';
                html +=
                    '<label for="body_key" class="form-label"> Key </label>';
                html +=
                    '<input type="text" class="form-control" placeholder="Enter Key" name="body_key[]" value="' +
                    body_keys[i] +
                    '" id="body_key">';
                html += "</div>";
                html += '<div class="col-sm-5">';
                html +=
                    '<label for="body_value" class="form-label"> Value </label>';
                html +=
                    '<input type="text" class="form-control" placeholder="Enter Value" name="body_value[]" value="' +
                    body_values[i] +
                    '" id="body_value">';
                html += "</div>";
                html += '<div class="col-sm-2 mt-5 ">';
                html +=
                    '<button type="button" class="btn btn-tool remove_keyvalue_section"> <i class="text-danger  bx bx-trash fa-2x "></i> </button>';
                html += "</div>";
                html += "</div>";
            }
        }
    } else {
        var html =
            '<div class="form-group row key-value-pair">' +
            '<div class="col-sm-5">' +
            '<label for="body_key" class="form-label"> Key </label>' +
            '<input type="text" class="form-control"  placeholder="Enter Key" name="body_key[]"  value="" id="body_key">' +
            "</div>" +
            '<div class="col-sm-5">' +
            '<label for="body_value" class="form-label"> Value </label>' +
            '<input type="text" class="form-control"  placeholder="Enter Key" name="body_value[]"  value="" id="body_value">' +
            "</div>" +
            '<div class="col-sm-2 mt-5"> ' +
            '<button type="button" class="btn btn-tool remove_keyvalue_section" > <i class="text-danger  bx bx-trash fa-2x "></i> </button>' +
            "</div>" +
            "</div>" +
            "</div>";
    }
    var test = $("#formdata_section").append(html);
}

$(document).on("click", ".remove_keyvalue_section", function () {
    $(this).closest(".row").remove();
});

// hide and show seller password

$(function () {
    // Function to toggle password visibility
    $(".toggle_password").click(function () {
        var input = $(".show_seller_password");
        var icon = $(this).find("i");
        var type = input.attr("type") === "password" ? "text" : "password";
        input.attr("type", type);
        icon.toggleClass("bx-show bx-low-vision");
    });

    // Function to toggle confirm password visibility
    $(".toggle_confirm_password").click(function () {
        var input = $('input[name="confirm_password"]');
        var icon = $(this).find("i");
        var type = input.attr("type") === "password" ? "text" : "password";
        input.attr("type", type);
        icon.toggleClass("bx-show bx-low-vision");
    });
});

// reset select2 selected option
$(function () {
    $(document).on("click", ".offer_slider_reset_button", function () {
        $(".offer_slider_title").val("");
        $(".offer_sliders_offer").val("").trigger("change");
    });
});

$(function () {
    $(".toggle_profile_password").click(function () {
        $(this).toggleClass("show");
        var input = $(this).prev(".show_profile_password");
        if (input.attr("type") === "password") {
            input.attr("type", "text");
        } else {
            input.attr("type", "password");
        }
    });
});

$(function () {
    $(document).on("click", ".view_more_btn", function (e) {
        e.preventDefault();

        $(".remaining-stores").removeClass("d-none");
        $("#store-dropdown").addClass("show");
        return false;
    });
});
$(".toggle-seller-profile-password").click(function () {
    $(this).toggleClass("show-password");

    var input = $(this).siblings("input");
    if (input.attr("type") === "password") {
        input.attr("type", "text");
        $(this).find("i").removeClass("bx-hide").addClass("bx-show");
    } else {
        input.attr("type", "password");
        $(this).find("i").removeClass("bx-show").addClass("bx-hide");
    }
});

$(function () {
    var $delete_button = $(".delete_selected_data");
    var table_id = $delete_button.data("table-id");

    // Initially hide the delete button
    $delete_button.hide();

    // Function to toggle the delete button
    function toggle_delete_button() {
        var selected_ids = $("#" + table_id)
            .bootstrapTable("getSelections")
            .map(function (row) {
                return row.id;
            });

        // Show or hide the delete button based on selection
        if (selected_ids.length > 0) {
            $delete_button.show();
        } else {
            $delete_button.hide();
        }
    }

    // Bind event to selection change
    $("#" + table_id).on(
        "check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table",
        toggle_delete_button
    );

    $(".delete_selected_data").on("click", function () {
        var delete_url = $(this).data("delete-url");

        var selected_ids = $("#" + table_id)
            .bootstrapTable("getSelections")
            .map(function (row) {
                return row.id;
            });

        if (selected_ids.length === 0) {
            iziToast.error({
                message: "Please select at least one data to delete.",
            });
            return;
        }

        if (confirm("Are you sure you want to delete the selected data?")) {
            $.ajax({
                url: delete_url,
                type: "DELETE",
                data: {
                    ids: selected_ids,
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
                success: function (response) {
                    // console.log(response);

                    // Check if the response contains an error flag
                    if (response.error == true) {
                        // console.log("here");
                        // Manually trigger the error handler
                        iziToast.error({
                            title: "Error",
                            message:
                                response.error_message || "An error occurred.",
                            position: "topRight",
                        });
                    } else {
                        iziToast.success({
                            message:
                                response.message ||
                                "Data deleted successfully.",
                        });

                        $("#" + table_id).bootstrapTable("refresh", {
                            silent: true,
                        });

                        // Clear the selection
                        $("#" + table_id).bootstrapTable("uncheckAll");

                        // Hide the delete button after successful deletion
                        toggle_delete_button();
                    }
                },
                error: function (xhr) {
                    iziToast.error({
                        message:
                            xhr.responseJSON.error ||
                            "Error occurred while deleting data.",
                    });
                    $("#" + table_id).bootstrapTable("refresh", {
                        silent: true,
                    });
                },
            });
        }
    });
});

$(function () {
    var $update_button = $(".bulk_update_deliverability_data");
    var table_id = $update_button.data("table-id");

    // Initially hide the update button
    $update_button.hide();

    // Function to toggle the update button
    function toggle_update_button() {
        var selected_ids = $("#" + table_id)
            .bootstrapTable("getSelections")
            .map(function (row) {
                return row.id;
            });

        // Show or hide the update button based on selection
        if (selected_ids.length > 0) {
            $update_button.show();
        } else {
            $update_button.hide();
        }
    }

    // Bind event to selection change
    $("#" + table_id).on(
        "check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table",
        toggle_update_button
    );

    // Open Modal and Pass IDs
    $(".bulk_update_deliverability_data").on("click", function () {
        var selected_ids = $("#" + table_id)
            .bootstrapTable("getSelections")
            .map(function (row) {
                return row.id;
            });

        if (selected_ids.length === 0) {
            iziToast.error({
                message: "Please select at least one product.",
            });
            return;
        }

        // Pass IDs to hidden input field in modal
        $("#product_id").val(selected_ids.join(","));

        // Open the modal
        $("#deliverabilityModal").modal("show");
    });

    $("#deliverabilityForm").on("submit", function (e) {
        e.preventDefault();

        var formData = {
            product_id: $("#product_id").val(),
            deliverable_type: $("#deliverable_type").val(),
            deliverable_zones: $("#deliverable_zones").val(),
            _token: $('meta[name="csrf-token"]').attr("content"),
        };

        $.ajax({
            url: "{{ route('seller.deliverability.bulk.update') }}",
            type: "POST",
            data: formData,
            success: function (response) {
                iziToast.success({
                    message: response.message,
                });

                $("#" + table_id).bootstrapTable("refresh", { silent: true });

                $("#deliverabilityModal").modal("hide");

                $("#" + table_id).bootstrapTable("uncheckAll");
                toggle_update_button();
            },
            error: function (xhr) {
                iziToast.error({
                    message:
                        xhr.responseJSON.error ||
                        "Error occurred while updating deliverability.",
                });
            },
        });
    });
});
$(function () {
    var $update_button = $(".bulk_update_combo_deliverability_data");
    var table_id = $update_button.data("table-id");

    // Initially hide the update button
    $update_button.hide();

    // Function to toggle the update button
    function toggle_update_button() {
        var selected_ids = $("#" + table_id)
            .bootstrapTable("getSelections")
            .map(function (row) {
                return row.id;
            });

        // Show or hide the update button based on selection
        if (selected_ids.length > 0) {
            $update_button.show();
        } else {
            $update_button.hide();
        }
    }

    // Bind event to selection change
    $("#" + table_id).on(
        "check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table",
        toggle_update_button
    );

    // Open Modal and Pass IDs
    $(".bulk_update_combo_deliverability_data").on("click", function () {
        var selected_ids = $("#" + table_id)
            .bootstrapTable("getSelections")
            .map(function (row) {
                return row.id;
            });

        if (selected_ids.length === 0) {
            iziToast.error({
                message: "Please select at least one product.",
            });
            return;
        }

        // Pass IDs to hidden input field in modal
        $("#product_id").val(selected_ids.join(","));

        // Open the modal
        $("#deliverabilityModal").modal("show");
    });

    $("#combodeliverabilityForm").on("submit", function (e) {
        e.preventDefault();

        var formData = {
            product_id: $("#product_id").val(),
            deliverable_type: $("#deliverable_type").val(),
            deliverable_zones: $("#deliverable_zones").val(),
            _token: $('meta[name="csrf-token"]').attr("content"),
        };

        $.ajax({
            url: "{{ route('seller.combo.deliverability.bulk.update') }}",
            type: "POST",
            data: formData,
            success: function (response) {
                iziToast.success({
                    message: response.message,
                });

                $("#" + table_id).bootstrapTable("refresh", { silent: true });

                $("#deliverabilityModal").modal("hide");

                $("#" + table_id).bootstrapTable("uncheckAll");
                toggle_update_button();
            },
            error: function (xhr) {
                iziToast.error({
                    message:
                        xhr.responseJSON.error ||
                        "Error occurred while updating deliverability.",
                });
            },
        });
    });
});

$(function () {
    $(".repeater").repeater({
        // (Optional)
        // start with an empty list of repeaters. Set your first (and only)
        // "data-repeater-item" with style="display:none;" and pass the
        // following configuration flag
        initEmpty: false,
        // (Optional)
        // "defaultValues" sets the values of added items.  The keys of
        // defaultValues refer to the value of the input's name attribute.
        // If a default value is not specified for an input, then it will
        // have its value cleared.
        defaultValues: {
            "text-input": "",
        },
        // (Optional)
        // "show" is called just after an item is added.  The item is hidden
        // at this point.  If a show callback is not given the item will
        // have $(this).show() called on it.
        show: function () {
            $(this).slideDown();
        },
        // (Optional)
        // "hide" is called when a user clicks on a data-repeater-delete
        // element.  The item is still visible.  "hide" is passed a function
        // as its first argument which will properly remove the item.
        // "hide" allows for a confirmation step, to send a delete request
        // to the server, etc.  If a hide callback is not given the item
        // will be deleted.
        hide: function (deleteElement) {
            if (confirm("Are you sure you want to delete this element?")) {
                $(this).slideUp(deleteElement);
            }
        },
        // (Optional)
        // You can use this if you need to manually re-index the list
        // for example if you are using a drag and drop library to reorder
        // list items.

        // (Optional)
        // Removes the delete button from the first list item,
        // defaults to false.
        isFirstItemUndeletable: true,
    });
});

$(function () {
    // Function to initialize select2

    function initializeSelect2() {
        $(".zipcode_list").select2({
            ajax: {
                url: appUrl + from + "/area/get_zipcodes",
                type: "GET",
                dataType: "json",
                delay: 250,
                data: function (params) {
                    return {
                        search: params.term,
                    };
                },
                processResults: function (response) {
                    return {
                        results: response.map(function (item) {
                            return {
                                id: item.id,
                                text: item.text || item.zipcode,
                            };
                        }),
                    };
                },
                cache: true,
            },
            placeholder: "Search for zipcodes",
        });

        // Ensure pre-selected zipcode is displayed correctly
        let selectedZipcode = $(".zipcode_list").find("option:selected").val();
        if (selectedZipcode) {
            let selectedText = $(".zipcode_list")
                .find("option:selected")
                .text();

            let newOption = new Option(
                selectedText,
                selectedZipcode,
                true,
                true
            );
            $(".zipcode_list").append(newOption).trigger("change");
        }
    }

    initializeSelect2();
});

// Initialize zone zipcode Select2

$(function () {
    function initializeZoneZipcodeSelect2(element) {
        element.select2({
            ajax: {
                url: appUrl + from + "/area/get_zipcodes",
                type: "GET",
                dataType: "json",
                delay: 250,
                data: function (params) {
                    return {
                        search: params.term,
                    };
                },
                processResults: function (response) {
                    return {
                        results: response,
                    };
                },
                cache: true,
            },
            placeholder: "Search for zipcodes",
            allowClear: true,
        });
    }

    // Initialize all existing `.zone_zipcode_list` selects
    $(".zone_zipcode_list").each(function () {
        initializeZoneZipcodeSelect2($(this));
    });

    // Handle dynamically added elements
    $(".repeater").on("click", "[data-repeater-create]", function () {
        setTimeout(function () {
            $(".repeater .zone_zipcode_list")
                .last()
                .each(function () {
                    initializeZoneZipcodeSelect2($(this));
                });
        }, 100);
    });
});

// Initialize city  Select2

$(function () {
    function initializeZoneCitySelect2(element) {
        element.select2({
            ajax: {
                url: appUrl + from + "/area/get_cities",
                type: "GET",
                dataType: "json",
                delay: 250,
                data: function (params) {
                    return {
                        search: params.term,
                    };
                },
                processResults: function (response) {
                    return {
                        results: response,
                    };
                },
                cache: true,
            },
            placeholder: "Search for cities",
            allowClear: true,
        });
    }

    // Initialize all existing `.zone_city_list` selects
    $(".zone_city_list").each(function () {
        initializeZoneCitySelect2($(this));
    });

    // Handle dynamically added elements
    $(".repeater").on("click", "[data-repeater-create]", function () {
        setTimeout(function () {
            $(".repeater .zone_city_list")
                .last()
                .each(function () {
                    initializeZoneCitySelect2($(this));
                });
        }, 100);
    });
});

$(function () {
    // Function to initialize select2 for .city_list elements
    function initializeCitySelect2() {
        $(".city_list").select2({
            ajax: {
                url: appUrl + from + "/area/get_cities",
                type: "GET",
                dataType: "json",
                delay: 250,
                data: function (params) {
                    return {
                        search: params.term,
                    };
                },
                processResults: function (response) {
                    // return {
                    //     results: response,
                    // };
                    return {
                        results: response.map(function (item) {
                            return {
                                id: item.id,
                                text: item.text || item.name,
                            };
                        }),
                    };
                },
                cache: true,
            },
            dropdownParent: $(".city_list_parent").last(),
            placeholder: "Search for cities",
        });
        // Ensure pre-selected zipcode is displayed correctly
        let selectedZipcode = $(".city_list").find("option:selected").val();
        if (selectedZipcode) {
            let selectedText = $(".city_list").find("option:selected").text();

            let newOption = new Option(
                selectedText,
                selectedZipcode,
                true,
                true
            );
            $(".city_list").append(newOption).trigger("change");
        }
    }

    initializeCitySelect2();

    $(".repeater").on("click", "[data-repeater-create]", function () {
        setTimeout(function () {
            initializeCitySelect2();
        }, 100);
    });
});

// create parcel modal

function parcelModal(seller_id = null) {
    if (from == "admin") {
    }

    let productVariantIds = [];
    let productName = [];
    let orderItemId = [];
    $(".product_variant_id").each(function () {
        productVariantIds.push($(this).val());
    });
    $(".product_name").each(function () {
        productName.push($(this).val());
    });
    $(".order_item_id").each(function () {
        orderItemId.push($(this).val());
    });

    var modalBody = document.getElementById("product_details");
    modalBody.innerHTML = "";
    for (var i = 0; i < productVariantIds.length; i++) {
        const data = JSON.parse(
            $("#product_variant_id_" + productVariantIds[i]).html()
        );

        const quantity = parseInt(data.quantity);
        const unit_price = parseInt(data.unit_price);
        const delivered_quantity = parseInt(data.delivered_quantity);
        if (
            delivered_quantity != quantity &&
            data.active_status != "cancelled" &&
            data.active_status != "delivered"
        ) {
            $("#empty_box_body").addClass("d-none");
            $("#modal-body").removeClass("d-none");
            let row =
                "<tr>" +
                "<th scope='row'>" +
                (i + 1) +
                "</th>" +
                "<td>" +
                productName[i] +
                "</td>" +
                "<td>" +
                productVariantIds[i] +
                "</td>" +
                "<td>" +
                quantity +
                "</td>" +
                "<td>" +
                unit_price +
                "</td>" +
                `<td><label for="checkbox-${productVariantIds[i]}"><input type="checkbox" data-item-id="${orderItemId[i]}" name="checkbox-${productVariantIds[i]}" id="checkbox-${productVariantIds[i]}" class="form-check-input product-to-ship"></label></td>`;
            ("</tr>");

            modalBody.innerHTML += row;
        }
    }
    if (modalBody.innerHTML == "") {
        $("#empty_box_body").removeClass("d-none");
        $("#modal-body").addClass("d-none");

        let empty_box_body = document.getElementById("empty_box_body");
        empty_box_body.innerHTML = "";
        let row = "<h5 class='text-center'>Items Are Already Shipped.</h5>";
        empty_box_body.innerHTML += row;
    }
}

// ship parcel

$(document).on("click", "#ship_parcel_btn", function (e) {
    e.preventDefault();
    let product_to_ship = $(".product-to-ship:checked");
    let parcel_title = $("#parcel_title").val();
    let order_id = $("#order_id").val();
    let parcel_order_type = $("#parcel_order_type").val();

    let selected_items = [];
    product_to_ship.each(function () {
        selected_items.push($(this).data("item-id"));
    });
    $.ajax({
        type: "POST",
        url: appUrl + from + "/orders/create_parcel",
        data: {
            parcel_title,
            selected_items,
            order_id,
            parcel_order_type,
            _token: $('meta[name="csrf-token"]').attr("content"),
        },
        beforeSend: function () {
            $("#ship_parcel_btn").html("Please wait");
            $("#ship_parcel_btn").attr("disabled", true);
        },
        success: function (response) {
            if (response.error == false) {
                iziToast.success({
                    message: response.message,
                });
                response.data.map((val) => {
                    $("#product_variant_id_" + val.product_variant_id).html(
                        JSON.stringify(val)
                    );
                });
                $("#parcel_table").bootstrapTable("refresh");
                $("#seller_parcel_table").bootstrapTable("refresh");
                $("#create_parcel_modal").modal("hide");
            } else {
                iziToast.error({
                    message: response.message,
                });
            }
        },
        error: function (xhr) {
            var errors = xhr.responseJSON.errors;
            for (var key in errors) {
                if (errors.hasOwnProperty(key)) {
                    var errorMessages = errors[key];
                    iziToast.error({
                        title: "Error",
                        message: errorMessages,
                        position: "topRight",
                    });
                }
            }
            $("#ship_parcel_btn").html("Ship").attr("disabled", false);
        },
    });
});
// view parcel items
$(document).on("show.bs.modal", "#view_parcel_items_modal", function (event) {
    let triggerElement = $(event.relatedTarget);
    current_selected_image = triggerElement;
    let parcel_items = $(current_selected_image).data("items");
    let modalBody = document.getElementById("parcel_product_details");
    modalBody.innerHTML = "";
    let count = 1;
    parcel_items.forEach((item) => {
        let status = "";
        if (item.item_status === "awaiting") {
            status =
                '<label class="badge bg-secondary">' +
                capitalizeFirstLetter(item.item_status) +
                "</label>";
        } else if (item.item_status === "received") {
            status =
                '<label class="badge bg-primary">' +
                capitalizeFirstLetter(item.item_status) +
                "</label>";
        } else if (item.item_status === "processed") {
            status =
                '<label class="badge bg-info">' +
                capitalizeFirstLetter(item.item_status) +
                "</label>";
        } else if (item.item_status === "shipped") {
            status =
                '<label class="badge bg-warning">' +
                capitalizeFirstLetter(item.item_status) +
                "</label>";
        } else if (item.item_status === "delivered") {
            status =
                '<label class="badge bg-success">' +
                capitalizeFirstLetter(item.item_status) +
                "</label>";
        } else if (
            item.item_status === "returned" ||
            item.item_status === "cancelled"
        ) {
            status =
                '<label class="badge bg-danger">' +
                capitalizeFirstLetter(item.item_status) +
                "</label>";
        } else if (item.item_status === "return_request_decline") {
            status =
                '<label class="badge bg-danger">' +
                formatStatus(item.item_status) +
                "</label>";
        } else if (item.item_status === "return_request_approved") {
            status =
                '<label class="badge bg-success">' +
                formatStatus(item.item_status) +
                "</label>";
        } else if (item.item_status === "return_request_pending") {
            status =
                '<label class="badge bg-secondary">' +
                formatStatus(item.item_status) +
                "</label>";
        }
        var row =
            "<tr>" +
            "<th scope='row'>" +
            count +
            "</th>" +
            "<td>" +
            item.product_name +
            "</td>" +
            `<td><a href='${item.image}' class="order-image-box">
                <img src='${item.image}' alt="${item.product_name}" class="image-box"></a></td>` +
            "<td>" +
            item.quantity +
            "</td>" +
            "<td>" +
            status +
            "</td>" +
            "</tr>";

        modalBody.innerHTML += row;
        count++;
    });
});

// parcel update status modal

$(document).on("hide.bs.modal", "#parcel_status_modal", function () {
    $("#parcel-items-container").empty();
    $("#tracking_box").empty();
    $("#tracking_box_old").empty();
    $(".shiprocket_order_box").removeClass("d-none");
    $(".manage_shiprocket_box").addClass("d-none");
});
$(document).on("show.bs.modal", "#parcel_status_modal", function (event) {
    let triggerElement = $(event.relatedTarget);
    current_selected_image = triggerElement;

    let parcel_items = $(current_selected_image).data("items");
    let order_tracking = $("#order_tracking").val();
    if (order_tracking != undefined) {
        order_tracking = JSON.parse(order_tracking);
    }

    $("#parcel_data").val(JSON.stringify(parcel_items));
    const container = document.getElementById("parcel-items-container");
    const tracking_box = document.getElementById("tracking_box");
    const tracking_box_old = document.getElementById("tracking_box_old");
    if (order_tracking != undefined) {
        order_tracking.forEach((tracking) => {
            if (tracking.parcel_id == parcel_items[0].parcel_id) {
                if (tracking.is_canceled == 0) {
                    $(".shiprocket_order_box").addClass("d-none");
                    $(".manage_shiprocket_box").removeClass("d-none");
                    let div = document.createElement("div");
                    div.innerHTML = `
                            <div class="accordion  mb-3" id="shiprocketOrderAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingOne">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                            Shiprocket Order Details
                                        </button>
                                    </h2>
                                    <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#shiprocketOrderAccordion">
                                        <div class="accordion-body">
                                            <p><strong>Shiprocket Order ID:</strong> <span class="text-dark">${tracking.shiprocket_order_id}</span></p>
                                            <p><strong>Shiprocket Tracking ID:</strong> <span class="text-dark">${tracking.tracking_id}</span></p>
                                            <p><strong>Shiprocket Tracking URL:</strong> <a href="${tracking.url}" target="_blank" class="text-primary">${tracking.url}</a></p>
                                        </div>
                                        <input type="hidden" name="shiprocket_tracking_id" id="shiprocket_tracking_id" value="${tracking.tracking_id}">
                                        <input type="hidden" name="shiprocket_order_id" id="shiprocket_order_id" value="${tracking.shiprocket_order_id}">
                                    </div>
                                </div>
                            </div>
                        `;

                    tracking_box.appendChild(div);
                } else {
                    let div = document.createElement("div");

                    div.innerHTML = `
                        <hr><h5>Cancelled Shiprocket Order Details</h5>
                        <p class="mb-0 text-bold"><span class="text-black-50">Shiprocket Order Id:</span> ${tracking.shiprocket_order_id}</p>
                        <p class="m-0 text-bold"><span class="text-black-50">Shiprocket Tracking Id:</span> ${tracking.tracking_id}</p>
                        <p class="m-0 text-bold"><span class="text-black-50">Shiprocket Tracking url:</span> <a href="${tracking.url}" target="_blank" class="text-primary">${tracking.url}</a></p><hr>
                        `;
                    tracking_box_old.appendChild(div);
                }
            }
        });
    }
    const card = document.createElement("div");
    card.className = "card p-3 border";
    let count = 1;
    card.innerHTML = `
    <table class="table">
        <thead>
            <tr>
                <th scope="col">#</th>
                <th scope="col">Name</th>
                <th scope="col">Image</th>
                <th scope="col">Quantity</th>
                <th scope="col">Status</th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>
`;
    const tbody = card.querySelector("tbody");

    parcel_items.forEach((element) => {
        let status = "";
        if (element.item_status === "awaiting") {
            status =
                '<label class="badge bg-secondary">' +
                capitalizeFirstLetter(element.item_status) +
                "</label>";
        } else if (element.item_status === "received") {
            status =
                '<label class="badge bg-primary">' +
                capitalizeFirstLetter(element.item_status) +
                "</label>";
        } else if (element.item_status === "processed") {
            status =
                '<label class="badge bg-info">' +
                capitalizeFirstLetter(element.item_status) +
                "</label>";
        } else if (element.item_status === "shipped") {
            status =
                '<label class="badge bg-warning">' +
                capitalizeFirstLetter(element.item_status) +
                "</label>";
        } else if (element.item_status === "delivered") {
            status =
                '<label class="badge bg-success">' +
                capitalizeFirstLetter(element.item_status) +
                "</label>";
        } else if (
            element.item_status === "returned" ||
            element.item_status === "cancelled"
        ) {
            status =
                '<label class="badge bg-danger">' +
                capitalizeFirstLetter(element.item_status) +
                "</label>";
        } else if (element.item_status === "return_request_decline") {
            status =
                '<label class="badge bg-danger">' +
                formatStatus(element.item_status) +
                "</label>";
        } else if (element.item_status === "return_request_approved") {
            status =
                '<label class="badge bg-success">' +
                formatStatus(element.item_status) +
                "</label>";
        } else if (element.item_status === "return_request_pending") {
            status =
                '<label class="badge bg-secondary">' +
                formatStatus(element.item_status) +
                "</label>";
        }

        $("#parcel_id").val(element.parcel_id);
        $("#deliver_by").val(element.delivery_boy_id);
        $(".parcel_status").val(element.active_status);
        tbody.innerHTML += `
        <tr>
            <td>${count++}</td>
            <td>${element.product_name}</td>
            <td><a href='${element.image
            }' class="image-box-100" data-toggle='lightbox' data-gallery='order-images'> <img src='${element.image
            }' alt="${element.product_name}"></a></td>
            <td>${element.quantity}</td>
            <td>${status}</td>
        </tr>
    `;
    });
    container.appendChild(card);
});

// Utility functions
function capitalizeFirstLetter(status) {
    return status.charAt(0).toUpperCase() + status.slice(1);
}

function formatStatus(status) {
    return capitalizeFirstLetter(status.replace(/_/g, " "));
}
//  update parcel order status

$(document).on("click", ".parcel_order_status_update", function (e) {
    let parcel_id = $("#parcel_id").val();
    let status = $(".parcel_status").val();
    if (status == "" || status == null) {
        iziToast.error({
            message: "Please Select Status",
        });
        return false;
    }
    let deliver_by = $("#deliver_by").val();
    Swal.fire({
        title: "Are You Sure?",
        text: "You won't be able to revert this!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, update it!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: "POST",
                    url: appUrl + from + "/orders/update_order_status",
                    data: {
                        parcel_id,
                        status,
                        deliver_by,
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },

                    dataType: "json",
                    success: function (result) {
                        if (result["error"] == false) {
                            $("#parcel_table").bootstrapTable("refresh");
                            $("#seller_parcel_table").bootstrapTable("refresh");
                            iziToast.success({
                                message: result["message"],
                            });
                            result.data.forEach((element) => {
                                $(".status-" + element["order_item_id"])
                                    .addClass("badge-info")
                                    .html(element["status"]);
                            });
                        } else {
                            iziToast.error({
                                message: result["message"],
                            });
                        }
                        swal.close();
                        setTimeout(function () {
                            location.reload();
                        }, 1000);
                    },
                });
            });
        },
        allowOutsideClick: false,
    });
});

// update digital order status
$(document).on("click", ".digital_order_status_update", function (e) {
    let status = $(".digital_order_status").val();
    const order_item_ids = $(".selected_order_item_ids:checked")
        .map(function () {
            return $(this).val();
        })
        .get();
    let order_id = $("#order_id").val();
    if (status == "" || status == null) {
        iziToast.error({
            message: "Please Select Status",
        });
        return false;
    }
    Swal.fire({
        title: "Are You Sure?",
        text: "You won't be able to revert this!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, update it!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: "POST",
                    url: appUrl + from + "/orders/update_order_status",
                    data: {
                        order_id,
                        order_item_ids,
                        status,
                        type: "digital",
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },

                    dataType: "json",
                    success: function (result) {
                        if (result["error"] == false) {
                            iziToast.success({
                                message: result["message"],
                            });
                            setTimeout(function () {
                                location.reload();
                            }, 1000);
                            result.data.forEach((element) => {
                                $(".status-" + element["order_item_id"])
                                    .addClass("badge-info")
                                    .html(element["status"]);
                            });
                        } else {
                            iziToast.error({
                                message: result["message"],
                            });
                        }
                        swal.close();
                    },
                });
            });
        },
        allowOutsideClick: false,
    });
});
// delete parcel

function delete_parcel(id) {
    Swal.fire({
        title: "Are You Sure?",
        text: "You won't be able to revert this!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: "post",
                    url: appUrl + from + "/orders/delete_parcel",
                    data: {
                        id,
                    },
                    dataType: "json",
                    success: function (response) {
                        if (response.error == true) {
                            Swal.fire("error", response.message, "error");
                        } else {
                            response.data.map((val) => {
                                $(
                                    "#product_variant_id_" +
                                    val.product_variant_id
                                ).html(JSON.stringify(val));
                            });
                            iziToast.success({
                                message: response.message,
                            });
                            Swal.fire("Success", "Parcel Deleted !", "success");
                        }
                        $("#parcel_table").bootstrapTable("refresh");
                        $("#seller_parcel_table").bootstrapTable("refresh");
                    },
                    error: function (xhr) {
                        var errors = xhr.responseJSON.errors;
                        for (var key in errors) {
                            if (errors.hasOwnProperty(key)) {
                                var errorMessages = errors[key];
                                iziToast.error({
                                    title: "Error",
                                    message: errorMessages,
                                    position: "topRight",
                                });
                            }
                        }
                        $("#ship_parcel_btn")
                            .html("Ship")
                            .attr("disabled", false);
                    },
                });
            });
        },
        allowOutsideClick: false,
    });
}
$(document).on("click", ".refresh_shiprocket_status", function (e) {
    let tracking_id = $("#shiprocket_tracking_id").val();
    if (tracking_id == undefined || tracking_id == "" || tracking_id == null) {
        iziToast.error({
            message: "Tracking Id is Required",
        });
        return false;
    }
    $.ajax({
        type: "POST",
        url: appUrl + from + "/orders/update_shiprocket_order_status",
        data: { tracking_id },
        dataType: "json",
        success: function (response) {
            if (response.error == false) {
                $("#parcel_table").bootstrapTable("refresh");
                iziToast.success({
                    message: response.message,
                });
                response.data.forEach((element) => {
                    $(".status-" + element["order_item_id"])
                        .addClass("badge-info")
                        .html(element["status"]);
                });
                $("#parcel_status_modal").modal("hide");

                return;
            }
            iziToast.error({
                message: response.message,
            });
            return false;
        },
    });
});
$(document).on("change", ".parcel_status", function (e) {
    let status = $(this).val();
    if (status == "delivered") {
        return $(".otp-field").removeClass("d-none");
    }
    $(".otp-field").addClass("d-none");
});
$(document).on("click", ".update_status_delivery_boy", function (e) {
    let parcel_id = $(this).data("id");
    let otp_system = $(this).data("otp-system");

    let status = $(".parcel_status").val();
    let post_otp = $("#otp").val();

    if (status == "" || status == undefined) {
        return iziToast.error({
            message: "Please Fill Status",
        });
    }
    if (
        otp_system == 1 &&
        status == "delivered" &&
        post_otp == "" &&
        post_otp == undefined
    ) {
        return iziToast.error({
            message: "Please Enter Otp",
        });
    }
    Swal.fire({
        title: "Are You Sure?",
        text: "You won't be able to revert this!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, update it!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: "POST",
                    url:
                        appUrl + "delivery_boy/orders/update_order_item_status",
                    data: {
                        id: parcel_id,
                        status: status,
                        otp: post_otp,
                    },
                    dataType: "json",
                    success: function (result) {
                        if (result["error"] == false) {
                            iziToast.success({
                                message: result["message"],
                            });
                            setTimeout(function () {
                                location.reload();
                            }, 1000);
                        } else {
                            iziToast.error({
                                message: result["message"],
                            });
                        }
                        swal.close();
                    },
                });
            });
        },
        allowOutsideClick: false,
    });
});
$(document).on("click", ".update_return_status_delivery_boy", function (e) {
    let order_item_id = $(this).data("id");

    let status = $(".order_item_status").val();

    Swal.fire({
        title: "Are You Sure?",
        text: "You won't be able to revert this!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, update it!",
        showLoaderOnConfirm: true,
        preConfirm: function () {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: "POST",
                    url:
                        appUrl +
                        "delivery_boy/orders/update_return_order_item_status",
                    data: {
                        order_item_id: order_item_id,
                        status: status,
                        _token: $('meta[name="csrf-token"]').attr("content"),
                    },
                    dataType: "json",
                    success: function (result) {
                        if (result["error"] == false) {
                            iziToast.success({
                                message: result["message"],
                            });
                            setTimeout(function () {
                                location.reload();
                            }, 1000);
                        } else {
                            iziToast.error({
                                message: result["message"],
                            });
                        }
                        swal.close();
                    },
                });
            });
        },
        allowOutsideClick: false,
    });
});
// Open Modal and Fill Data
// $(document).on("click", ".edit-deliverability", function () {
//     let productId = $(this).data("id");
//     let deliverableType = $(this).data("type");
//     let selectedZones = $(this).data("zones"); // Should be an array of objects with {id, name}

//     $("#product_id").val(productId);
//     $("#deliverable_type").val(deliverableType);

//     // Ensure selected zones are properly formatted before setting in Select2
//     if (selectedZones && selectedZones.length > 0) {
//         let zoneOptions = selectedZones.map((zone) => ({
//             id: zone.id,
//             text: zone.name,
//             serviceable_cities: zone.serviceable_cities || "",
//             serviceable_zipcodes: zone.serviceable_zipcodes || "",
//         }));

//         $("#deliverable_zones").empty().select2({
//             data: zoneOptions,
//             templateResult: formatZones,
//             templateSelection: formatzonesSelection,
//         });

//         // Set selected values
//         $("#deliverable_zones")
//             .val(selectedZones.map((zone) => zone.id))
//             .trigger("change");
//     } else {
//         $("#deliverable_zones").val(null).trigger("change");
//     }

//     $("#deliverabilityModal").modal("show");
// });

$(document).on("click", ".edit-deliverability", function () {
    let productId = $(this).data("id");
    let deliverableType = $(this).data("type");
    let selectedZones = $(this).data("zones"); // Stored zones from DB

    $("#product_id").val(productId);
    $("#deliverable_type").val(deliverableType);

    // Fetch all zones including pre-selected ones
    $.ajax({
        url: appUrl + from + "/zones/seller_zones_data",
        dataType: "json",
        delay: 250,
        data: {
            seller_id: $("#seller_id").val(),
        },
        success: function (response) {
            let allZones = response.results || [];

            // Convert stored zones into Select2 format
            let preSelectedZones = selectedZones.map((zone) => ({
                id: zone.id,
                text: zone.name,
                serviceable_cities: zone.serviceable_cities || "",
                serviceable_zipcodes: zone.serviceable_zipcodes || "",
            }));

            // Combine all zones with pre-selected ones
            let zoneOptions = [
                ...preSelectedZones,
                ...allZones.filter(
                    (zone) =>
                        !preSelectedZones.some(
                            (selected) => selected.id == zone.id
                        )
                ),
            ];

            $("#deliverable_zones").empty().select2({
                data: zoneOptions,
                templateResult: formatZones,
                templateSelection: formatzonesSelection,
                allowClear: true,
            });

            // Set pre-selected values
            $("#deliverable_zones")
                .val(preSelectedZones.map((zone) => zone.id))
                .trigger("change");
        },
    });

    $("#deliverabilityModal").modal("show");
});

// Submit Form via AJAX
$("#deliverabilityForm").submit(function (e) {
    e.preventDefault();

    let formData = $(this).serialize();

    $.ajax({
        url: appUrl + from + "/update_product_deliverability",
        type: "POST",
        data: formData,
        success: function (response) {
            // console.log(response);
            $("#deliverabilityModal").modal("hide");
            $("#seller_deliverability_table").bootstrapTable("refresh");
            if (response.error == false) {
                iziToast.success({
                    message: response.message,
                });
            } else {
                iziToast.error({
                    message: "Something went wrong! Try again.",
                });
            }
        },
        error: function (error) {
            iziToast.error({
                message: "Something went wrong! Try again.",
            });
        },
    });
});
$("#combodeliverabilityForm").submit(function (e) {
    e.preventDefault();

    let formData = $(this).serialize();

    $.ajax({
        url: appUrl + from + "/update_combo_product_deliverability",
        type: "POST",
        data: formData,
        success: function (response) {
            // console.log(response);
            $("#deliverabilityModal").modal("hide");
            $("#seller_combo_deliverability_table").bootstrapTable("refresh");
            if (response.error == false) {
                iziToast.success({
                    message: response.message,
                });
            } else {
                iziToast.error({
                    message: "Something went wrong! Try again.",
                });
            }
        },
        error: function (error) {
            iziToast.error({
                message: "Something went wrong! Try again.",
            });
        },
    });
});

$(function () {
    $(".products_display_style_for_web").on("change", function () {
        var selectedStyle = $(this).val();
        var iframe = document.getElementById(
            "products_display_style_for_web_iframe"
        ).contentWindow;

        // Send selected style to the iframe
        iframe.postMessage(selectedStyle, "*");
    });

    // Trigger change event on page load to set initial view
    $(".products_display_style_for_web").trigger("change");
});
$(function () {
    $(".categories_display_style_for_web").on("change", function () {
        var selectedStyle = $(this).val();
        var iframe = document.getElementById(
            "categories_display_style_for_web_iframe"
        ).contentWindow;

        // Send selected style to the iframe
        iframe.postMessage(selectedStyle, "*");
    });

    // Trigger change event on page load to set initial view
    $(".categories_display_style_for_web").trigger("change");
});
$(function () {
    $(".brands_display_style_for_web").on("change", function () {
        var selectedStyle = $(this).val();
        var iframe = document.getElementById(
            "brands_display_style_for_web_iframe"
        ).contentWindow;

        // Send selected style to the iframe
        iframe.postMessage(selectedStyle, "*");
    });

    // Trigger change event on page load to set initial view
    $(".brands_display_style_for_web").trigger("change");
});
$(function () {
    $(".wishlist_display_style_for_web").on("change", function () {
        var selectedStyle = $(this).val();
        var iframe = document.getElementById(
            "wishlist_display_style_for_web_iframe"
        ).contentWindow;

        // Send selected style to the iframe
        iframe.postMessage(selectedStyle, "*");
    });

    // Trigger change event on page load to set initial view
    $(".wishlist_display_style_for_web").trigger("change");
});

$(function () {
    function updateProductCardIframe(selectedStyle) {
        var iframe = document.getElementById(
            "products_display_style_for_web_iframe"
        );

        if (!iframe) return;

        // Ensure iframe src is updated only when selection changes
        iframe.src = "/admin/web_product_card_style";

        // Wait for iframe to load before sending postMessage
        iframe.onload = function () {
            iframe.contentWindow.postMessage(selectedStyle, "*");
        };
    }

    // Get the initially selected value
    var selectedStyle = $(".products_display_style_for_web").val();

    // Update iframe on page load
    updateProductCardIframe(selectedStyle);
});
$(function () {
    function updateCategoryCardIframe(selectedStyle) {
        var iframe = document.getElementById(
            "categories_display_style_for_web_iframe"
        );

        if (!iframe) return;

        // Ensure iframe src is updated only when selection changes
        iframe.src = "/admin/web_categories_style";

        // Wait for iframe to load before sending postMessage
        iframe.onload = function () {
            iframe.contentWindow.postMessage(selectedStyle, "*");
        };
    }

    // Get the initially selected value
    var selectedStyle = $(".categories_display_style_for_web").val();

    // Update iframe on page load
    updateCategoryCardIframe(selectedStyle);
});
$(function () {
    function updateBrandCardIframe(selectedStyle) {
        var iframe = document.getElementById(
            "brands_display_style_for_web_iframe"
        );

        if (!iframe) return;

        // Ensure iframe src is updated only when selection changes
        iframe.src = "/admin/web_brands_style";

        // Wait for iframe to load before sending postMessage
        iframe.onload = function () {
            iframe.contentWindow.postMessage(selectedStyle, "*");
        };
    }

    // Get the initially selected value
    var selectedStyle = $(".brands_display_style_for_web").val();

    // Update iframe on page load
    updateBrandCardIframe(selectedStyle);
});
$(function () {
    function updateWishlistCardIframe(selectedStyle) {
        var iframe = document.getElementById(
            "wishlist_display_style_for_web_iframe"
        );

        if (!iframe) return;

        // Ensure iframe src is updated only when selection changes
        iframe.src = "/admin/web_wishlist_style";

        // Wait for iframe to load before sending postMessage
        iframe.onload = function () {
            iframe.contentWindow.postMessage(selectedStyle, "*");
        };
    }

    // Get the initially selected value
    var selectedStyle = $(".wishlist_display_style_for_web").val();

    // Update iframe on page load
    updateWishlistCardIframe(selectedStyle);
});
$(document).on("click", ".edit-language", function () {
    var languageId = $(this).data("id");
    var languageName = $(this).data("name");
    var languageCode = $(this).data("code");

    // Set values in modal fields
    $("#language_id").val(languageId);
    $("#language_name").val(languageName);
});

// Handle AJAX form submission
$("#editLanguageForm").on("submit", function (e) {
    e.preventDefault(); // Prevent default form submission

    var languageId = $("#language_id").val();
    var formData = {
        _token: $('meta[name="csrf-token"]').attr("content"),
        language: $("#language_name").val(),
        _method: "PUT",
    };

    $.ajax({
        url: "/languages/update/" + languageId,
        type: "POST",
        data: formData,
        success: function (response) {
            if (response.error == false) {
                iziToast.success({
                    message: response.message,
                });
                $("#editLanguageModal").modal("hide");
                location.reload();
            } else {
                iziToast.error({
                    message: response.message,
                });
            }
        },
        error: function (xhr) {
            iziToast.error({
                message: xhr.responseJSON.message,
            });
        },
    });
});

// Handle delete action
$(document).on("click", ".delete-language", function () {
    var deleteUrl = $(this).data("url");

    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: deleteUrl,
                type: "DELETE",
                data: {
                    _token: $('meta[name="csrf-token"]').attr("content"),
                },
                success: function (response) {
                    if (response.error == false) {
                        iziToast.success({
                            message: response.message,
                        });
                        location.reload();
                    }
                },
                error: function () {
                    Swal.fire("Error!", "Something went wrong!", "error");
                },
            });
        }
    });
});


// $(function () {
//     function initCategorySelect(storeId) {
//         $("#seller_categories").select2({
//             ajax: {
//                 url: appUrl + "seller/categories/get_category_details",
//                 dataType: "json",
//                 delay: 250,
//                 data: function (params) {
//                     return {
//                         store_id: storeId,
//                         search: params.term,
//                         limit: 10,
//                     };
//                 },
//                 processResults: function (response) {
//                     return {
//                         results: response.results,
//                     };
//                 },
//                 cache: true,
//             },
//             escapeMarkup: function (markup) {
//                 return markup;
//             },
//             placeholder:
//                 $("#seller_categories").data("placeholder") ||
//                 "Search for categories",
//             width: $("#seller_categories").data("width")
//                 ? $("#categories").data("width")
//                 : "100%",
//             allowClear: Boolean($("#seller_categories").data("allow-clear")),
//         });
//     }

//     // Initialize Select2 with first store's ID
//     let defaultStoreId = $(".seller_register_store_id").val();
//     initCategorySelect(defaultStoreId);

//     // When store changes, reinitialize Select2 with new store ID
//     $(".seller_register_store_id").on("change", function () {
//         let storeId = $(this).val();
//         $("#seller_categories").val(null).trigger("change"); // Clear old selections
//         initCategorySelect(storeId);
//     });
// });

$(function () {
    function initCategorySelect(storeId) {
        $("#seller_categories").select2({
            ajax: {
                url: appUrl + "seller/categories/get_category_details",
                dataType: "json",
                delay: 250,
                data: function (params) {
                    return {
                        store_id: storeId,
                        search: params.term,
                        limit: 10,
                    };
                },
                processResults: function (response) {
                    let categories = [];

                    // Process categories
                    response.results.forEach((category) => {
                        if (category.parent_id == 0) {
                            // Parent category should also be selectable
                            let parentCategory = {
                                id: category.id,
                                text: category.text,
                            };

                            // Find children
                            let children = response.results
                                .filter(
                                    (child) => child.parent_id === category.id
                                )
                                .map((child) => ({
                                    id: child.id,
                                    text: `\u00A0\u00A0\u00A0 ${child.text}`,
                                }));

                            if (children.length > 0) {
                                categories.push(parentCategory, ...children);
                            } else {
                                categories.push(parentCategory);
                            }
                        }
                    });

                    return {
                        results:
                            categories.length > 0
                                ? categories
                                : response.results.map((cat) => ({
                                    id: cat.id,
                                    text: cat.text,
                                })),
                    };
                },
                cache: true,
            },
            placeholder:
                $("#seller_categories").data("placeholder") ||
                "Search for categories",
            width: $("#seller_categories").data("width") || "100%",
            allowClear: Boolean($("#seller_categories").data("allow-clear")),
            escapeMarkup: function (markup) {
                return markup;
            },
        });
    }

    // Initialize Select2 with first store's ID
    let defaultStoreId = $(".seller_register_store_id").val();
    initCategorySelect(defaultStoreId);

    // When store changes, reinitialize Select2 with new store ID
    $(".seller_register_store_id").on("change", function () {
        let storeId = $(this).val();
        $("#seller_categories").val(null).trigger("change");
        initCategorySelect(storeId);
    });
});
