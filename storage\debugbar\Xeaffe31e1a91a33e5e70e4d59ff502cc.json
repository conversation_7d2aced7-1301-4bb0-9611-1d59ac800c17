{"__meta": {"id": "Xeaffe31e1a91a33e5e70e4d59ff502cc", "datetime": "2025-06-28 18:34:05", "utime": **********.887671, "method": "GET", "uri": "/seller/offers/list?limit=10&sort=id&order=desc&offset=0", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 13, "messages": [{"message": "[18:34:05] LOG.info: list request [\n    {\n        \"limit\": \"10\",\n        \"sort\": \"id\",\n        \"order\": \"desc\",\n        \"offset\": \"0\"\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.731491, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: trim(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 212", "message_html": null, "is_string": false, "label": "warning", "time": **********.738072, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.info: offer  [\n    [\n        {\n            \"id\": 36,\n            \"name\": \"\\u062e\\u0635\\u0645 \\u0639\\u0644\\u0649 \\u0622\\u064a\\u0641\\u0648\\u0646 15\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-23T14:55:31.000000Z\",\n            \"expire_date\": \"2025-06-13T14:55:31.000000Z\",\n            \"offer_views\": 470,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 36,\n                    \"image\": \"offers\\/iphone.jpg\"\n                }\n            ]\n        },\n        {\n            \"id\": 43,\n            \"name\": \"\\u0639\\u0631\\u0648\\u0636 \\u0627\\u0644\\u0623\\u0644\\u0639\\u0627\\u0628 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062a\\u0631\\u0648\\u0646\\u064a\\u0629\",\n            \"is_active\": true,\n            \"created_at\": \"2025-05-23T14:55:31.000000Z\",\n            \"expire_date\": \"2025-07-10T14:55:31.000000Z\",\n            \"offer_views\": 688,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 43,\n                    \"image\": \"offers\\/games.jpg\"\n                }\n            ]\n        },\n        {\n            \"id\": 62,\n            \"name\": \"jjk\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-11T13:27:30.000000Z\",\n            \"expire_date\": null,\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": []\n        },\n        {\n            \"id\": 78,\n            \"name\": \"asads\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T00:43:01.000000Z\",\n            \"expire_date\": \"2025-06-20T02:42:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 78,\n                    \"image\": \"\\/media\\/r-1750292078_4936.jpg\"\n                }\n            ]\n        },\n        {\n            \"id\": 79,\n            \"name\": \"sdfsdfv\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T00:44:45.000000Z\",\n            \"expire_date\": \"2025-06-20T02:44:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 79,\n                    \"image\": \"\\/media\\/r-1750292078_4936.jpg\"\n                }\n            ]\n        },\n        {\n            \"id\": 80,\n            \"name\": \"sad\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T00:56:05.000000Z\",\n            \"expire_date\": \"2025-06-21T02:54:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 80,\n                    \"image\": \"\\/offers\\/screenshot-2025-06-18-154805-1750294538_7652.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 81,\n            \"name\": \"ssg\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T01:01:37.000000Z\",\n            \"expire_date\": \"2025-06-20T03:00:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": []\n        },\n        {\n            \"id\": 82,\n            \"name\": \"adasss\",\n            \"is_active\": false,\n            \"created_at\": \"2025-06-19T01:05:50.000000Z\",\n            \"expire_date\": \"2025-06-28T03:05:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 82,\n                    \"image\": \"offers\\/1750327263_offer.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 84,\n            \"name\": \"sadsa\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T10:54:00.000000Z\",\n            \"expire_date\": \"2025-06-20T12:53:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 84,\n                    \"image\": \"offers\\/jPBtWzjieetdvW1uPzownFQmEA6UOCi7UcrJhY12.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 85,\n            \"name\": \"sadsa\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T10:55:27.000000Z\",\n            \"expire_date\": \"2025-06-20T12:55:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 85,\n                    \"image\": \"offers\\/Z4dy4agqb5r8CP6RAlrRfGATYbaEUaHVJHPK4j39.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 86,\n            \"name\": \"ds\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T10:56:06.000000Z\",\n            \"expire_date\": \"2025-06-20T12:55:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 86,\n                    \"image\": \"offers\\/3Kf35auLTGlNT11TAGP4KCxXvidzRqje7xVKTFSU.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 87,\n            \"name\": \"ds\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T10:57:20.000000Z\",\n            \"expire_date\": \"2025-06-26T12:57:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 87,\n                    \"image\": \"offers\\/FiNL1Rr4C3cRlH1jnpdYnYrmLyZj8lK7IzUz2W7g.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 88,\n            \"name\": \"asa\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T10:58:44.000000Z\",\n            \"expire_date\": \"2025-06-20T12:58:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 88,\n                    \"image\": \"offers\\/1Eo0szEGbm5roRaxUrt3vNbiTbzhYv1ZCDLTii2j.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 89,\n            \"name\": \"asa\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T10:59:35.000000Z\",\n            \"expire_date\": \"2025-07-02T12:59:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 89,\n                    \"image\": \"offers\\/Wz7o0Z1QSNDr9kxKqLS8OMPK8Ew9cbMsYjcaJebH.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 90,\n            \"name\": \"sa\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T11:01:15.000000Z\",\n            \"expire_date\": \"2025-06-24T13:01:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 90,\n                    \"image\": \"offers\\/VqwhGay4JyJgfSVyZsqgZ9CjVaN7mS1mN4tAMBB2.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 91,\n            \"name\": \"dsds\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T11:13:29.000000Z\",\n            \"expire_date\": \"2025-06-20T13:13:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 91,\n                    \"image\": \"offers\\/hX1H87hz8muZBwfGu83PsYeQWLHdYYcl53Z6pNwC.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 92,\n            \"name\": \"asdasd\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T11:56:44.000000Z\",\n            \"expire_date\": \"2025-06-20T13:56:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 92,\n                    \"image\": \"offers\\/C01EgPgsuGv8klWwXXhzcep2uODdkot0GlUBscf6.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 93,\n            \"name\": \"asdasd\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T11:57:38.000000Z\",\n            \"expire_date\": \"2025-06-26T13:57:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 93,\n                    \"image\": \"offers\\/0OAugPqrHnYJtTRDTTTkS82J66NZhmS803K6LweV.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 94,\n            \"name\": \"dx\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T11:59:42.000000Z\",\n            \"expire_date\": \"2025-06-21T13:59:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 94,\n                    \"image\": \"offers\\/IDVLNpoer64nKk1UF9SQKsHEuao1KrhH2ok36iKF.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 95,\n            \"name\": \"dx\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-19T12:00:37.000000Z\",\n            \"expire_date\": \"2025-06-21T14:00:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 2,\n            \"images\": [\n                {\n                    \"offer_id\": 95,\n                    \"image\": \"offers\\/rj56t1uLoCsfeBKauPYRBUpLFYC5MnicnxIzUuWJ.png\"\n                }\n            ]\n        },\n        {\n            \"id\": 96,\n            \"name\": \"saa\",\n            \"is_active\": true,\n            \"created_at\": \"2025-06-28T18:32:42.000000Z\",\n            \"expire_date\": \"2025-07-05T20:32:00.000000Z\",\n            \"offer_views\": 0,\n            \"is_approved\": 1,\n            \"images\": [\n                {\n                    \"offer_id\": 96,\n                    \"image\": \"offers\\/J11viuD8YA0qhPegN6fLe8l17qty4Ac1QD3Jau8Y.png\"\n                }\n            ]\n        }\n    ]\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.784345, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 309", "message_html": null, "is_string": false, "label": "warning", "time": **********.829397, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 309", "message_html": null, "is_string": false, "label": "warning", "time": **********.851668, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 309", "message_html": null, "is_string": false, "label": "warning", "time": **********.854333, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 309", "message_html": null, "is_string": false, "label": "warning", "time": **********.856983, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 309", "message_html": null, "is_string": false, "label": "warning", "time": **********.860359, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 309", "message_html": null, "is_string": false, "label": "warning", "time": **********.86288, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 309", "message_html": null, "is_string": false, "label": "warning", "time": **********.865543, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 309", "message_html": null, "is_string": false, "label": "warning", "time": **********.868225, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 309", "message_html": null, "is_string": false, "label": "warning", "time": **********.870865, "xdebug_link": null, "collector": "log"}, {"message": "[18:34:05] LOG.warning: str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php on line 309", "message_html": null, "is_string": false, "label": "warning", "time": **********.873483, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751135643.702276, "end": **********.88771, "duration": 2.185434103012085, "duration_str": "2.19s", "measures": [{"label": "Booting", "start": 1751135643.702276, "relative_start": 0, "end": **********.639559, "relative_end": **********.639559, "duration": 1.9372830390930176, "duration_str": "1.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.639583, "relative_start": 1.9373071193695068, "end": **********.887713, "relative_end": 2.86102294921875e-06, "duration": 0.24812984466552734, "duration_str": "248ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30830128, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 20, "templates": [{"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.848766, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.850094, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.852283, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.853132, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.854964, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.855767, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.85757, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.858359, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.861329, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.86214, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.863471, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.864349, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.866188, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.866978, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.868838, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.869616, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.871442, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.872285, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}, {"name": "components.status-dropdown", "param_count": null, "params": [], "start": **********.874105, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/status-dropdown.blade.phpcomponents.status-dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fstatus-dropdown.blade.php&line=1", "ajax": false, "filename": "status-dropdown.blade.php", "line": "?"}}, {"name": "components.image-lightbox", "param_count": null, "params": [], "start": **********.874954, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/image-lightbox.blade.phpcomponents.image-lightbox", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fimage-lightbox.blade.php&line=1", "ajax": false, "filename": "image-lightbox.blade.php", "line": "?"}}]}, "route": {"uri": "GET seller/offers/list", "middleware": "web, CheckInstallation, auth, role:seller", "controller": "App\\Http\\Controllers\\Seller\\OfferController@list", "namespace": null, "prefix": "", "where": [], "as": "seller.products.list", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=208\" onclick=\"\">app/Http/Controllers/Seller/OfferController.php:208-340</a>"}, "queries": {"nb_statements": 14, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01786, "accumulated_duration_str": "17.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.701386, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 6.775}, {"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.710918, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 6.775, "width_percent": 4.983}, {"sql": "select * from `users` where `users`.`id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.716692, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 11.758, "width_percent": 4.759}, {"sql": "select * from `roles` where `roles`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.725242, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 16.517, "width_percent": 6.383}, {"sql": "select `id` from `stores` where `user_id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.732089, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:9251", "source": {"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=9251", "ajax": false, "filename": "function_helper.php", "line": "9251"}, "connection": "eshop", "explain": null, "start_percent": 22.9, "width_percent": 7.055}, {"sql": "select * from `settings` where `variable` = 'system_settings' limit 1", "type": "query", "params": [], "bindings": ["system_settings"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.738639, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:609", "source": {"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=609", "ajax": false, "filename": "function_helper.php", "line": "609"}, "connection": "eshop", "explain": null, "start_percent": 29.955, "width_percent": 7.559}, {"sql": "select `id`, `title_ar` as `name`, `is_active`, `created_at`, `expire_date`, `offer_views`, `is_approved` from `offers` where `store_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 248}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.745484, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:248", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 248}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=248", "ajax": false, "filename": "OfferController.php", "line": "248"}, "connection": "eshop", "explain": null, "start_percent": 37.514, "width_percent": 6.887}, {"sql": "select `offer_id`, `image_url` as `image` from `offer_images` where `offer_images`.`offer_id` in (36, 43, 62, 78, 79, 80, 81, 82, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96) and `is_main` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 248}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.754622, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:248", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 248}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=248", "ajax": false, "filename": "OfferController.php", "line": "248"}, "connection": "eshop", "explain": null, "start_percent": 44.401, "width_percent": 6.943}, {"sql": "select `id`, `title_ar` as `name`, `is_active`, `created_at`, `expire_date`, `offer_views`, `is_approved` from `offers` where `store_id` = 26 group by `offers`.`id`", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 272}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.785069, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:272", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=272", "ajax": false, "filename": "OfferController.php", "line": "272"}, "connection": "eshop", "explain": null, "start_percent": 51.344, "width_percent": 8.175}, {"sql": "select `offer_id`, `image_url` as `image` from `offer_images` where `offer_images`.`offer_id` in (36, 43, 62, 78, 79, 80, 81, 82, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96) and `is_main` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 272}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7924361, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:272", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 272}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=272", "ajax": false, "filename": "OfferController.php", "line": "272"}, "connection": "eshop", "explain": null, "start_percent": 59.518, "width_percent": 15.342}, {"sql": "select `id`, `title_ar` as `name`, `is_active`, `created_at`, `expire_date`, `offer_views`, `is_approved` from `offers` where `store_id` = 26 group by `offers`.`id`, `id` order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 278}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.800548, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:278", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 278}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=278", "ajax": false, "filename": "OfferController.php", "line": "278"}, "connection": "eshop", "explain": null, "start_percent": 74.86, "width_percent": 6.775}, {"sql": "select `offer_id`, `image_url` as `image` from `offer_images` where `offer_images`.`offer_id` in (87, 88, 89, 90, 91, 92, 93, 94, 95, 96) and `is_main` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 278}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8072422, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:278", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\OfferController.php", "line": 278}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FOfferController.php&line=278", "ajax": false, "filename": "OfferController.php", "line": "278"}, "connection": "eshop", "explain": null, "start_percent": 81.635, "width_percent": 7.671}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 403 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [403, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.839618, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:38", "source": {"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=38", "ajax": false, "filename": "AppServiceProvider.php", "line": "38"}, "connection": "eshop", "explain": null, "start_percent": 89.306, "width_percent": 6.607}, {"sql": "select * from `roles` where `roles`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.8450499, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:39", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=39", "ajax": false, "filename": "AppServiceProvider.php", "line": "39"}, "connection": "eshop", "explain": null, "start_percent": 95.913, "width_percent": 4.087}]}, "models": {"data": {"App\\Models\\awfarly\\Offer": {"value": 52, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FOffer.php&line=1", "ajax": false, "filename": "Offer.php", "line": "?"}}, "App\\Models\\awfarly\\OfferImages": {"value": 48, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FOfferImages.php&line=1", "ajax": false, "filename": "OfferImages.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Store": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Setting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}}, "count": 107, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/seller/offers/manage_offers\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "store_id": "26", "store_name": "<PERSON><PERSON>", "store_image": "stores/1750281403_logo.png", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "403", "system_settings": "{\"app_name\":\"\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a\",\"support_number\":\"919876543210\",\"support_email\":\"<EMAIL>\",\"logo\":\"media\\/1751134451_logo.png\",\"favicon\":null,\"storage_type\":\"local\",\"current_version_of_android_app\":\"1.1.0\",\"current_version_of_ios_app\":\"1.0.0\",\"version_system_status\":0,\"expand_product_image\":0,\"customer_app_maintenance_status\":1,\"offer_request_status\":0,\"message_for_customer_app\":\"sdsa\",\"sidebar_color\":null,\"sidebar_type\":null,\"navbar_fixed\":0,\"theme_mode\":0,\"currency_setting\":\"\\u062f.\\u0644\"}"}, "request": {"path_info": "/seller/offers/list", "status_code": "<pre class=sf-dump id=sf-dump-318667328 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-318667328\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-207284218 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n  \"<span class=sf-dump-key>offset</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-207284218\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1747746160 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1747746160\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-222130394 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/seller/offers/manage_offers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlBCVVlZLys1N0dvWGJPOThPWi9NZ0E9PSIsInZhbHVlIjoiMkptOTgxU2ZKdzI5eGovK3ZQUkFZTXJNeHlpVnVJT0c3ZXJEOWMrelNZNkFlMUt2T1ZKbVBvVStUZWtzdm1NVllPZ0c2TUNuaXBoUkxyNWlGR2dqdE9Wb2Z1SE44YllFUnlrVnB3YVROUkJSQlNRRTNzQ1hmbHllN1VBM0hkVGEiLCJtYWMiOiJjMmRhZDZjMzc0MjU0ZjA2MDExMjlhZDk0OTY0M2U0Yzc4ZDRmNDBiZjcxMzA5NjM0Njg4Y2YwYjMzOTlmZTI3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImxBanNKYmxZOWV2aVRhZXZNRXNRckE9PSIsInZhbHVlIjoiUktyM295MWRZRUVIcUJFUEp6M0dhQStic0t6OWVIWjlNZDJHK1l6NjRKVm9xVnljMnNybGJYMkI0VVR1UEppMWdXUHlianNlbDU0WFNQVVFDUWRua0dLa0VLZlpOVDlnQmhQUzNJL3Z4VDROeGFYYUtxNTY3V2llVUp1TmNTa3UiLCJtYWMiOiIyMzYxODc5ZTg0ZTk3MjFkNGY5YzdjNWZjZjQ5YjQwZTMyYmJmZWU4NWQ1MzczMDkzY2Q0NjExNTRjMzBlZWQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222130394\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1312847314 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mVUiKCS25MLlribzCWoFa9ZEN0oL6qkA0JoxpOT0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1312847314\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1308621618 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 18:34:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImUyVEZtQnlmYStzM0svQW9rdzR1ckE9PSIsInZhbHVlIjoiMWFSdjdjdktGZ0YvZnZIMmNFSkVPUGdHUHg1Q1BReXR1cXUxVEVMd25zMldDYm9kREp4M3hEREtSR1BaWTZvYlk3c0dabHVKbHhRT09mcVhNd1IwNk55TVFOdGRJdWxKR0FzY1ZlckV5TEJobWhnbndnWS9IWDBLYnl0NFhkWmQiLCJtYWMiOiIyODE2YjIyMjBhYTFhYjU2ZTIxNzNkNzE1NDVhODA4NGY5ODljOWUyM2M4MzRiY2QxOTU4MTU3ZGQ1MWNlNzM5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:34:05 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IklGWXIranFZVjR0ZnF3TEFQV05hbEE9PSIsInZhbHVlIjoiMFdrcGJtYjlLcWRabHlTczlYZzBFR3ZRdjJNcVFuNUhCbUxTZy8wU3BtL2xmVThITEFLMTgvdlFwWWI4Vk5sSUE4Ly92MHZhN3d0aGlmaXovNXVVMmdKdXFzRXF2ZGljVG1GaFU4TzhuUW5ZcUgwSkpqdGlVZG8vWSs1N2V0dmsiLCJtYWMiOiI5NTk4OWY2OGEyNDhiNzYxMWM0YjhiYmJjYzRmYTkyN2Q2ODdlY2Q1ZTJjNjViOWRlOTEyMzY5YmY0NzkyMzQyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:34:05 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImUyVEZtQnlmYStzM0svQW9rdzR1ckE9PSIsInZhbHVlIjoiMWFSdjdjdktGZ0YvZnZIMmNFSkVPUGdHUHg1Q1BReXR1cXUxVEVMd25zMldDYm9kREp4M3hEREtSR1BaWTZvYlk3c0dabHVKbHhRT09mcVhNd1IwNk55TVFOdGRJdWxKR0FzY1ZlckV5TEJobWhnbndnWS9IWDBLYnl0NFhkWmQiLCJtYWMiOiIyODE2YjIyMjBhYTFhYjU2ZTIxNzNkNzE1NDVhODA4NGY5ODljOWUyM2M4MzRiY2QxOTU4MTU3ZGQ1MWNlNzM5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:34:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IklGWXIranFZVjR0ZnF3TEFQV05hbEE9PSIsInZhbHVlIjoiMFdrcGJtYjlLcWRabHlTczlYZzBFR3ZRdjJNcVFuNUhCbUxTZy8wU3BtL2xmVThITEFLMTgvdlFwWWI4Vk5sSUE4Ly92MHZhN3d0aGlmaXovNXVVMmdKdXFzRXF2ZGljVG1GaFU4TzhuUW5ZcUgwSkpqdGlVZG8vWSs1N2V0dmsiLCJtYWMiOiI5NTk4OWY2OGEyNDhiNzYxMWM0YjhiYmJjYzRmYTkyN2Q2ODdlY2Q1ZTJjNjViOWRlOTEyMzY5YmY0NzkyMzQyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:34:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308621618\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2027300481 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/seller/offers/manage_offers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>26</span>\n  \"<span class=sf-dump-key>store_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">bothina</span>\"\n  \"<span class=sf-dump-key>store_image</span>\" => \"<span class=sf-dump-str title=\"26 characters\">stores/1750281403_logo.png</span>\"\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>403</span>\n  \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"528 characters\">{&quot;app_name&quot;:&quot;\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:&quot;media\\/1751134451_logo.png&quot;,&quot;favicon&quot;:null,&quot;storage_type&quot;:&quot;local&quot;,&quot;current_version_of_android_app&quot;:&quot;1.1.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;version_system_status&quot;:0,&quot;expand_product_image&quot;:0,&quot;customer_app_maintenance_status&quot;:1,&quot;offer_request_status&quot;:0,&quot;message_for_customer_app&quot;:&quot;sdsa&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0,&quot;currency_setting&quot;:&quot;\\u062f.\\u0644&quot;}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2027300481\", {\"maxDepth\":0})</script>\n"}}