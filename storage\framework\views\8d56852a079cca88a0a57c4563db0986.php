<?php $__env->startSection('title'); ?>
    <?= 'Update System Users' ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="container-fluid flex-grow-1 container-p-y">
        <!-- Basic Layout -->

        <div class="row">
            <div class="col-xl">
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="mb-3"> <?php echo e($user->username); ?></h4>
                            <form method="post"
                                class="submit_form"action="<?php echo e(route('system_users.permissions_update', $user->id)); ?>">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PUT'); ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>Module/Permissions</th>
                                                <th>View</th>
                                                <th>Create</th>
                                                <th>Edit</th>
                                                <th>Delete</th>
                                            </tr>
                                        </thead>
                                        <?php $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section => $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tbody>

                                                <tr>
                                                    <td><?php echo e(ucfirst($section)); ?></td>
                                                    <td>
                                                        <div class="form-check">
                                                            <input type="checkbox" class="form-check-input"
                                                                name="permissions[<?php echo e($section); ?>][view]"
                                                                <?php echo e($permission['view ' . $section] ? 'checked' : ''); ?>

                                                                value="view <?php echo e($section); ?>">
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="form-check">
                                                            <input type="checkbox" class="form-check-input"
                                                                name="permissions[<?php echo e($section); ?>][create]"
                                                                <?php echo e($permission['create ' . $section] ? 'checked' : ''); ?>

                                                                value="create <?php echo e($section); ?>">
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="form-check">
                                                            <input type="checkbox" class="form-check-input"
                                                                name="permissions[<?php echo e($section); ?>][edit]"
                                                                <?php echo e($permission['edit ' . $section] ? 'checked' : ''); ?>

                                                                value="edit <?php echo e($section); ?>">
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="form-check">
                                                            <input type="checkbox" class="form-check-input"
                                                                name="permissions[<?php echo e($section); ?>][delete]"
                                                                <?php echo e($permission['delete ' . $section] ? 'checked' : ''); ?>

                                                                value="delete <?php echo e($section); ?>">
                                                        </div>
                                                    </td>
                                                </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button class="btn btn-primary mt-2  align-content-end submit_button"
                                        type="submit"><?php echo e(labels('admin_labels.update_permissions', 'Update Permissions')); ?></button>
                                </div>
                            </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/pages/forms/update_system_users.blade.php ENDPATH**/ ?>