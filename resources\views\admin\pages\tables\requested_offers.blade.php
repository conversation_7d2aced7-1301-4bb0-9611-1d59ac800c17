@extends('admin.layout')

@section('title', __('admin_labels.manage_requested_offers'))

@section('content')
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="bx bx-check-circle me-2"></i> {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif
@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="bx bx-error-circle me-2"></i> {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

<div class="container py-4">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-bottom-0 py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="bx bx-list-ul me-2"></i>
                    {{ __('admin_labels.manage_requested_offers') }}
                </h5>
            </div>
        </div>

        <div class="card-body">
            <div class="row g-4">
                @forelse($offers as $offer)
                <div class="col-12">
                    <div class="card border-0 shadow-sm hover-lift">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-start gap-3">
                                @if($offer->images->first())
                                <div class="flex-shrink-0">
                                    <img src="{{ getMediaImageUrl($offer->images->first()->image_url) }}"
                                        class="rounded-2 border"
                                        style="width: 120px; height: 90px; object-fit: cover;">
                                </div>
                                @endif

                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1 fw-semibold">{{ $offer->title_ar }} - {{ $offer->title_en }}</h6>
                                            <p class="small text-muted mb-2">{{ $offer->description }}</p>


                                            <div class="d-flex flex-wrap gap-3">
                                                <span class="badge bg-primary bg-opacity-10 text-dark">
                                                    {{ $offer->category->name ?? '-' }}
                                                </span>
                                                <span class="badge bg-success bg-opacity-10 text-dark">
                                                    {{ $offer->price }} د.ل
                                                </span>
                                                @if($offer->discounted_price)
                                                <span class="badge bg-warning bg-opacity-10 text-dark">
                                                    {{ __('admin_labels.discount') }}: {{ $offer->discounted_price }} د.ل
                                                </span>
                                                @endif
                                            </div>
                                        </div>

                                        <div class="text-end">
                                            <small class="text-muted d-block">{{ __('admin_labels.store_name') }}</small>
                                            <span class="fw-medium">{{ $offer->store->name ?? '-' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mt-3 pt-2 border-top">
                                <div class="small text-muted">
                                    <span class="me-3"><i class="bx bx-calendar me-1"></i> {{ $offer->created_at->format('Y-m-d') }}</span>
                                </div>

                                <div class="d-flex gap-2">
                                    <a href="{{ route('admin.requested_offers.show', $offer->id) }}"
                                        class="btn btn-sm btn-outline-primary px-3 rounded-2">
                                        <i class="bx bx-show me-1"></i> {{ __('admin_labels.view') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bx bx-package text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">{{ __('admin_labels.no_requested_offers') }}</h5>
                    </div>
                </div>
                @endforelse
            </div>

            @if($offers->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $offers->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .hover-lift {
        transition: all 0.25s ease;
    }

    .hover-lift:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
    }

    .badge {
        font-weight: 500;
        padding: 0.35em 0.65em;
    }
</style>
@endpush
