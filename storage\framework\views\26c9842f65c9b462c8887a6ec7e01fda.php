<!DOCTYPE html>
<html lang="en">

<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

<?php echo $__env->make('admin.include_css', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body>
    <div id="db-wrapper">

        <?php if (isset($component)) { $__componentOriginal5a187440a9bb387888dc3a7e008245dc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5a187440a9bb387888dc3a7e008245dc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.side-bar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.side-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5a187440a9bb387888dc3a7e008245dc)): ?>
<?php $attributes = $__attributesOriginal5a187440a9bb387888dc3a7e008245dc; ?>
<?php unset($__attributesOriginal5a187440a9bb387888dc3a7e008245dc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5a187440a9bb387888dc3a7e008245dc)): ?>
<?php $component = $__componentOriginal5a187440a9bb387888dc3a7e008245dc; ?>
<?php unset($__componentOriginal5a187440a9bb387888dc3a7e008245dc); ?>
<?php endif; ?>
        <div id="page-content">
            <?php if (isset($component)) { $__componentOriginal7a59feeeee8ce3f3cb3543e6971245f2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7a59feeeee8ce3f3cb3543e6971245f2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.header','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7a59feeeee8ce3f3cb3543e6971245f2)): ?>
<?php $attributes = $__attributesOriginal7a59feeeee8ce3f3cb3543e6971245f2; ?>
<?php unset($__attributesOriginal7a59feeeee8ce3f3cb3543e6971245f2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7a59feeeee8ce3f3cb3543e6971245f2)): ?>
<?php $component = $__componentOriginal7a59feeeee8ce3f3cb3543e6971245f2; ?>
<?php unset($__componentOriginal7a59feeeee8ce3f3cb3543e6971245f2); ?>
<?php endif; ?>
            <div class="container-fluid mt-5 px-6"  <?php echo e(session()->get('locale') == 'en' ? 'dir=ltr' : 'dir=rtl'); ?>>
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
    </div>
    <?php if (isset($component)) { $__componentOriginal13a4d234756c16032caa3e2834ca83d8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal13a4d234756c16032caa3e2834ca83d8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.footer','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('admin.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal13a4d234756c16032caa3e2834ca83d8)): ?>
<?php $attributes = $__attributesOriginal13a4d234756c16032caa3e2834ca83d8; ?>
<?php unset($__attributesOriginal13a4d234756c16032caa3e2834ca83d8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal13a4d234756c16032caa3e2834ca83d8)): ?>
<?php $component = $__componentOriginal13a4d234756c16032caa3e2834ca83d8; ?>
<?php unset($__componentOriginal13a4d234756c16032caa3e2834ca83d8); ?>
<?php endif; ?>
    <!-- Scripts -->
</body>
<?php echo $__env->make('admin.include_script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</html>
<?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/layout.blade.php ENDPATH**/ ?>