{"__meta": {"id": "Xc6a9e56f4f48163db6ba60c6ac9c9401", "datetime": "2025-06-28 18:30:23", "utime": **********.760245, "method": "GET", "uri": "/seller/categories/get_seller_categories?ignore_status=0", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 10, "messages": [{"message": "[18:30:23] LOG.info: category_ids FROM SELLERE [\n    {\n        \"id\": 71,\n        \"parent_id\": null,\n        \"slug\": \"electronics\",\n        \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n        \"row_order\": 0,\n        \"is_active\": true,\n        \"clicks\": 0,\n        \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n        \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n        \"name_ar\": \"\\u0627\\u0644\\u0643\\u062a\\u0631\\u0648\\u0646\\u064a\\u0627\\u062a\",\n        \"name_en\": \"Electronics\",\n        \"description_en\": null,\n        \"description_ar\": null,\n        \"pivot\": {\n            \"store_id\": 26,\n            \"category_id\": 71\n        }\n    },\n    {\n        \"id\": 72,\n        \"parent_id\": null,\n        \"slug\": \"fashion-1\",\n        \"image\": \"categories\\/1750699935_cate.png\",\n        \"row_order\": 0,\n        \"is_active\": true,\n        \"clicks\": 0,\n        \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n        \"updated_at\": \"2025-06-23T17:32:15.000000Z\",\n        \"name_ar\": \"\\u0645\\u0644\\u0627\\u0628\\u0633\",\n        \"name_en\": \"Fashion\",\n        \"description_en\": null,\n        \"description_ar\": null,\n        \"pivot\": {\n            \"store_id\": 26,\n            \"category_id\": 72\n        }\n    },\n    {\n        \"id\": 73,\n        \"parent_id\": null,\n        \"slug\": \"health-beauty-1\",\n        \"image\": \"categories\\/1750700043_cate.png\",\n        \"row_order\": 0,\n        \"is_active\": true,\n        \"clicks\": 0,\n        \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n        \"updated_at\": \"2025-06-23T17:34:03.000000Z\",\n        \"name_ar\": \"\\u0635\\u062d\\u0629 \\u0648\\u062c\\u0645\\u0627\\u0644\",\n        \"name_en\": \"Health & Beauty\",\n        \"description_en\": null,\n        \"description_ar\": null,\n        \"pivot\": {\n            \"store_id\": 26,\n            \"category_id\": 73\n        }\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.718978, "xdebug_link": null, "collector": "log"}, {"message": "[18:30:23] LOG.info: category : [\n    [\n        {\n            \"id\": 77,\n            \"parent_id\": 71,\n            \"slug\": \"phones\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0647\\u0648\\u0627\\u062a\\u0641\",\n            \"name_en\": \"Phones\",\n            \"description_en\": null,\n            \"description_ar\": null\n        }\n    ]\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.723312, "xdebug_link": null, "collector": "log"}, {"message": "[18:30:23] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.726728, "xdebug_link": null, "collector": "log"}, {"message": "[18:30:23] LOG.info: category : [\n    [\n        {\n            \"id\": 79,\n            \"parent_id\": 72,\n            \"slug\": \"watches\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0633\\u0627\\u0639\\u0627\\u062a\",\n            \"name_en\": \"Watches\",\n            \"description_en\": null,\n            \"description_ar\": null\n        },\n        {\n            \"id\": 80,\n            \"parent_id\": 72,\n            \"slug\": \"jewelry\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0645\\u062c\\u0648\\u0647\\u0631\\u0627\\u062a\",\n            \"name_en\": \"Jewelry\",\n            \"description_en\": null,\n            \"description_ar\": null\n        }\n    ]\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.73183, "xdebug_link": null, "collector": "log"}, {"message": "[18:30:23] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.735288, "xdebug_link": null, "collector": "log"}, {"message": "[18:30:23] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.738881, "xdebug_link": null, "collector": "log"}, {"message": "[18:30:23] LOG.info: category : [\n    [\n        {\n            \"id\": 81,\n            \"parent_id\": 73,\n            \"slug\": \"pharmacy\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0635\\u064a\\u062f\\u0644\\u064a\\u0629\",\n            \"name_en\": \"Pharmacy\",\n            \"description_en\": null,\n            \"description_ar\": null\n        },\n        {\n            \"id\": 82,\n            \"parent_id\": 73,\n            \"slug\": \"personal-care\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0639\\u0646\\u0627\\u064a\\u0629 \\u0634\\u062e\\u0635\\u064a\\u0629\",\n            \"name_en\": \"Personal Care\",\n            \"description_en\": null,\n            \"description_ar\": null\n        },\n        {\n            \"id\": 91,\n            \"parent_id\": 73,\n            \"slug\": \"clinics\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0639\\u064a\\u0627\\u062f\\u0627\\u062a\",\n            \"name_en\": \"Clinics\",\n            \"description_en\": null,\n            \"description_ar\": null\n        }\n    ]\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.743979, "xdebug_link": null, "collector": "log"}, {"message": "[18:30:23] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.747426, "xdebug_link": null, "collector": "log"}, {"message": "[18:30:23] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.751276, "xdebug_link": null, "collector": "log"}, {"message": "[18:30:23] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.754735, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.121226, "end": **********.760265, "duration": 0.6390390396118164, "duration_str": "639ms", "measures": [{"label": "Booting", "start": **********.121226, "relative_start": 0, "end": **********.68099, "relative_end": **********.68099, "duration": 0.5597639083862305, "duration_str": "560ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.681001, "relative_start": 0.5597748756408691, "end": **********.760267, "relative_end": 1.9073486328125e-06, "duration": 0.07926607131958008, "duration_str": "79.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 29778512, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET seller/categories/get_seller_categories", "middleware": "web, CheckInstallation, auth, role:seller", "controller": "App\\Http\\Controllers\\Seller\\CategoryController@getSellerCategories", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=231\" onclick=\"\">app/Http/Controllers/Seller/CategoryController.php:231-261</a>"}, "queries": {"nb_statements": 25, "nb_visible_statements": 25, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.013450000000000002, "accumulated_duration_str": "13.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.699056, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 4.387}, {"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.7023451, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 4.387, "width_percent": 3.569}, {"sql": "select * from `users` where `users`.`id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.7045789, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 7.955, "width_percent": 3.941}, {"sql": "select * from `roles` where `roles`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.7078419, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 11.896, "width_percent": 3.197}, {"sql": "select `id` from `stores` where `user_id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7095828, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "function_helper.php:9251", "source": {"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=9251", "ajax": false, "filename": "function_helper.php", "line": "9251"}, "connection": "eshop", "explain": null, "start_percent": 15.093, "width_percent": 3.048}, {"sql": "select * from `stores` where `stores`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 236}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.71145, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:236", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=236", "ajax": false, "filename": "CategoryController.php", "line": "236"}, "connection": "eshop", "explain": null, "start_percent": 18.141, "width_percent": 4.833}, {"sql": "select `categories`.*, `store_categories`.`store_id` as `pivot_store_id`, `store_categories`.`category_id` as `pivot_category_id` from `categories` inner join `store_categories` on `categories`.`id` = `store_categories`.`category_id` where `store_categories`.`store_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 236}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.715004, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:236", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=236", "ajax": false, "filename": "CategoryController.php", "line": "236"}, "connection": "eshop", "explain": null, "start_percent": 22.974, "width_percent": 5.502}, {"sql": "select * from `categories` where `categories`.`id` = 71 limit 1", "type": "query", "params": [], "bindings": [71], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.719152, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 28.476, "width_percent": 3.197}, {"sql": "select * from `categories` where `categories`.`parent_id` in (71)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.721285, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 31.673, "width_percent": 3.941}, {"sql": "select * from `categories` where `categories`.`id` = 77 limit 1", "type": "query", "params": [], "bindings": [77], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.723494, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 35.613, "width_percent": 2.825}, {"sql": "select * from `categories` where `categories`.`parent_id` in (77)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7253249, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 38.439, "width_percent": 3.048}, {"sql": "select * from `categories` where `categories`.`id` = 72 limit 1", "type": "query", "params": [], "bindings": [72], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.728024, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 41.487, "width_percent": 3.123}, {"sql": "select * from `categories` where `categories`.`parent_id` in (72)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7296302, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 44.61, "width_percent": 3.271}, {"sql": "select * from `categories` where `categories`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": [79], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7320101, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 47.881, "width_percent": 3.569}, {"sql": "select * from `categories` where `categories`.`parent_id` in (79)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7336488, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 51.45, "width_percent": 4.535}, {"sql": "select * from `categories` where `categories`.`id` = 80 limit 1", "type": "query", "params": [], "bindings": [80], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7355719, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 55.985, "width_percent": 4.238}, {"sql": "select * from `categories` where `categories`.`parent_id` in (80)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7373278, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 60.223, "width_percent": 4.461}, {"sql": "select * from `categories` where `categories`.`id` = 73 limit 1", "type": "query", "params": [], "bindings": [73], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.739262, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 64.684, "width_percent": 4.907}, {"sql": "select * from `categories` where `categories`.`parent_id` in (73)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.741178, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 69.591, "width_percent": 4.312}, {"sql": "select * from `categories` where `categories`.`id` = 81 limit 1", "type": "query", "params": [], "bindings": [81], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7442, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 73.903, "width_percent": 3.048}, {"sql": "select * from `categories` where `categories`.`parent_id` in (81)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7457962, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 76.952, "width_percent": 4.684}, {"sql": "select * from `categories` where `categories`.`id` = 82 limit 1", "type": "query", "params": [], "bindings": [82], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.74771, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 81.636, "width_percent": 4.907}, {"sql": "select * from `categories` where `categories`.`parent_id` in (82)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.749603, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 86.543, "width_percent": 5.279}, {"sql": "select * from `categories` where `categories`.`id` = 91 limit 1", "type": "query", "params": [], "bindings": [91], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.751557, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 91.822, "width_percent": 4.981}, {"sql": "select * from `categories` where `categories`.`parent_id` in (91)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7533538, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 96.803, "width_percent": 3.197}]}, "models": {"data": {"App\\Models\\awfarly\\Category": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Store": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\awfarly\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}}, "count": 24, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/seller/offers\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "store_id": "26", "store_name": "<PERSON><PERSON>", "store_image": "stores/1750281403_logo.png", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "403", "system_settings": "{\"app_name\":\"\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a\",\"support_number\":\"919876543210\",\"support_email\":\"<EMAIL>\",\"logo\":\"media\\/1751134451_logo.png\",\"favicon\":null,\"storage_type\":\"local\",\"current_version_of_android_app\":\"1.1.0\",\"current_version_of_ios_app\":\"1.0.0\",\"version_system_status\":0,\"expand_product_image\":0,\"customer_app_maintenance_status\":1,\"offer_request_status\":0,\"message_for_customer_app\":\"sdsa\",\"sidebar_color\":null,\"sidebar_type\":null,\"navbar_fixed\":0,\"theme_mode\":0,\"currency_setting\":\"\\u062f.\\u0644\"}"}, "request": {"path_info": "/seller/categories/get_seller_categories", "status_code": "<pre class=sf-dump id=sf-dump-1645525204 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1645525204\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-430586406 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ignore_status</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-430586406\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-33983656 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-33983656\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1929851511 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/seller/offers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikw4dUIycHU3RjlqbFNQS0N3TVROZ0E9PSIsInZhbHVlIjoiaE9QNlVDQzRWV1dRdXc4RHViaWVyOTRlOXdrdE5CUXozOG1Na0tZNTVHd1FpSUJxMFY4VjlTbldIa2lOdHBqRVpuV1pYYXg4RUZlRXdkaExoWks4aFBhUTNDbWJGZ2lvSy9TeHRrUlhKQjJRWk9GcjdRMjRCMkxNN3BZRytkSHYiLCJtYWMiOiI2ZjM4Y2I3YjBkZjE2MjAyMGY3MGYxNmIyMWYxZDlhMzVjMzlkMjQ4MDkwNWY3MjFkZGJjYzdhYzg1NjdlNWJkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InoyREN1a3dUNVdqZWdTWHpQZTBRT0E9PSIsInZhbHVlIjoiL2Y0TWxJcjRhSnZrR0UrNmJSdUFyZ04xUlNLTVhHOFdGUVcwYjNXejFINFZhVmJ5RGZhQlhZUHFobTFPTFBqVWFTWkdPUE9zb1ljTWhPdUNHUVJteklCSzNxdE9YdXlSQlROdFozMGYwSVRFUW9rcEQ0cDA2dlZNMFQwVkg5eFQiLCJtYWMiOiI0Yjg2YTUzZDA0NDVkNzc0MjY2OGM1Yzg1Yzc4MmFhZWU3ZGZjMDA0ODY3MzI2YTU4Yjk4YzE0ODE3M2ZiZTIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929851511\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1983837971 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mVUiKCS25MLlribzCWoFa9ZEN0oL6qkA0JoxpOT0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1983837971\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1071378377 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 18:30:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImZQK1pqU3dsQytERDdNOG9OakIwL2c9PSIsInZhbHVlIjoiVnYydzNYR01jdytaRGRMdzhETXdUOEovUUt2c003RlNhZXczTTBpYUEyT1NxOExtM0c4L2NObWJFcWdsdnBYZTJ6Z2pReVVPZEVoNFhFeTIrOVRTSllNZE45UmM5YmsvR2xjQi9BNGlJcERneWVvWFlwdjE3RDl6c2ZST0x3UTMiLCJtYWMiOiJkOTA4MmRkOTA2OGU3MWZiNWM3NTcxMTZjNjFmNjI0MjJhYzU0NDRkNDY2MjRlOWM1ZTNmZjU0NDE4ZWVjMDYxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:30:23 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlgxQWNMZmFQaUwvd05EbDhmd0VTVFE9PSIsInZhbHVlIjoiTk8zZk5sN3I3K2RPeWZMZ0cxbGk4dWM0dEhEWG5xOUJjTERHUU1nU0UxbWh1cFJDc09EbVA2ZGZkU01GSEdhUkl2bDdMYUN2eTlpL2ZUeDI0NVFVcnJNak51RGd5YnAyWFd5elVpMlVZdjFvd1JDSm5OVzVCL3ZQVzVnQkN1UDUiLCJtYWMiOiIyZWVjMjcyNjU3YTQ3NzA1YjlhOTg3Y2IwM2VmZTkzYjhiY2MyMjZhMmQ0ZjlkMDNkMTE4NmYwMGQ3NWUxODVmIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:30:23 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImZQK1pqU3dsQytERDdNOG9OakIwL2c9PSIsInZhbHVlIjoiVnYydzNYR01jdytaRGRMdzhETXdUOEovUUt2c003RlNhZXczTTBpYUEyT1NxOExtM0c4L2NObWJFcWdsdnBYZTJ6Z2pReVVPZEVoNFhFeTIrOVRTSllNZE45UmM5YmsvR2xjQi9BNGlJcERneWVvWFlwdjE3RDl6c2ZST0x3UTMiLCJtYWMiOiJkOTA4MmRkOTA2OGU3MWZiNWM3NTcxMTZjNjFmNjI0MjJhYzU0NDRkNDY2MjRlOWM1ZTNmZjU0NDE4ZWVjMDYxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:30:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlgxQWNMZmFQaUwvd05EbDhmd0VTVFE9PSIsInZhbHVlIjoiTk8zZk5sN3I3K2RPeWZMZ0cxbGk4dWM0dEhEWG5xOUJjTERHUU1nU0UxbWh1cFJDc09EbVA2ZGZkU01GSEdhUkl2bDdMYUN2eTlpL2ZUeDI0NVFVcnJNak51RGd5YnAyWFd5elVpMlVZdjFvd1JDSm5OVzVCL3ZQVzVnQkN1UDUiLCJtYWMiOiIyZWVjMjcyNjU3YTQ3NzA1YjlhOTg3Y2IwM2VmZTkzYjhiY2MyMjZhMmQ0ZjlkMDNkMTE4NmYwMGQ3NWUxODVmIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:30:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1071378377\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-855404066 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/seller/offers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>26</span>\n  \"<span class=sf-dump-key>store_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">bothina</span>\"\n  \"<span class=sf-dump-key>store_image</span>\" => \"<span class=sf-dump-str title=\"26 characters\">stores/1750281403_logo.png</span>\"\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>403</span>\n  \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"528 characters\">{&quot;app_name&quot;:&quot;\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:&quot;media\\/1751134451_logo.png&quot;,&quot;favicon&quot;:null,&quot;storage_type&quot;:&quot;local&quot;,&quot;current_version_of_android_app&quot;:&quot;1.1.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;version_system_status&quot;:0,&quot;expand_product_image&quot;:0,&quot;customer_app_maintenance_status&quot;:1,&quot;offer_request_status&quot;:0,&quot;message_for_customer_app&quot;:&quot;sdsa&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0,&quot;currency_setting&quot;:&quot;\\u062f.\\u0644&quot;}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855404066\", {\"maxDepth\":0})</script>\n"}}