{"__meta": {"id": "X089141b310148abe383a2748339905f5", "datetime": "2025-06-28 18:18:38", "utime": **********.04302, "method": "GET", "uri": "/seller/categories/get_seller_categories?ignore_status=0", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 10, "messages": [{"message": "[18:18:37] LOG.info: category_ids FROM SELLERE [\n    {\n        \"id\": 71,\n        \"parent_id\": null,\n        \"slug\": \"electronics\",\n        \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n        \"row_order\": 0,\n        \"is_active\": true,\n        \"clicks\": 0,\n        \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n        \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n        \"name_ar\": \"\\u0627\\u0644\\u0643\\u062a\\u0631\\u0648\\u0646\\u064a\\u0627\\u062a\",\n        \"name_en\": \"Electronics\",\n        \"description_en\": null,\n        \"description_ar\": null,\n        \"pivot\": {\n            \"store_id\": 26,\n            \"category_id\": 71\n        }\n    },\n    {\n        \"id\": 72,\n        \"parent_id\": null,\n        \"slug\": \"fashion-1\",\n        \"image\": \"categories\\/1750699935_cate.png\",\n        \"row_order\": 0,\n        \"is_active\": true,\n        \"clicks\": 0,\n        \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n        \"updated_at\": \"2025-06-23T17:32:15.000000Z\",\n        \"name_ar\": \"\\u0645\\u0644\\u0627\\u0628\\u0633\",\n        \"name_en\": \"Fashion\",\n        \"description_en\": null,\n        \"description_ar\": null,\n        \"pivot\": {\n            \"store_id\": 26,\n            \"category_id\": 72\n        }\n    },\n    {\n        \"id\": 73,\n        \"parent_id\": null,\n        \"slug\": \"health-beauty-1\",\n        \"image\": \"categories\\/1750700043_cate.png\",\n        \"row_order\": 0,\n        \"is_active\": true,\n        \"clicks\": 0,\n        \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n        \"updated_at\": \"2025-06-23T17:34:03.000000Z\",\n        \"name_ar\": \"\\u0635\\u062d\\u0629 \\u0648\\u062c\\u0645\\u0627\\u0644\",\n        \"name_en\": \"Health & Beauty\",\n        \"description_en\": null,\n        \"description_ar\": null,\n        \"pivot\": {\n            \"store_id\": 26,\n            \"category_id\": 73\n        }\n    }\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.921929, "xdebug_link": null, "collector": "log"}, {"message": "[18:18:37] LOG.info: category : [\n    [\n        {\n            \"id\": 77,\n            \"parent_id\": 71,\n            \"slug\": \"phones\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0647\\u0648\\u0627\\u062a\\u0641\",\n            \"name_en\": \"Phones\",\n            \"description_en\": null,\n            \"description_ar\": null\n        }\n    ]\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.943492, "xdebug_link": null, "collector": "log"}, {"message": "[18:18:37] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.954484, "xdebug_link": null, "collector": "log"}, {"message": "[18:18:37] LOG.info: category : [\n    [\n        {\n            \"id\": 79,\n            \"parent_id\": 72,\n            \"slug\": \"watches\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0633\\u0627\\u0639\\u0627\\u062a\",\n            \"name_en\": \"Watches\",\n            \"description_en\": null,\n            \"description_ar\": null\n        },\n        {\n            \"id\": 80,\n            \"parent_id\": 72,\n            \"slug\": \"jewelry\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0645\\u062c\\u0648\\u0647\\u0631\\u0627\\u062a\",\n            \"name_en\": \"Jewelry\",\n            \"description_en\": null,\n            \"description_ar\": null\n        }\n    ]\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.971161, "xdebug_link": null, "collector": "log"}, {"message": "[18:18:37] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.982121, "xdebug_link": null, "collector": "log"}, {"message": "[18:18:37] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.993086, "xdebug_link": null, "collector": "log"}, {"message": "[18:18:38] LOG.info: category : [\n    [\n        {\n            \"id\": 81,\n            \"parent_id\": 73,\n            \"slug\": \"pharmacy\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0635\\u064a\\u062f\\u0644\\u064a\\u0629\",\n            \"name_en\": \"Pharmacy\",\n            \"description_en\": null,\n            \"description_ar\": null\n        },\n        {\n            \"id\": 82,\n            \"parent_id\": 73,\n            \"slug\": \"personal-care\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0639\\u0646\\u0627\\u064a\\u0629 \\u0634\\u062e\\u0635\\u064a\\u0629\",\n            \"name_en\": \"Personal Care\",\n            \"description_en\": null,\n            \"description_ar\": null\n        },\n        {\n            \"id\": 91,\n            \"parent_id\": 73,\n            \"slug\": \"clinics\",\n            \"image\": \"\\/media\\/hammer-1747776172_2478.png\",\n            \"row_order\": 0,\n            \"is_active\": true,\n            \"clicks\": 0,\n            \"created_at\": \"2025-05-23T12:55:23.000000Z\",\n            \"updated_at\": \"2025-05-25T16:18:27.000000Z\",\n            \"name_ar\": \"\\u0639\\u064a\\u0627\\u062f\\u0627\\u062a\",\n            \"name_en\": \"Clinics\",\n            \"description_en\": null,\n            \"description_ar\": null\n        }\n    ]\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.005859, "xdebug_link": null, "collector": "log"}, {"message": "[18:18:38] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.011052, "xdebug_link": null, "collector": "log"}, {"message": "[18:18:38] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.018514, "xdebug_link": null, "collector": "log"}, {"message": "[18:18:38] LOG.info: category : [\n    []\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.029752, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751134715.792193, "end": **********.043056, "duration": 2.2508630752563477, "duration_str": "2.25s", "measures": [{"label": "Booting", "start": 1751134715.792193, "relative_start": 0, "end": **********.800851, "relative_end": **********.800851, "duration": 2.0086581707000732, "duration_str": "2.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.800895, "relative_start": 2.008702039718628, "end": **********.043058, "relative_end": 1.9073486328125e-06, "duration": 0.24216294288635254, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 29778512, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET seller/categories/get_seller_categories", "middleware": "web, CheckInstallation, auth, role:seller", "controller": "App\\Http\\Controllers\\Seller\\CategoryController@getSellerCategories", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=231\" onclick=\"\">app/Http/Controllers/Seller/CategoryController.php:231-261</a>"}, "queries": {"nb_statements": 25, "nb_visible_statements": 25, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03251999999999999, "accumulated_duration_str": "32.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.859102, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 5.351}, {"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.868715, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 5.351, "width_percent": 3.075}, {"sql": "select * from `users` where `users`.`id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.875897, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 8.426, "width_percent": 2.737}, {"sql": "select * from `roles` where `roles`.`id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.8855689, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 11.162, "width_percent": 2.306}, {"sql": "select `id` from `stores` where `user_id` = 403 limit 1", "type": "query", "params": [], "bindings": [403], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.890909, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:9251", "source": {"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=9251", "ajax": false, "filename": "function_helper.php", "line": "9251"}, "connection": "eshop", "explain": null, "start_percent": 13.469, "width_percent": 3.967}, {"sql": "select * from `stores` where `stores`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 236}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.897792, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:236", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=236", "ajax": false, "filename": "CategoryController.php", "line": "236"}, "connection": "eshop", "explain": null, "start_percent": 17.435, "width_percent": 4.274}, {"sql": "select `categories`.*, `store_categories`.`store_id` as `pivot_store_id`, `store_categories`.`category_id` as `pivot_category_id` from `categories` inner join `store_categories` on `categories`.`id` = `store_categories`.`category_id` where `store_categories`.`store_id` = 26", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 236}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9087431, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:236", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=236", "ajax": false, "filename": "CategoryController.php", "line": "236"}, "connection": "eshop", "explain": null, "start_percent": 21.71, "width_percent": 4.736}, {"sql": "select * from `categories` where `categories`.`id` = 71 limit 1", "type": "query", "params": [], "bindings": [71], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.922531, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 26.445, "width_percent": 5.074}, {"sql": "select * from `categories` where `categories`.`parent_id` in (71)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.930307, "duration": 0.006900000000000001, "duration_str": "6.9ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 31.519, "width_percent": 21.218}, {"sql": "select * from `categories` where `categories`.`id` = 77 limit 1", "type": "query", "params": [], "bindings": [77], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.944046, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 52.737, "width_percent": 3.014}, {"sql": "select * from `categories` where `categories`.`parent_id` in (77)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9495451, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 55.75, "width_percent": 2.46}, {"sql": "select * from `categories` where `categories`.`id` = 72 limit 1", "type": "query", "params": [], "bindings": [72], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9582002, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 58.21, "width_percent": 2.983}, {"sql": "select * from `categories` where `categories`.`parent_id` in (72)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9637852, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 61.193, "width_percent": 3.075}, {"sql": "select * from `categories` where `categories`.`id` = 79 limit 1", "type": "query", "params": [], "bindings": [79], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9717238, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 64.268, "width_percent": 3.444}, {"sql": "select * from `categories` where `categories`.`parent_id` in (79)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.977406, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 67.712, "width_percent": 2.983}, {"sql": "select * from `categories` where `categories`.`id` = 80 limit 1", "type": "query", "params": [], "bindings": [80], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9829502, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 70.695, "width_percent": 5.135}, {"sql": "select * from `categories` where `categories`.`parent_id` in (80)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.988435, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 75.83, "width_percent": 2.798}, {"sql": "select * from `categories` where `categories`.`id` = 73 limit 1", "type": "query", "params": [], "bindings": [73], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9973621, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 78.629, "width_percent": 5.32}, {"sql": "select * from `categories` where `categories`.`parent_id` in (73)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.0021331, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 83.948, "width_percent": 2.091}, {"sql": "select * from `categories` where `categories`.`id` = 81 limit 1", "type": "query", "params": [], "bindings": [81], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0062, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 86.039, "width_percent": 1.476}, {"sql": "select * from `categories` where `categories`.`parent_id` in (81)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.008678, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 87.515, "width_percent": 2.03}, {"sql": "select * from `categories` where `categories`.`id` = 82 limit 1", "type": "query", "params": [], "bindings": [82], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.011788, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 89.545, "width_percent": 2.214}, {"sql": "select * from `categories` where `categories`.`parent_id` in (82)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.014509, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 91.759, "width_percent": 2.399}, {"sql": "select * from `categories` where `categories`.`id` = 91 limit 1", "type": "query", "params": [], "bindings": [91], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.019417, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 94.157, "width_percent": 3.629}, {"sql": "select * from `categories` where `categories`.`parent_id` in (91)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 220}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 248}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0252519, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:214", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Seller/CategoryController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Seller\\CategoryController.php", "line": 214}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FSeller%2FCategoryController.php&line=214", "ajax": false, "filename": "CategoryController.php", "line": "214"}, "connection": "eshop", "explain": null, "start_percent": 97.786, "width_percent": 2.214}]}, "models": {"data": {"App\\Models\\awfarly\\Category": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Store": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\awfarly\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}}, "count": 24, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/seller/offers\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "store_id": "26", "store_name": "<PERSON><PERSON>", "store_image": "stores/1750281403_logo.png", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "403", "system_settings": "{\"app_name\":\"\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a\",\"support_number\":\"919876543210\",\"support_email\":\"<EMAIL>\",\"logo\":\"media\\/1751134451_logo.png\",\"favicon\":null,\"storage_type\":\"local\",\"current_version_of_android_app\":\"1.1.0\",\"current_version_of_ios_app\":\"1.0.0\",\"version_system_status\":0,\"expand_product_image\":0,\"customer_app_maintenance_status\":1,\"offer_request_status\":0,\"message_for_customer_app\":\"sdsa\",\"sidebar_color\":null,\"sidebar_type\":null,\"navbar_fixed\":0,\"theme_mode\":0,\"currency_setting\":\"\\u062f.\\u0644\"}"}, "request": {"path_info": "/seller/categories/get_seller_categories", "status_code": "<pre class=sf-dump id=sf-dump-618466171 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-618466171\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1863171015 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ignore_status</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863171015\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-996903591 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-996903591\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-790260252 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/seller/offers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkcxaFJxek1xaFRnaTE5TE9uMGdlZ2c9PSIsInZhbHVlIjoiSFpYc2pmN1dNeDNHaHhvVlkyVjY1clVwWG9PajZRQSttSDV6Z3EwdFpieHZaVlBjOEF1RGNGS01jenJQZFJQMXF0MXl0TDNuOENhKzRtcG9pbFViajEzZlRhN1FVU0o4djJubHpjNTNnQStLU2p4aWkyVEhKeHp2Wk1SRS82UlEiLCJtYWMiOiIyNzljYjdjYjM1OGFkNTI5NDZiMGY4ZTliYzhlY2ZjMTA4YjE3MzU1NDUxZDRkN2MwNWIxYjE5YzA5YTY5ODg2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkNNV1FzckNvOXA0VEl3UUlxeERNQWc9PSIsInZhbHVlIjoibEE1R25iQkphcjF3R3FPTTF0TTZ4bHJaeHBVMXVQTTlGTDM4TURXOHJKYm1IengzNXAyVHVPSWtLQzUxMzRqaWpmRkJKYlFDa3I3VHM3cXJ4OENwVjdKeFo3S3hXR3NRTG4rTk1oU0g0SGZVLzR6dGJNQy9JRTFYK2NkSDdPVlAiLCJtYWMiOiIwM2JjNzE1Y2VlYWUwMDA4MmQ4N2U5OGRiNDJlNzA5NDk5Y2U2MjI5YmQyZGNmYzViZWQ2ZThjYmE1MmUyZDE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-790260252\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2134744070 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mVUiKCS25MLlribzCWoFa9ZEN0oL6qkA0JoxpOT0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134744070\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1216493815 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 18:18:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlZXa1lXNFNLeWFyUmMvVHBRZ1l3Qnc9PSIsInZhbHVlIjoiRmlSWWdrK2VWQVlkakR0dzRMSVQ5UW0rNzErWkNvMHQ0ZEQ1cHY5OU95ZDJlb01Ic21MMHVHaVluNzM4YlBzc3pnYkhVdHBRQjlTa3E3U0JpTWtMTVF0YTlZeVk2UWtmRHpneFFNQjRzNVphYzdNdGdXNmJHQTRWeHF1eEtiQnUiLCJtYWMiOiI5OTdiNmUxZGU0YzQ3MGJiMTU4NGVlYWMxYmQ5M2FmN2ExYTg3MTdjYjE5MTgwZTA4Y2M3N2JlMDdiYmQwZjRkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:18:38 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlBTazNleDRuRHV2cmZZSTk5VS9VUGc9PSIsInZhbHVlIjoiSE5XVjJBSzNaWUxKUVIvZlRvR3Fud2I2QVdoTnZyMnhtNHlvZGNvcDdKcTB0cGRXOFZKTGxjMkplM0xUMFp5dk0zRnErMThaVFMyR1E1S0dnQ1NZT0xCcmpaVnNTT3NRa2lMb1RERWxRNVFXU25pbElDOFVUNnI5RE8wRkdGS0MiLCJtYWMiOiI4OWI3NzFjZWJhYjIwZTEyZjhlNDdiOWViZGU3YjIxOWIwMWIyNDIxNTI4NjYxMmM4MTU3YzA2ZTQzMTlhODczIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:18:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlZXa1lXNFNLeWFyUmMvVHBRZ1l3Qnc9PSIsInZhbHVlIjoiRmlSWWdrK2VWQVlkakR0dzRMSVQ5UW0rNzErWkNvMHQ0ZEQ1cHY5OU95ZDJlb01Ic21MMHVHaVluNzM4YlBzc3pnYkhVdHBRQjlTa3E3U0JpTWtMTVF0YTlZeVk2UWtmRHpneFFNQjRzNVphYzdNdGdXNmJHQTRWeHF1eEtiQnUiLCJtYWMiOiI5OTdiNmUxZGU0YzQ3MGJiMTU4NGVlYWMxYmQ5M2FmN2ExYTg3MTdjYjE5MTgwZTA4Y2M3N2JlMDdiYmQwZjRkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:18:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlBTazNleDRuRHV2cmZZSTk5VS9VUGc9PSIsInZhbHVlIjoiSE5XVjJBSzNaWUxKUVIvZlRvR3Fud2I2QVdoTnZyMnhtNHlvZGNvcDdKcTB0cGRXOFZKTGxjMkplM0xUMFp5dk0zRnErMThaVFMyR1E1S0dnQ1NZT0xCcmpaVnNTT3NRa2lMb1RERWxRNVFXU25pbElDOFVUNnI5RE8wRkdGS0MiLCJtYWMiOiI4OWI3NzFjZWJhYjIwZTEyZjhlNDdiOWViZGU3YjIxOWIwMWIyNDIxNTI4NjYxMmM4MTU3YzA2ZTQzMTlhODczIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:18:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216493815\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1849685983 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/seller/offers</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>26</span>\n  \"<span class=sf-dump-key>store_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">bothina</span>\"\n  \"<span class=sf-dump-key>store_image</span>\" => \"<span class=sf-dump-str title=\"26 characters\">stores/1750281403_logo.png</span>\"\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>403</span>\n  \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"528 characters\">{&quot;app_name&quot;:&quot;\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:&quot;media\\/1751134451_logo.png&quot;,&quot;favicon&quot;:null,&quot;storage_type&quot;:&quot;local&quot;,&quot;current_version_of_android_app&quot;:&quot;1.1.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;version_system_status&quot;:0,&quot;expand_product_image&quot;:0,&quot;customer_app_maintenance_status&quot;:1,&quot;offer_request_status&quot;:0,&quot;message_for_customer_app&quot;:&quot;sdsa&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0,&quot;currency_setting&quot;:&quot;\\u062f.\\u0644&quot;}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1849685983\", {\"maxDepth\":0})</script>\n"}}