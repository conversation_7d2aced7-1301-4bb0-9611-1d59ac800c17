<?php

namespace App\Models\awfarly;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Offer extends Model
{
    protected $fillable = [
        'title',
        'slug',
        'tags',
        'description',
        'extra_description',
        'short_description',
        'price',
        'discounted_price',
        'offer_views',
        'expire_date',
        'store_id',
        'category_id',
        'is_active',
        'spotlight',
        'is_for_all_branches',
        'address_id',
        'row_order',
        'is_approved',
    ];

    /**
     *  the attributes that should be cast.
     *
     * @return array<string, string>
     */

    protected $casts = [
        'id' => 'integer',
        'price' => 'decimal:2',
        'discounted_price' => 'decimal:2',
        'offer_views' => 'integer',
        'store_id' => 'integer',
        'category_id' => 'integer',
        'address_id' => 'integer',
        'is_active' => 'boolean',
        'spotlight' => 'boolean',
        'is_for_all_branches' => 'boolean',
        'row_order' => 'integer',
        'expire_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }
    public function address()
    {
        return $this->belongsTo(Address::class);
    }
    public function favorites()
    {
        return $this->hasMany(Favorite::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'offer_categories', 'offer_id', 'category_id');
    }

    public function images(): HasMany
    {
        return $this->hasMany(OfferImages::class);
    }

    public function codes(): HasMany
    {
        return $this->hasMany(OfferCode::class);
    }

    public function claims(): HasMany
    {
        return $this->hasMany(OfferClaim::class);
    }

    public function ratings(): HasMany
    {
        return $this->hasMany(Rating::class);
    }

    public function reports(): HasMany
    {
        return $this->hasMany(ReportIssuse::class);
    }

    public function asvertisment()
    {
        return $this->hasMany(Advertisment::class);
    }
    public function storeBranches()
    {
        return $this->belongsToMany(StoreBranch::class, 'branch_offers')->withTimestamps();
    }
}
