<?php

namespace App\Http\Controllers\Admin;

use App\Enums\TransactionStatus;
use App\Enums\UserRole;
use App\Http\Requests\AddStoreRequest;
use App\Models\awfarly\Advertisment;
use App\Models\awfarly\Role;
use App\Models\awfarly\Store;
use App\Models\awfarly\Transaction;
use App\Models\awfarly\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Routing\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class StoreController extends Controller
{
    public function index()
    {
        return view('admin.pages.forms.stores');
    }

    public function manage_store()
    {
        return view('admin.pages.tables.manage_stores');
    }

    /**
     * Store  new store along with its associated user account.
     *
     * This function:
     * - Validates the uniqueness of the store user’s mobile and email
     * - Creates a user with the STORE role
     * - Uploads logo and optional cover image
     * - Fills and saves store details
     * - Wraps the operation in a database transaction for consistency
     *
     * @param AddStoreRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(AddStoreRequest $request)
    {
        try {
            // Get the role ID for users with 'STORE' role
            $roleId = Role::where('name', UserRole::STORE)->value('id');

            // Check for existing user with same mobile under the store role
            if (User::where('role_id', $roleId)->where('mobile', $request->mobile)->exists()) {
                return back()
                    ->withInput()
                    ->withErrors(['error' => __('admin_labels.phone_already_exists')]);
            }

            // Check for existing user with same email under the store role
            if (User::where('role_id', $roleId)->where('email', $request->email)->exists()) {
                return back()->withInput()->withErrors([
                    'error' => __('admin_labels.email_already_exists')
                ]);
            }

            // Use a DB transaction to ensure atomicity of user + store creation
            DB::transaction(function () use ($request, $roleId) {
                // Create store user
                $user = User::create([
                    'username' => $request->username,
                    'email'    => $request->email,
                    'mobile'   => $request->mobile,
                    'password' => bcrypt($request->password),
                    'role_id'  => $roleId,
                ]);

                // Initialize store with user ID and fill other fields
                $store = new Store([
                    'user_id'          => $user->id,
                    'name_ar'          => $request->name_ar,
                    'name_en'          => $request->name_en,
                    'description_ar'   => $request->description_ar,
                    'description_en'   => $request->description_en,
                    'primary_color'    => $request->primary_color ?? '#e46262',
                    'secondary_color'  => $request->secondary_color,
                    'hover_color'      => $request->hover_color,
                    'active_color'     => $request->active_color,
                    'background_color' => $request->background_color,
                    'is_active'        => $request->is_active,
                    'is_approved'      => 1,
                ]);

                // Upload logo image (required)
                if ($request->hasFile('logo')) {
                    $store->logo = upload_image($request->file('logo'), 'stores', 'logo');
                }

                // Upload cover image (optional)
                if ($request->hasFile('cover')) {
                    $store->cover = upload_image($request->file('cover'), 'stores', 'cover');
                }

                // Save store
                $store->save();
            });

            // Redirect to store list with success message
            return redirect()
                ->route('admin.stores.manage_store')
                ->with('success', __('admin_labels.store_created_successfully'));
        } catch (\Exception $e) {
            Log::error('Store Creation Error: ' . $e->getMessage());

            return back()
                ->withInput()
                ->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }


    /**
     * Retrieve and return a filtered, sorted, and paginated list of stores.
     *
     * This method supports:
     * - Searching by store name (Arabic or English)
     * - Filtering by active status
     * - Paginating the results
     * 
     * The response includes:
     * - Store ID, name (based on locale), logo, and banner (with image preview)
     * - Status dropdown for toggling active state
     * - Edit button link
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request)
    {
        // Extract request parameters
        $search  = trim($request->input('search'));
        $sort    = $request->input('sort', 'id');
        $order   = $request->input('order', 'DESC');
        $offset  = $search || request('pagination_offset') ? request('pagination_offset') : 0;
        $limit   = $request->input('limit', 10);
        $status  = $request->input('status', '');
        $locale  = app()->getLocale();

        // Build query with optional filters
        $query = Store::query()
            ->where('is_approved', 1)
            ->when($search, function ($query) use ($search) {
                // Search by both Arabic and English names
                $query->where(function ($q) use ($search) {
                    $q->where('name_ar', 'like', "%{$search}%")
                        ->orWhere('name_en', 'like', "%{$search}%");
                });
            })
            ->when($status !== '', function ($query) use ($status) {
                // Filter by is_active status
                $query->where('is_active', $status);
            });

        // Count total records before applying pagination
        $total = (clone $query)->count();

        // Fetch paginated and sorted store records
        $stores = $query->orderBy($sort, $order)
            ->skip($offset)
            ->take($limit)
            ->get();

        // Format each store's data for the response
        $data = $stores->map(function ($store) use ($locale) {
            $editUrl   = route('admin.store.update', $store->id);
            $imageUrl  = getMediaImageUrl($store->logo, 'STORE_IMG_PATH');
            $bannerUrl = getMediaImageUrl($store->cover, 'STORE_IMG_PATH');

            return [
                'id'    => $store->id,
                'name'  => $store->{"name_{$locale}"},

                // Logo image preview with lightbox
                'image' => view('components.image-lightbox', [
                    'id'        => "image-{$store->id}",
                    'url'       => $imageUrl,
                    'thumbnail' => $imageUrl,
                    'width'     => 90
                ])->render(),

                // Banner image preview with lightbox
                'banner' => view('components.image-lightbox', [
                    'id'        => "banner-{$store->id}",
                    'url'       => $bannerUrl,
                    'thumbnail' => $bannerUrl,
                    'width'     => 90
                ])->render(),

                // Dropdown to toggle active/inactive status
                'status' => view('components.status-dropdown', [
                    'id'            => $store->id,
                    'url'           => "/admin/store/update_status/{$store->id}",
                    'isActive'      => $store->is_active,
                    'activeClass'   => 'active_status',
                    'inactiveClass' => 'inactive_status'
                ])->render(),

                'last_login' => $store->user->last_login ?? '-',

                // Action buttons 
                'operate' => view('components.action-button', [
                    'editUrl'   => $editUrl,
                    'editClass' => 'text-dark single_action_button',
                    'editIcon'  => 'bx bx-pencil mx-2'
                ])->render()
            ];
        });

        // Return JSON response
        return response()->json([
            'rows'  => $data,
            'total' => $total,
        ]);
    }

    /**
     * Toggle the active status of a specific store.
     *
     * This method switches the `is_active` flag:
     * - If currently active (1), it becomes inactive (0)
     * - If currently inactive (0), it becomes active (1)
     *
     * Returns a JSON response indicating success or failure.
     *
     * @param int $id Store ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function update_status($id)
    {
        // Attempt to find the store or fail with 404
        $store = Store::findOrFail($id);

        try {
            // Toggle the is_active status
            $store->is_active = $store->is_active == 1 ? 0 : 1;

            // Save the updated status
            $store->save();

            return response()->json([
                'success' => labels('admin_labels.status_updated_successfully', 'Status updated successfully.')
            ]);
        } catch (\Exception $e) {
            // Handle and return any exception that occurs
            return response()->json([
                'error' => labels('admin_labels.something_went_wrong', 'Something went wrong')
            ]);
        }
    }

    /**
     * Display the store edit form.
     *
     * Retrieves a store by ID and returns the edit view with the store data.
     *
     * @param int $id The ID of the store to edit
     * @return \Illuminate\View\View Returns the store edit view with store data
     */
    public function edit($id)
    {
        // Retrieve the store record by ID
        $store = Store::find($id);

        return view('admin.pages.forms.update_store', [
            'store' => $store
        ]);
    }

    /**
     * Update the specified store in storage.
     *
     * Handles the store update request, including user account updates and file uploads for logo and cover image.
     * Validates input using the AddStoreRequest form request class.
     *
     * @param AddStoreRequest $request Validated form request data
     * @param int $id The ID of the store to update
     * @return \Illuminate\Http\RedirectResponse Redirects with success/error message
     */
    public function update(AddStoreRequest $request, $id)
    {
        $store = Store::findOrFail($id);

        try {
            DB::transaction(function () use ($request, $store) {
                // === Update related user ===
                $user = User::find($store->user_id);
                $user->username = $request->username ?? '';
                $user->email    = $request->email;
                $user->mobile   = $request->mobile;

                // Only update password if provided
                if ($request->filled('password')) {
                    $user->password = bcrypt($request->password);
                }

                $user->save();

                // === Handle logo ===
                if ($request->hasFile('logo')) {
                    if ($store->logo && Storage::disk('public')->exists($store->logo)) {
                        Storage::disk('public')->delete($store->logo);
                    }
                    $store->logo = upload_image($request->logo, 'stores', 'logo');
                }

                // === Handle cover ===
                if ($request->hasFile('cover')) {
                    if ($store->cover && Storage::disk('public')->exists($store->cover)) {
                        Storage::disk('public')->delete($store->cover);
                    }
                    $store->cover = upload_image($request->cover, 'stores', 'cover');
                }

                // === Update store data ===
                $store->fill([
                    'name_ar'          => $request->name_ar,
                    'name_en'          => $request->name_en,
                    'description_ar'   => $request->description_ar,
                    'description_en'   => $request->description_en,
                    'primary_color'    => $request->primary_color,
                    'secondary_color'  => $request->secondary_color,
                    'hover_color'      => $request->hover_color,
                    'active_color'     => $request->active_color,
                    'background_color' => $request->background_color,
                    'is_active'        => $request->is_active,
                ]);

                $store->save();
            });

            return redirect()
                ->route('admin.stores.manage_store')
                ->with('success', __('admin_labels.store_updated_successfully'));
        } catch (\Exception $e) {
            Log::error("Store update failed: " . $e->getMessage());

            return back()
                ->withInput()
                ->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }
    public function requested_stores()
    {
        return view('admin.pages.tables.manage_requested_stores');
    }

    public function requested_stores_list(Request $request)
    {
        try {
            Log::info("ff");
            // Extract request parameters
            $offset  = request('pagination_offset') ? request('pagination_offset') : 0;
            $limit   = $request->input('limit', 10);
            $locale  = app()->getLocale();
            // Build query with optional filters
            $query = Store::query()->where('is_approved', 0);

            // Count total records before applying pagination
            $total = (clone $query)->count();

            // Fetch paginated and sorted store records
            $stores = $query
                ->skip($offset)
                ->take($limit)
                ->get();

            // Format each store's data for the response
            $data = $stores->map(function ($store) use ($locale) {
                $editUrl   = route('admin.stores.requested_stores_edit', $store->id);
                $imageUrl  = getMediaImageUrl($store->logo, 'STORE_IMG_PATH');
                $bannerUrl = getMediaImageUrl($store->cover, 'STORE_IMG_PATH');

                return [
                    'id'    => $store->id,
                    'name'  => $store->{"name_{$locale}"},
                    'status' => __('admin_labels.pending'),
                    'created_at' => $store->created_at->format('d-m-Y H:i:s'),
                    // Action buttons 
                    'operate' => view('components.action-button', [
                        'editUrl'   => $editUrl,
                        'editClass' => 'text-dark single_action_button',
                        'editIcon'  => 'bx bx-pencil mx-2'
                    ])->render()
                ];
            });

            // Return JSON response
            return response()->json([
                'rows'  => $data,
                'total' => $total,
            ]);
        } catch (\Exception $e) {
            Log::info('Error: ' . $e->getMessage());
            return response()->json([
                'rows'  => [],
                'total' => 0,
            ]);
        }
    }

    public function requested_stores_edit($id)
    {
        $store = Store::find($id);

        return view('admin.pages.forms.update_requested_store', [
            'store' => $store
        ]);
    }

    /**
     * Approve a store by updating its approval status.
     *
     * @param int $id Store ID
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approve($id)
    {
        try {
            // Mark the store as approved
            Store::where('id', $id)->update(['is_approved' => 1]);

            // Redirect back to the manage requests page with success message
            return redirect()
                ->route('admin.stores.manage_requested_stores')
                ->with('success', __('admin_labels.store_approve_successfully'));
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Store Approval Error: ' . $e->getMessage());

            // Return back with error message
            return back()
                ->withInput()
                ->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }

    /**
     * Reject a store request by deleting the associated user and store.
     * A rejection reason may be recorded here or used for notification if needed.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id Store ID
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reject(Request $request, $id)
    {
        try {
            // Retrieve the store record
            $store = Store::findOrFail($id);

            // Delete the user who submitted the store request
            User::where('id', $store->user_id)->delete();

            // TODO: Optional - Send rejection WhatsApp message or email
            // TODO: Optional - Save rejection_reason if needed

            // Redirect back to manage requests with success message
            return redirect()
                ->route('admin.stores.manage_requested_stores')
                ->with('success', __('admin_labels.store_rejected_successfully'));
        } catch (\Exception $e) {
            // Log error for debugging
            Log::error('Store Rejection Error: ' . $e->getMessage());

            // Return back with error message
            return back()
                ->withInput()
                ->withErrors(['error' => __('admin_labels.something_went_wrong')]);
        }
    }


    public function storeTransactions()
    {
        $total = Transaction::count();
        $completed = Transaction::where('status', TransactionStatus::COMPLETED)->count();
        $failed = Transaction::where('status', TransactionStatus::FAILED)->count();
        $pending = Transaction::where('status', TransactionStatus::PENDING)->count();
        $revenue = Transaction::where('status', TransactionStatus::COMPLETED)->sum('amount');
        return view('admin.pages.tables.store_transactions', compact('total', 'completed', 'failed', 'pending', 'revenue'));
    }
    ///////////////////
    public function transactionsList($user_id = '', $role_id = 2)
    {
        $search = trim(request()->input('search')) ?? '';
        $offset = $search || (request('pagination_offset')) ? (request('pagination_offset')) : 0;
        $limit = request()->input('limit', 10);
        $sort = request()->input('sort', 'id');
        $order = request()->input('order', 'ASC');

        $transactionsQuery = Transaction::with(['user']);


        if (request()->has('search') && trim(request()->input('search')) !== '') {
            $transactionsQuery->where(function ($query) use ($search) {
                $query->where('id', 'LIKE', "%{$search}%")
                    ->orWhere('amount', 'LIKE', "%{$search}%")
                    ->orWhere('created_at', 'LIKE', "%{$search}%")
                    ->orWhere('users.username', 'LIKE', "%{$search}%")
                    ->orWhere('users.mobile', 'LIKE', "%{$search}%")
                    ->orWhere('users.email', 'LIKE', "%{$search}%")
                    ->orWhere('type', 'LIKE', "%{$search}%")
                    ->orWhere('status', 'LIKE', "%{$search}%")
                    ->orWhere('external_ref', 'LIKE', "%{$search}%");
            });
        }

        if (request()->filled('start_date') && request()->filled('end_date')) {
            $transactionsQuery->whereDate('transactions.created_at', '>=', request()->input('start_date'))
                ->whereDate('transactions.created_at', '<=', request()->input('end_date'));
        }

        $totalQuery = clone $transactionsQuery;
        $total = $totalQuery->count();
        $txn_search_res = $transactionsQuery
            ->orderBy($sort, $order)
            ->skip($offset)
            ->take($limit)
            ->get();

        $formattedTransactions = $txn_search_res->map(function ($row) {
            $show_url = route('admin.store.transaction_show', $row->id);

            $action = '<div class="dropdown bootstrap-table-dropdown" dir="ltr">
                         <a class="dropdown-item" href="' . $show_url . '"><i class="bx bxs-show"></i> ' . __("admin_labels.view") . '</a>
                        </div>';
            $status = match ($row->status) {
                TransactionStatus::PENDING => '<span class="badge bg-info">' . __('admin_labels.pending') . '</span>',
                TransactionStatus::PAID => '<span class="badge bg-success">' . __('admin_labels.paid') . '</span>',
                TransactionStatus::COMPLETED => '<span class="badge bg-success">' . __('admin_labels.completed') . '</span>',
                TransactionStatus::FAILED => '<span class="badge bg-danger">' . __('admin_labels.failed') . '</span>',
                TransactionStatus::CANCELED => '<span class="badge bg-danger">' . __('admin_labels.canceled') . '</span>',
            };
            return [
                'id' => $row->id,
                'name' => $row->user->username,
                'type' => $row->type == 'ads' ? __('admin_labels.advertisments') : $row->type,
                'amount' => $row->amount,
                'status' => $status,
                'currency' => $row->currency,
                'external_ref' => $row->external_ref,
                'created_at' => Carbon::parse($row->created_at)->format('d-m-Y'),
                'operate' => $action,
            ];
        });

        return response()->json(['total' => $total, 'rows' => $formattedTransactions]);
    }
    public function show($id)
    {
        $transaction = Transaction::with('user')->findOrFail($id);
        
    // Load related ad if type is 'ads'
    $ad = null;
    if ($transaction->type === 'ads' && $transaction->related_id) {
        $ad = Advertisment::with('package')->find($transaction->related_id);
    }
Log::info('ad',[$ad]);
        return view('admin.pages.views.transaction', compact('transaction','ad'));
    }
    ///////////////////////////////////////////////////////////////////
    public function get_stores_list(Request $request)
    {
        $store_id = getStoreId();

        $search = trim($request->input('search'));
        $limit = (int) $request->input('limit', 10);

        $stores = Store::where('name', 'like', '%' . $search . '%')
            ->where('id', '<>', $store_id)
            ->limit($limit)
            ->get(['id', 'name']);

        $totalCount = Store::where('name', 'like', '%' . $search . '%')->where('id', '<>', $store_id)->count();

        $response = [
            'total' => $totalCount,
            'results' => $stores->map(function ($store) {
                return [
                    'id' => $store->id,
                    'text' => $store->name,
                ];
            }),
        ];

        return response()->json($response);
    }




    public function getStores($limit = null, $offset = null, $sort = 'id', $order = 'DESC', $search = null, $from_app = false)
    {
        $query = DB::table('stores as s')->select('*');
        if ($from_app != true) {
            $query->where('s.status', 1);
        }
        if ($search) {
            $query->where(function ($query) use ($search) {
                $query->orWhere('s.id', 'LIKE', "%{$search}%")
                    ->orWhere('s.name', 'LIKE', "%{$search}%");
            });
        }

        $total = $query->count();

        $stores = $query
            ->orderBy($sort, $order)
            ->when($limit, function ($query, $limit) use ($offset) {
                return $query->skip($offset)->take($limit);
            })
            ->get();

        $bulkData = [
            'error' => $stores->isEmpty(),
            'message' => $stores->isEmpty() ? labels('admin_labels.store_not_exist', 'Store(s) does not exist')
                :
                labels('admin_labels.store_retrieved_successfully', 'Store(s) retrieved successfully'),
            'total' => $total,
            'data' => $stores->map(function ($row) {
                return [

                    'id' => $row->id ?? "",
                    'name' => $row->name ?? "",
                    'description' => $row->description ?? "",
                    'is_single_seller_order_system' => $row->is_single_seller_order_system ?? "",
                    '' =>  "",
                    'note_for_necessary_documents' => $row->note_for_necessary_documents ?? "",
                    'primary_color' => $row->primary_color ?? "",
                    'secondary_color' => $row->secondary_color ?? "",
                    'active_color' => $row->active_color ?? "",
                    'hover_color' => $row->hover_color ?? "",
                    'background_color' => $row->background_color ?? "",
                    'store_settings' => json_decode($row->store_settings) ?? "",
                    'image' => getMediaImageUrl($row->image, 'STORE_IMG_PATH'),
                    'banner_image' => getMediaImageUrl($row->banner_image, 'STORE_IMG_PATH'),
                    'banner_image_for_most_selling_product' => getMediaImageUrl($row->banner_image_for_most_selling_product, 'STORE_IMG_PATH'),
                    'stack_image' => getMediaImageUrl($row->stack_image, 'STORE_IMG_PATH'),
                    'login_image' => getMediaImageUrl($row->login_image, 'STORE_IMG_PATH'),
                    'status' => $row->status ?? "",
                    'delivery_charge_type' => $row->delivery_charge_type ?? "",
                    'delivery_charge_amount' => $row->delivery_charge_amount ?? 0,
                    'minimum_free_delivery_amount' => $row->minimum_free_delivery_amount ?? 0,
                    'product_deliverability_type' => $row->product_deliverability_type ?? "",
                ];
            }),
        ];

        return $bulkData;
    }
}
