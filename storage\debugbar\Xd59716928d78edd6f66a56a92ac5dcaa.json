{"__meta": {"id": "Xd59716928d78edd6f66a56a92ac5dcaa", "datetime": "2025-06-28 12:35:42", "utime": **********.766486, "method": "GET", "uri": "/admin/offers/requested/36", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[12:35:42] LOG.info: o [\n    false\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.239599, "xdebug_link": null, "collector": "log"}, {"message": "[12:35:42] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.413213, "xdebug_link": null, "collector": "log"}, {"message": "[12:35:42] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.614093, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751114138.360211, "end": **********.766565, "duration": 4.406354188919067, "duration_str": "4.41s", "measures": [{"label": "Booting", "start": 1751114138.360211, "relative_start": 0, "end": 1751114141.877564, "relative_end": 1751114141.877564, "duration": 3.517353057861328, "duration_str": "3.52s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1751114141.877615, "relative_start": 3.517404079437256, "end": **********.766571, "relative_end": 5.9604644775390625e-06, "duration": 0.8889560699462891, "duration_str": "889ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 33115120, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "admin.pages.views.requested_offers", "param_count": null, "params": [], "start": **********.313178, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/admin/pages/views/requested_offers.blade.phpadmin.pages.views.requested_offers", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fadmin%2Fpages%2Fviews%2Frequested_offers.blade.php&line=1", "ajax": false, "filename": "requested_offers.blade.php", "line": "?"}}, {"name": "admin.layout", "param_count": null, "params": [], "start": **********.37511, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/admin/layout.blade.phpadmin.layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fadmin%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}, {"name": "admin.include_css", "param_count": null, "params": [], "start": **********.411679, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/admin/include_css.blade.phpadmin.include_css", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fadmin%2Finclude_css.blade.php&line=1", "ajax": false, "filename": "include_css.blade.php", "line": "?"}}, {"name": "components.admin.side-bar", "param_count": null, "params": [], "start": **********.462989, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/side-bar.blade.phpcomponents.admin.side-bar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Fside-bar.blade.php&line=1", "ajax": false, "filename": "side-bar.blade.php", "line": "?"}}, {"name": "components.admin.header", "param_count": null, "params": [], "start": **********.569948, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/header.blade.phpcomponents.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "components.admin.footer", "param_count": null, "params": [], "start": **********.672368, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/footer.blade.phpcomponents.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "admin.include_script", "param_count": null, "params": [], "start": **********.750948, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/admin/include_script.blade.phpadmin.include_script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fadmin%2Finclude_script.blade.php&line=1", "ajax": false, "filename": "include_script.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/offers/requested/{id}", "middleware": "web, CheckInstallation, auth, role:super_admin,admin,editor, CheckStoreNotEmpty", "controller": "App\\Http\\Controllers\\Admin\\OfferController@showRequestedOffer", "namespace": null, "prefix": "", "where": [], "as": "admin.requested_offers.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOfferController.php&line=361\" onclick=\"\">app/Http/Controllers/Admin/OfferController.php:361-374</a>"}, "queries": {"nb_statements": 31, "nb_visible_statements": 31, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07003, "accumulated_duration_str": "70.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.070642, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 4.941}, {"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.097944, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 4.941, "width_percent": 3.656}, {"sql": "select * from `users` where `users`.`id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.111914, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 17, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 8.596, "width_percent": 2.499}, {"sql": "select * from `roles` where `roles`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/LogoutMiddleware.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\LogoutMiddleware.php", "line": 10}], "start": **********.128015, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "role:34", "source": {"index": 22, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=34", "ajax": false, "filename": "RoleMiddleware.php", "line": "34"}, "connection": "eshop", "explain": null, "start_percent": 11.095, "width_percent": 2.228}, {"sql": "select count(*) as aggregate from `stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "CheckStoreNotEmpty", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckStoreNotEmpty.php", "line": 19}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "CheckInstallation", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckInstallation.php", "line": 23}], "start": **********.13743, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "CheckStoreNotEmpty:19", "source": {"index": 19, "namespace": "middleware", "name": "CheckStoreNotEmpty", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\CheckStoreNotEmpty.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FCheckStoreNotEmpty.php&line=19", "ajax": false, "filename": "CheckStoreNotEmpty.php", "line": "19"}, "connection": "eshop", "explain": null, "start_percent": 13.323, "width_percent": 1.514}, {"sql": "select `id`, `store_id`, `title_ar`, `title_en`, `description_ar`, `description_en`, `price`, `discounted_price`, `created_at`, `expire_date`, `is_approved` from `offers` where `offers`.`id` = '36' limit 1", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OfferController.php", "line": 369}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1480649, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:369", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Admin/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OfferController.php", "line": 369}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOfferController.php&line=369", "ajax": false, "filename": "OfferController.php", "line": "369"}, "connection": "eshop", "explain": null, "start_percent": 14.836, "width_percent": 2.613}, {"sql": "select `id`, `name_ar` as `name`, `logo` from `stores` where `stores`.`id` in (26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OfferController.php", "line": 369}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.159165, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:369", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OfferController.php", "line": 369}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOfferController.php&line=369", "ajax": false, "filename": "OfferController.php", "line": "369"}, "connection": "eshop", "explain": null, "start_percent": 17.45, "width_percent": 2.256}, {"sql": "select `categories`.*, `offer_categories`.`offer_id` as `pivot_offer_id`, `offer_categories`.`category_id` as `pivot_category_id` from `categories` inner join `offer_categories` on `categories`.`id` = `offer_categories`.`category_id` where `offer_categories`.`offer_id` in (36)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OfferController.php", "line": 369}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.179011, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:369", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OfferController.php", "line": 369}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOfferController.php&line=369", "ajax": false, "filename": "OfferController.php", "line": "369"}, "connection": "eshop", "explain": null, "start_percent": 19.706, "width_percent": 3.227}, {"sql": "select * from `offer_images` where `offer_images`.`offer_id` in (36)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OfferController.php", "line": 369}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.199445, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:369", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Admin/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OfferController.php", "line": 369}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOfferController.php&line=369", "ajax": false, "filename": "OfferController.php", "line": "369"}, "connection": "eshop", "explain": null, "start_percent": 22.933, "width_percent": 2.799}, {"sql": "select exists(select * from `offer_codes` where `offer_id` = '36') as `exists`", "type": "query", "params": [], "bindings": ["36"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/Admin/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OfferController.php", "line": 370}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.210635, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "OfferController.php:370", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/Admin/OfferController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\OfferController.php", "line": 370}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FOfferController.php&line=370", "ajax": false, "filename": "OfferController.php", "line": "370"}, "connection": "eshop", "explain": null, "start_percent": 25.732, "width_percent": 2.313}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 402 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [402, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.259658, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:38", "source": {"index": 20, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=38", "ajax": false, "filename": "AppServiceProvider.php", "line": "38"}, "connection": "eshop", "explain": null, "start_percent": 28.045, "width_percent": 2.399}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.2687478, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:39", "source": {"index": 21, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=39", "ajax": false, "filename": "AppServiceProvider.php", "line": "39"}, "connection": "eshop", "explain": null, "start_percent": 30.444, "width_percent": 2.085}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.277811, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 32.529, "width_percent": 5.312}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.339619, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 37.841, "width_percent": 4.855}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.377943, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 42.696, "width_percent": 5.655}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.4213328, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 48.351, "width_percent": 5.583}, {"sql": "select * from `settings` where `variable` = 'system_settings' limit 1", "type": "query", "params": [], "bindings": ["system_settings"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.467337, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:609", "source": {"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=609", "ajax": false, "filename": "function_helper.php", "line": "609"}, "connection": "eshop", "explain": null, "start_percent": 53.934, "width_percent": 2.827}, {"sql": "select * from `settings` where `variable` = 'sms_gateway_settings' limit 1", "type": "query", "params": [], "bindings": ["sms_gateway_settings"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.478352, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:609", "source": {"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 609}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=609", "ajax": false, "filename": "function_helper.php", "line": "609"}, "connection": "eshop", "explain": null, "start_percent": 56.761, "width_percent": 3.399}, {"sql": "select count(*) as aggregate from `ch_messages` where `seen` = 0 and `from_id` != 402 and `to_id` = 402", "type": "query", "params": [], "bindings": [0, 402, 402], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/ChatifyMessenger.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\ChatifyMessenger.php", "line": 269}, {"index": 17, "namespace": "view", "name": "components.admin.side-bar", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/side-bar.blade.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.517076, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "ChatifyMessenger.php:269", "source": {"index": 16, "namespace": null, "name": "app/ChatifyMessenger.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\ChatifyMessenger.php", "line": 269}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FChatifyMessenger.php&line=269", "ajax": false, "filename": "ChatifyMessenger.php", "line": "269"}, "connection": "eshop", "explain": null, "start_percent": 60.16, "width_percent": 2.356}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.5342062, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 62.516, "width_percent": 5.398}, {"sql": "select * from `stores` where (`is_active` = 1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.573416, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:964", "source": {"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=964", "ajax": false, "filename": "function_helper.php", "line": "964"}, "connection": "eshop", "explain": null, "start_percent": 67.914, "width_percent": 2.413}, {"sql": "select * from `stores` where `is_active` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "components.admin.header", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/header.blade.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.583162, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "components.admin.header:25", "source": {"index": 15, "namespace": "view", "name": "components.admin.header", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/header.blade.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Fheader.blade.php&line=25", "ajax": false, "filename": "header.blade.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 70.327, "width_percent": 2.27}, {"sql": "select * from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "components.admin.header", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/header.blade.php", "line": 64}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5952468, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "components.admin.header:64", "source": {"index": 16, "namespace": "view", "name": "components.admin.header", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/header.blade.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Fheader.blade.php&line=64", "ajax": false, "filename": "header.blade.php", "line": "64"}, "connection": "eshop", "explain": null, "start_percent": 72.597, "width_percent": 2.128}, {"sql": "select `language` from `languages` where (`code` is null)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.6043541, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:964", "source": {"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=964", "ajax": false, "filename": "function_helper.php", "line": "964"}, "connection": "eshop", "explain": null, "start_percent": 74.725, "width_percent": 2.656}, {"sql": "select `id` from `stores` where `user_id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.615513, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:9251", "source": {"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=9251", "ajax": false, "filename": "function_helper.php", "line": "9251"}, "connection": "eshop", "explain": null, "start_percent": 77.381, "width_percent": 2.699}, {"sql": "select `primary_color`, `secondary_color`, `hover_color`, `active_color` from `stores` where (`id` is null)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.625293, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:964", "source": {"index": 13, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 964}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=964", "ajax": false, "filename": "function_helper.php", "line": "964"}, "connection": "eshop", "explain": null, "start_percent": 80.08, "width_percent": 1.599}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.636699, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 81.679, "width_percent": 5.341}, {"sql": "select `id` from `stores` where `user_id` = 402 limit 1", "type": "query", "params": [], "bindings": [402], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.675393, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:9251", "source": {"index": 17, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 9251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=9251", "ajax": false, "filename": "function_helper.php", "line": "9251"}, "connection": "eshop", "explain": null, "start_percent": 87.02, "width_percent": 1.885}, {"sql": "select `seller_store`.*, `seller_data`.*, `users`.`username` from `seller_store` left join `seller_data` on `seller_data`.`id` = `seller_store`.`seller_id` left join `users` on `users`.`id` = `seller_store`.`user_id` where `seller_store`.`store_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": "view", "name": "components.admin.footer", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/footer.blade.php", "line": 179}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.686731, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "components.admin.footer:179", "source": {"index": 13, "namespace": "view", "name": "components.admin.footer", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/components/admin/footer.blade.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fcomponents%2Fadmin%2Ffooter.blade.php&line=179", "ajax": false, "filename": "footer.blade.php", "line": "179"}, "connection": "eshop", "explain": null, "start_percent": 88.905, "width_percent": 2.642}, {"sql": "select * from `updates` order by `id` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 10573}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.6986709, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "function_helper.php:10573", "source": {"index": 16, "namespace": null, "name": "app/function_helper.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\function_helper.php", "line": 10573}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2Ffunction_helper.php&line=10573", "ajax": false, "filename": "function_helper.php", "line": "10573"}, "connection": "eshop", "explain": null, "start_percent": 91.546, "width_percent": 3.627}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 159}], "start": **********.712784, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:41", "source": {"index": 16, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Providers\\AppServiceProvider.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FProviders%2FAppServiceProvider.php&line=41", "ajax": false, "filename": "AppServiceProvider.php", "line": "41"}, "connection": "eshop", "explain": null, "start_percent": 95.173, "width_percent": 4.827}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 1939, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Store": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Setting": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\awfarly\\Offer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FOffer.php&line=1", "ajax": false, "filename": "Offer.php", "line": "?"}}, "App\\Models\\awfarly\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\awfarly\\Category": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\awfarly\\OfferImages": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FOfferImages.php&line=1", "ajax": false, "filename": "OfferImages.php", "line": "?"}}, "App\\Models\\Updates": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUpdates.php&line=1", "ajax": false, "filename": "Updates.php", "line": "?"}}}, "count": 1961, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "HIFIBSOF75z10mVeKEpiyurHuBRr9opJ79mlwkSS", "store_id": "20", "store_name": "null", "store_image": "null", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/offers/requested/36\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "402", "system_settings": "{\"app_name\":\"\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a\",\"support_number\":\"919876543210\",\"support_email\":\"<EMAIL>\",\"logo\":null,\"favicon\":null,\"storage_type\":\"local\",\"current_version_of_android_app\":\"1.1.0\",\"current_version_of_ios_app\":\"1.0.0\",\"version_system_status\":0,\"expand_product_image\":0,\"customer_app_maintenance_status\":1,\"offer_request_status\":0,\"message_for_customer_app\":\"sdsa\",\"sidebar_color\":null,\"sidebar_type\":null,\"navbar_fixed\":0,\"theme_mode\":0,\"currency_setting\":\"\\u062f.\\u0644\"}"}, "request": {"path_info": "/admin/offers/requested/36", "status_code": "<pre class=sf-dump id=sf-dump-2102660315 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2102660315\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-564425514 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-564425514\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1363197513 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1363197513\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-308095734 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/admin/offers/requested/36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImY2b01HV1pEMHA5Uk5Db2ZXdnVndGc9PSIsInZhbHVlIjoibWNLTW4wMDVLZ0U1cGFQSUVmSzZqQ1BnbTFTSndLeGxRSUN1VXJkK3R6OGFCQlJuQUtqallIcVVWM2pkWHdpdk5GM1A4Vyt2K25HcDNLdWFQNXlVaVVLUzJzQWpxYnhQK21sbDZWRkJxczZNU0ZJWWVPTTQ1SStaLzVOT0RISEMiLCJtYWMiOiI1OGVlOTY4YTc0ZmQ3Mjc3ODIxYjRhOGQ3MGJlY2Q4NmQxY2ExMDY4NmJkM2E2YTUyYjU4MmQ0MzhjZmJkOWU2IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVwV3FNRUxmTFlBUnUxUTg0RTBXK0E9PSIsInZhbHVlIjoiWTdhUXRUeCtXWkpUN2J2b3c1bnQ4MUkrRDhNRytsclJIU1dqSG0rMzJ1MFcwV1psTkhzWDdEM0NVNVVpWmR0c3RNUHc0V2dOWWhxLzNRSG8waDBaRFdmMVYvNVhVZE9Tc0lhSUFleE03MDBwTDJDUm9Yb2hua3NNeERJd2VXVy8iLCJtYWMiOiJlZmQ5MDkxYmQwYjUxZjZlNWEzZjdiZmUwYzZjY2Y3MWFiNjZjMzg1ZTNmN2I4YTBkMWIwOGMzOTlkMGY5MjVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-308095734\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-702969756 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HIFIBSOF75z10mVeKEpiyurHuBRr9opJ79mlwkSS</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kG491Qos0uYHdgKgtGYFpA0pwkJgFWuoBTczgOOd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702969756\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-902193645 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 12:35:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImVkTUhGYUdhVDZoNy9OSXY5aHk1TlE9PSIsInZhbHVlIjoidW11VWZMbGJ6OE51QzZkdytyMklKNlQvRm1HV0tGUUdid3IrUi9QcStQWXJCcStlN2lWR3NnTUh4ejdjNFZnQ1JTa3IzMExCZEloaFQzZlViZi9jWUtydTRYS1IvdGtCcm10bXplWjJmcjV1NEN5N1FkejlHUVF6MzYzbXFveDIiLCJtYWMiOiIxYmY3ZDUxY2MwMDJjYzliMTZjZTMwNzhiYjM0MmNmNzZjMGNmYzVjOTkyZjE1NTkzZjMxODExODM0NDY1ZGYxIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 14:35:42 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjA3b1hvUDE1dFJzWlk2S3JVRVgzdFE9PSIsInZhbHVlIjoidUZydGYwR1Zrd0ZJUXV1bjJCbjZwUGV0Wm9PQVpsVWV2cjNhM3hWVWtwZGk4ZDMzeVkyK2ZWSDExZVZSS3pMN3V6c3dxMURSVExUZG0wbVkyUEM5WHA0YUFHdCs1RzRBdzFrR2tGaktlT3RwdjVHYmk0TzY2ZjdaSFpmSjNSZ3MiLCJtYWMiOiJlMWUwYzk4Mzg4MDQyMGNiYzYwMDg3ODkyNThjN2UyOWRhZTM4M2E5ZDY3NzQ3NDMxMjZhNmNiMGYxNzJiMTk1IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 14:35:42 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImVkTUhGYUdhVDZoNy9OSXY5aHk1TlE9PSIsInZhbHVlIjoidW11VWZMbGJ6OE51QzZkdytyMklKNlQvRm1HV0tGUUdid3IrUi9QcStQWXJCcStlN2lWR3NnTUh4ejdjNFZnQ1JTa3IzMExCZEloaFQzZlViZi9jWUtydTRYS1IvdGtCcm10bXplWjJmcjV1NEN5N1FkejlHUVF6MzYzbXFveDIiLCJtYWMiOiIxYmY3ZDUxY2MwMDJjYzliMTZjZTMwNzhiYjM0MmNmNzZjMGNmYzVjOTkyZjE1NTkzZjMxODExODM0NDY1ZGYxIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 14:35:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjA3b1hvUDE1dFJzWlk2S3JVRVgzdFE9PSIsInZhbHVlIjoidUZydGYwR1Zrd0ZJUXV1bjJCbjZwUGV0Wm9PQVpsVWV2cjNhM3hWVWtwZGk4ZDMzeVkyK2ZWSDExZVZSS3pMN3V6c3dxMURSVExUZG0wbVkyUEM5WHA0YUFHdCs1RzRBdzFrR2tGaktlT3RwdjVHYmk0TzY2ZjdaSFpmSjNSZ3MiLCJtYWMiOiJlMWUwYzk4Mzg4MDQyMGNiYzYwMDg3ODkyNThjN2UyOWRhZTM4M2E5ZDY3NzQ3NDMxMjZhNmNiMGYxNzJiMTk1IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 14:35:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902193645\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-215329259 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HIFIBSOF75z10mVeKEpiyurHuBRr9opJ79mlwkSS</span>\"\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>20</span>\n  \"<span class=sf-dump-key>store_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://127.0.0.1:8000/admin/offers/requested/36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>402</span>\n  \"<span class=sf-dump-key>system_settings</span>\" => \"<span class=sf-dump-str title=\"504 characters\">{&quot;app_name&quot;:&quot;\\u0627\\u0648\\u0641\\u0631\\u0644\\u064a&quot;,&quot;support_number&quot;:&quot;919876543210&quot;,&quot;support_email&quot;:&quot;<EMAIL>&quot;,&quot;logo&quot;:null,&quot;favicon&quot;:null,&quot;storage_type&quot;:&quot;local&quot;,&quot;current_version_of_android_app&quot;:&quot;1.1.0&quot;,&quot;current_version_of_ios_app&quot;:&quot;1.0.0&quot;,&quot;version_system_status&quot;:0,&quot;expand_product_image&quot;:0,&quot;customer_app_maintenance_status&quot;:1,&quot;offer_request_status&quot;:0,&quot;message_for_customer_app&quot;:&quot;sdsa&quot;,&quot;sidebar_color&quot;:null,&quot;sidebar_type&quot;:null,&quot;navbar_fixed&quot;:0,&quot;theme_mode&quot;:0,&quot;currency_setting&quot;:&quot;\\u062f.\\u0644&quot;}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215329259\", {\"maxDepth\":0})</script>\n"}}