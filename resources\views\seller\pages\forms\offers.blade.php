@extends('seller/layout')
@section('title')
{{ __('admin_labels.offers') }}
@endsection
@section('content')
<section class="main-content">
    <div class="container-fluid mt-5 mb-5 px-6">
        <div class="row">
            <div class="d-flex row align-items-center">
                <div class="col-md-6 page-info-title">
                    <h3>{{ __('admin_labels.add_offer') }}
                    </h3>
                    <p class="sub_title">
                        {{ __('admin_labels.add_offers_with_power_and_simplicity') }}
                    </p>
                </div>
                <div class="col-md-6 d-flex justify-content-end" dir="ltr">
                    <nav aria-label="breadcrumb" class="float-end">
                        <ol class="breadcrumb">
                            <i class='bx bx-home-smile'></i>
                            <li class="breadcrumb-item"><a
                                    href="{{ route('seller.home') }}">{{ labels('admin_labels.home', 'Home') }}</a>
                            </li>
                            <li class="breadcrumb-item">
                                {{ __('admin_labels.offers') }}
                            </li>
                            <li class="breadcrumb-item">
                                {{ __('admin_labels.manage_offers') }}
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                {{ __('admin_labels.add_offer') }}
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Basic Layout -->

        <section class="overview-statistic">

            <div class="col-xxl-12 p-0">
                <div class="row cols-5 d-flex">
                    <div class="col-md-12 col-xl-3">
                        <div class="card p-5">
                            <div class="card1">
                                <ul id="progressbar" class="text-center">
                                    <li class="active step0"></li>
                                    <li class="step0"></li>
                                    <li class="step0"></li>
                                    <li class="step0 "></li>
                                    <li class="step0 "></li>
                                    <li class="step0"></li>
                                    <li class="step0"></li>
                                </ul>

                                <h6 class="mt-1">
                                    {{ __('admin_labels.select_offer_category') }}
                                </h6>
                                <h6>{{ __('admin_labels.offer_information') }}
                                </h6>
                                <h6>{{ __('admin_labels.offer_price') }}
                                </h6>
                                <h6>
                                    {{ __('admin_labels.expire_date') }}
                                </h6>
                                <h6>
                                    {{ __('admin_labels.offer_store_branches') }}
                                </h6>
                                <h6>{{ __('admin_labels.offer_code') }}
                                </h6>
                                <h6>{{ __('admin_labels.offer_media') }}
                                </h6>

                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 col-xl-9 mt-md-2 mt-sm-2 mt-xl-0">
                        @if($branches->isEmpty())
                        <div class="alert alert-warning text-center p-5">
                            <h4>{{ __('admin_labels.no_branches_found') }}</h4>
                            <p>{{ __('admin_labels.please_add_branch_before_creating_offer') }}</p>
                            <a href="{{ route('storebranches.create') }}" class="btn  mt-3">
                                {{ __('admin_labels.add_branch_now') }}
                            </a>
                        </div>
                        @else


                        <form action="{{ route('seller_products.store') }}" enctype="multipart/form-data"
                            method="POST" id="save-offer">
                            @php

                            $store_id = getStoreId();

                            @endphp
                            <input type="hidden" name="store_id" value="{{ $store_id }}">
                            @csrf

                            <div class="card2 first-screen ml-2 show">
                                <div class="row">

                                    <div class="col col-xxl-6">
                                        <div class="card p-5">
                                            <h6>{{ labels('admin_labels.select_category', 'Select Product Category') }}
                                            </h6>
                                            <hr>
                                            <div id="offer_category_tree_view_html" class='category-tree-container'>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-end ml-2 mt-xxl-3 mt-7 next-button text-center" data-step="step1">
                                    <button type="button"
                                        class="btn btn-primary">{{ labels('admin_labels.next', 'Next') }}</button>
                                </div>
                            </div>

                            <div class="card2 ml-2">
                                <div class="row">
                                    <div class="col col-xxl-12">
                                        <div class="card p-5">
                                            <h6>{{ __('admin_labels.offer_information') }}
                                            </h6>
                                            <hr>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="pro_input_text_ar"
                                                            class="form-label">{{ __('admin_labels.offer_name_ar') }}
                                                            <span class='text-asterisks text-sm'>*</span></label>
                                                        <input type="text" class="form-control" id="pro_input_text_ar"
                                                            placeholder="{{ __('admin_labels.offer_name_ar') }}" name="pro_input_name_ar">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="pro_input_text_en"
                                                            class="form-label">{{ __('admin_labels.offer_name_en') }}
                                                            <input type="text" class="form-control" id="pro_input_text_en"
                                                                placeholder="{{ __('admin_labels.offer_name_en') }}" name="pro_input_name_en">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <label for="description_ar"
                                                            class="form-label">{{ __('admin_labels.offer_description_ar') }}
                                                            <span class='text-asterisks text-sm'>*</span></label>
                                                        <textarea class="form-control" id="description_ar"
                                                            name="description_ar"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <label for="description_en"
                                                            class="form-label">{{ __('admin_labels.offer_description_en') }}
                                                            <textarea class="form-control" id="description_en"
                                                                name="description_en"></textarea>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="tags"
                                                            class="form-label">{{ labels('admin_labels.tags', 'Tags') }}
                                                        </label>
                                                        <input type="text" class="form-control" id="tags"
                                                            placeholder="dress,milk,almond" name="tags">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <label for="pro_short_description"
                                                            class="form-label">{{ __('admin_labels.short_description') }}
                                                            <span class='text-asterisks text-sm'>*</span></label>
                                                        <textarea class="form-control" id="short_description"
                                                            name="short_description"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-end ml-2 mt-xxl-3 mt-7 next-button text-center" data-step="step2">
                                    <button type="button"
                                        class="btn btn-primary ">{{ labels('admin_labels.next', 'Next') }}</button>
                                </div>
                            </div>

                            <div class="card2 ml-2">
                                <div class="row">
                                    <div class="col col-xxl-12">
                                        <div class="card p-5">
                                            <h6>{{ __('admin_labels.offer_price') }}</h6>
                                            <hr>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="pro_input_price_before_discount"
                                                            class="form-label">{{ __('admin_labels.price_before_discount') }}</label>
                                                        <span class='text-asterisks text-sm'>*</span></label>
                                                        <input type="text" class="form-control" id="pro_input_price_before_discount"
                                                            placeholder="0.00" name="pro_input_price_before_discount">
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="pro_input_discounted_price"
                                                            class="form-label">{{ __('admin_labels.discounted_price') }}</label>
                                                        <span class='text-asterisks text-sm'>*</span></label>
                                                        <input type="text" class="form-control" id="pro_input_discounted_price"
                                                            placeholder="0.00" name="pro_input_discounted_price">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-end ml-2 mt-xxl-3 mt-7 next-button text-center" data-step="step3">
                                    <button type="button" class="btn btn-primary ">Next</button>
                                </div>
                            </div>
                            <div class="card2 ml-2 product_quantity_and_others">
                                <div class="row">
                                    <div class="col col-xxl-12">
                                        <div class="card p-5">
                                            <h6>{{ __('admin_labels.expire_date') }}
                                            </h6>
                                            <hr>
                                            <div class="row">
                                                <div class="col-md-6 total_allowed_quantity">
                                                    <div class="form-group">
                                                        <label for="expire_date"
                                                            class="form-label">{{ __('admin_labels.select_expire_date') }}
                                                            <span class='text-asterisks text-sm'>*</span></label>
                                                        <input type="datetime-local" class="col-md-12 form-control"
                                                            name="expire_date" value=""
                                                            min="{{ date('Y-m-d\TH:i') }}"
                                                            placeholder="Select Expire Date and Time">
                                                    </div>
                                                </div>

                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="float-end ml-2 mt-xxl-3 mt-7 next-button text-center" data-step="step4">
                                    <button type="button"
                                        class="btn btn-primary ">{{ labels('admin_labels.next', 'Next') }}</button>
                                </div>
                            </div>
                            <div class="card2 ml-2 ">
                                <div class="row">
                                    <div class="col col-xxl-12">
                                        <div class="card p-5">
                                            <h6>{{ __('admin_labels.offer_store_branches') }}
                                            </h6>
                                            <hr>

                                            <div class="row">

                                                <div class="col-md-6 mt-7 ">
                                                    <div class="form-group">
                                                        <div class="d-flex justify-content-between">
                                                            <div>
                                                                <label for="all_branches"
                                                                    class="form-label">{{ __('admin_labels.all_branches') }}</label>
                                                            </div>
                                                            <div class="d-flex">

                                                                <div class="form-check form-switch">
                                                                    <input class="form-check-input" type="checkbox"
                                                                        id="is_all_branches_checkbox"
                                                                        name="all_branches" checked>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 mt-7 collapse" id='branches_till'>
                                                    <div class="form-group">
                                                        <label for="branches_till"
                                                            class="form-label">{{ __('admin_labels.branches') }}</label>
                                                        <select class="form-select branches_list"
                                                            id="branches_list" name="branches[]"
                                                            multiple>
                                                        </select>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="float-end ml-2 mt-xxl-3 mt-7 next-button text-center" data-step="step5">
                                    <button type="button"
                                        class="btn btn-primary ">{{ labels('admin_labels.next', 'Next') }}</button>
                                </div>
                            </div>
                            <div class="card2 ml-2">
                                <div class="row">
                                    <div class="col col-xxl-12">
                                        <div class="card p-5">
                                            <h6>{{ __('admin_labels.offer_code') }}
                                            </h6>
                                            <hr>

                                            <div class="row">

                                                <div class="col-md-6 mt-7 ">
                                                    <div class="form-group">
                                                        <div class="d-flex justify-content-between">
                                                            <div>
                                                                <label for="add_code"
                                                                    class="form-label">{{ __('admin_labels.add_code') }}</label>
                                                            </div>
                                                            <div class="d-flex">

                                                                <div class="form-check form-switch">
                                                                    <input class="form-check-input" type="checkbox"
                                                                        id="add_code_checkbox"

                                                                        name="add_code">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 mt-7 collapse" id='code_till'>
                                                    <div class="form-group">
                                                        <label for="code_till"
                                                            class="form-label">{{ __('admin_labels.code_count') }}</label>
                                                        <span class='text-asterisks text-sm'>*</span></label>
                                                        <input type="text" class="form-control" id="code_count"
                                                            placeholder="0" name="code_count">
                                                        </select>
                                                    </div>
                                                </div>

                                            </div>


                                        </div>
                                    </div>
                                </div>
                                <div class="float-end ml-2 mt-xxl-3 mt-7 next-button text-center" data-step="step6">
                                    <button type="button"
                                        class="btn btn-primary ">{{ labels('admin_labels.next', 'Next') }}</button>
                                </div>
                            </div>
                            <div class="card2 ml-2">
                                <div class="row">
                                    <div class="col-6 col-xxl-6">
                                        <div class="card p-5">
                                            <h6>{{ __('admin_labels.offer_media') }} ({{ labels('admin_labels.images', 'Images') }})</h6>
                                            <hr>

                                            {{-- Main Image --}}
                                            <div class="mb-4">
                                                <label class="form-label">
                                                    {{ labels('admin_labels.main_image', 'Main Image') }}
                                                    <span class="text-asterisks text-sm">*</span>
                                                </label>
                                                <input type="file" name="main_image" id="mainImageInput"
                                                    class="form-control @error('main_image') is-invalid @enderror" accept="image/*" required>
                                                @error('main_image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                                <small class="text-muted">(Recommended Size: 180 x 180 pixels)</small>

                                                <div class="mt-3">
                                                    <img id="mainImagePreview" src="#" alt="Main Image Preview"
                                                        class="img-fluid rounded d-none" style="max-height: 180px;">
                                                </div>
                                            </div>

                                            {{-- Other Images --}}
                                            <div class="mb-4">
                                                <label class="form-label">
                                                    {{ labels('admin_labels.other_images', 'Other Images') }}
                                                </label>
                                                <input type="file" name="other_images[]" id="otherImagesInput"
                                                    class="form-control" accept="image/*" multiple>
                                                <small class="text-muted">(Recommended Size: 180 x 180 pixels)</small>

                                                <div class="row mt-3" id="otherImagesPreview"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="float-end ml-2 mt-xxl-3 mt-7 text-center">
                                    <button type="submit"
                                        class="btn btn-primary submit_button">{{ labels('admin_labels.submit', 'Submit') }}</button>
                                </div>


                            </div>

                        </form>
                        @endif
                        <div class="float-end ml-2 mt-xxl-3 mt-7 me-0  px-3 text-center">
                            <p class="prev btn reset-btn">{{ labels('admin_labels.go_back', 'Go Back') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</section>
<script>
    // Main image preview
    document.getElementById('mainImageInput').addEventListener('change', function(e) {
        const preview = document.getElementById('mainImagePreview');
        const file = e.target.files[0];
        if (file) {
            preview.src = URL.createObjectURL(file);
            preview.classList.remove('d-none');
        } else {
            preview.classList.add('d-none');
            preview.src = '';
        }
    });

    // Multiple images preview
    document.getElementById('otherImagesInput').addEventListener('change', function(e) {
        const container = document.getElementById('otherImagesPreview');
        container.innerHTML = ''; // Clear previous previews
        Array.from(e.target.files).forEach(file => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const col = document.createElement('div');
                col.className = 'col-4 mb-2';
                col.innerHTML = `<img src="${e.target.result}" class="img-fluid rounded border" style="max-height: 150px;">`;
                container.appendChild(col);
            };
            reader.readAsDataURL(file);
        });
    });
</script>

@endsection