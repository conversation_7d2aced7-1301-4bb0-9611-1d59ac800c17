 <!-- Sidebar -->

 <nav class="navbar-vertical navbar bg-white">
     <div class="nav-scroller bg-white">
         @php

         use Chatify\ChatifyMessenger;

         $setting = getSettings('system_settings', true);
         $setting = json_decode($setting, true);

         $logo = file_exists(public_path(config('constants.MEDIA_PATH') . $setting['logo']))
         ? asset(config('constants.MEDIA_PATH') . $setting['logo'])
         : asset(config('constants.DEFAULT_LOGO'));
         @endphp
         <div class="sidenav-header d-flex justify-content-center align-items-center">
             <a class="navbar-brand m-0" href="{{ route('seller.home') }}">
                 <img src="{{ $logo }}" class="navbar-brand-img" alt="main_logo">
             </a>
         </div>


         <!-- code for menu search -->

         <div class="ps-2 pe-2 mt-4">
             <!-- Search Bar -->
             <input type="text" class="form-control menuSearch" placeholder="بحث ...">
         </div>

         <ul class="navbar-nav" id="menuList">
             <li class="sidebar-title"><i class='bx bx-tachometer'></i>
                 {{ labels('admin_labels.dashboard', 'Dashboard') }}
             </li>
             <li class="nav-item ">
                 <a class="nav-link {{ Request::is('seller/home') || Request::is('seller/home/<USER>') ? 'active' : '' }}"
                     href="{{ route('seller.home') }}">
                     <span class="nav-link-text ">{{ labels('admin_labels.home', 'Home') }}</span>
                 </a>
             </li>
             <li class="sidebar-title "><i class='bx bx-card'></i> {{ labels('admin_labels.manage', 'Manage') }}</li>
             <li class="nav-item ">
                 <a data-bs-toggle="collapse" href="#order_dropdown"
                     class="nav-link {{ Request::is('seller/ads') || Request::is('seller/ads*') ? 'active' : '' }}  {{ Request::is('seller/ads') || Request::is('seller/orders*') ? '' : 'collapsed' }}"
                     aria-controls="order_dropdown" role="button" aria-expanded="false">
                     <span class="nav-link-text ">{{ __('admin_labels.manage_advertisments') }}</span><i
                         class="fas fa-angle-down"></i>
                 </a>
                 <div class="collapse {{ Request::is('seller/ads') || Request::is('seller/ads*') ? 'show' : '' }}"
                     id="order_dropdown">
                     <ul class="nav">
                         <li
                             class="nav-item {{ Request::is('seller/ads') || Request::is('seller/ads*') ? 'active' : '' }}">
                             <a class="nav-link " href="{{ route('seller.ads.index') }}">
                                 <span class="nav-link-text ">{{ __('admin_labels.advertisments') }}</span>
                             </a>
                         </li>
                     </ul>
                 </div>
             </li>
             <li class="nav-item ">
                 <a class="nav-link {{ Request::is('seller/categories') || Request::is('seller/categories/*') ? 'active' : '' }}"
                     href="{{ route('seller_categories.index') }}">
                     <span class="nav-link-text ">{{ labels('admin_labels.categories', 'Categories') }}</span>
                 </a>
             </li>

             <li class="nav-item ">
                 <a class="nav-link {{ Request::is('seller/accounts') || Request::is('seller/accounts/*') ? 'active' : '' }}"
                     href="{{ route('store_accounts.index') }}">
                     <span class="nav-link-text ">{{ __('admin_labels.store_account_manage') }}</span>
                 </a>
             </li>

             <li class="sidebar-title "><i class='bx bx-cart-alt'></i>
                 {{ __('admin_labels.offers') }}
             </li>

             <li class="nav-item ">
                 <a class="nav-link {{ Request::is('seller/offers/codes') || Request::is('seller/offers/codes') ? 'active' : '' }}"
                     href="{{ route('codes.index') }}">
                     <span class="sidenav-normal"> {{ __('admin_labels.offer_codes') }} </span>
                 </a>
             </li>

             <li class="nav-item ">
                 <a data-bs-toggle="collapse" href="#products_dropdown"
                     class="nav-link {{ Request::is('seller/offers') || Request::is('seller/offers/manage_offers') ? 'active' : '' }} {{ Request::is('seller/offers') || Request::is('seller/offers/*') || Request::is('seller/product_faqs') ? '' : 'collapsed' }}"
                     aria-controls="products_dropdown" role="button" aria-expanded="false">
                     <span
                         class="nav-link-text ">{{ __('admin_labels.manage_offers') }}</span><i
                         class="fas fa-angle-down"></i>
                 </a>
                 <div class="collapse {{ Request::is('seller/offers') || Request::is('seller/offers/*') || Request::is('seller/product_faqs') ? 'show' : '' }}"
                     id="products_dropdown">
                     <ul class="nav ">
                         <li class="nav-item {{ Request::is('seller/offers') ? 'active' : '' }}">
                             <a class="nav-link" href="{{ route('seller.offers.index') }}">
                                 <span class="nav-link-text">{{ __('admin_labels.add_offer') }}
                                 </span>
                             </a>
                         </li>
                         <li class="nav-item {{ Request::is('seller/offers/manage_offers') ? 'active' : '' }}">
                             <a class="nav-link" href="{{ route('store.offer.manage_offer') }}">
                                 <span
                                     class="nav-link-text">{{ __('admin_labels.manage_offers') }}
                                 </span>
                             </a>
                         </li>
                       
                        
                     </ul>
                 </div>
             </li>



             <li class="sidebar-title "><i class='bx bx-wallet-alt'></i>
                 {{ labels('admin_labels.wallet_management', 'Wallet Management') }}
             </li>
             <li class="nav-item ">
                 <a class="nav-link {{ Request::is('seller/transaction/wallet_transactions') ? 'active' : '' }}"
                     href="{{ route('seller.transaction.wallet_transactions') }}">
                     <span
                         class="nav-link-text ">{{ labels('admin_labels.wallet_transaction', 'Wallet Transaction') }}</span>
                 </a>
             </li>
             <li class="sidebar-title "><i
                     class='bx bx-tachometer'></i></i>{{ __('admin_labels.branches_manage') }}
             </li>

             <li class="nav-item {{ Request::is('seller/branches') ? 'active' : '' }}">
                 <a class="nav-link" href="{{ route('seller.branches') }}">
                     <span class="nav-link-text">{{ __('admin_labels.branches') }}</span>
                 </a>
             </li>

          
             
         </ul>
     </div>
 </nav>