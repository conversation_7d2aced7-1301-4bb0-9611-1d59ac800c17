{"__meta": {"id": "X268cd95c55f898da81732ba64e03ac5c", "datetime": "2025-06-29 00:11:31", "utime": 1751155891.134924, "method": "POST", "uri": "/admin/users/authenticate", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751155884.421662, "end": 1751155891.134994, "duration": 6.713331937789917, "duration_str": "6.71s", "measures": [{"label": "Booting", "start": 1751155884.421662, "relative_start": 0, "end": **********.419636, "relative_end": **********.419636, "duration": 2.997973918914795, "duration_str": "3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.419678, "relative_start": 2.998015880584717, "end": 1751155891.135001, "relative_end": 6.9141387939453125e-06, "duration": 3.715322971343994, "duration_str": "3.72s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 30564312, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/users/authenticate", "middleware": "web, CheckInstallation", "controller": "App\\Http\\Controllers\\Admin\\UserController@authenticate", "namespace": null, "prefix": "", "where": [], "as": "admin.authenticate", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserController.php&line=76\" onclick=\"\">app/Http/Controllers/Admin/UserController.php:76-108</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02049, "accumulated_duration_str": "20.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.541456, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 10.981}, {"sql": "select * from `users` where `mobile` = '*********' limit 1", "type": "query", "params": [], "bindings": ["*********"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Admin/UserController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\UserController.php", "line": 83}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.323344, "duration": 0.009800000000000001, "duration_str": "9.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:139", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=139", "ajax": false, "filename": "EloquentUserProvider.php", "line": "139"}, "connection": "eshop", "explain": null, "start_percent": 10.981, "width_percent": 47.828}, {"sql": "select * from `users` where `is_active` = 1 and `users`.`id` = 402 limit 1", "type": "query", "params": [], "bindings": [1, 402], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/UserController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\UserController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5371408, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "UserController.php:87", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/UserController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\UserController.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserController.php&line=87", "ajax": false, "filename": "UserController.php", "line": "87"}, "connection": "eshop", "explain": null, "start_percent": 58.809, "width_percent": 15.569}, {"sql": "select * from `roles` where `roles`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/UserController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\UserController.php", "line": 87}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.920739, "duration": 0.00525, "duration_str": "5.25ms", "memory": 0, "memory_str": null, "filename": "UserController.php:87", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Admin/UserController.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Controllers\\Admin\\UserController.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserController.php&line=87", "ajax": false, "filename": "UserController.php", "line": "87"}, "connection": "eshop", "explain": null, "start_percent": 74.378, "width_percent": 25.622}]}, "models": {"data": {"App\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\awfarly\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\awfarly\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2Fawfarly%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "gHMtR2YExSHHEgFc714qMfUiCrPEtqVPiRz3Qc6o", "store_id": "20", "store_name": "null", "store_image": "null", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "402"}, "request": {"path_info": "/admin/users/authenticate", "status_code": "<pre class=sf-dump id=sf-dump-309868396 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-309868396\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-507823849 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-507823849\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-702164133 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gHMtR2YExSHHEgFc714qMfUiCrPEtqVPiRz3Qc6o</span>\"\n  \"<span class=sf-dump-key>mobile</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702164133\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1435855256 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InpkWTdNRm01aUZHdkNOQzd6UDlFWnc9PSIsInZhbHVlIjoiZEs1M2EveWE4VEZaZkxBaVc4QUJCaDEyVTNqRUt4SGdSWmE0K0RVenlUdHI3RTVlYnc5OUFRM2VPR01mbDhKdG95QjJXOU9FbGFVNFZOeU9jOEFCMkdoOXBjM3V2YUV6TUx0RXVTbXRndjRDSVhSdlRHRHRHa3ZsLzNDNG01MHgiLCJtYWMiOiJjNzg4YjdlOWFlMjAyM2NjZjQ2OTRmZDliODEwNWNhN2ExNzQzZDg2ZjAwZjMwODJmMGM1ZGQ1YjFiM2M3YmYxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlFVOEJSOVFLZXh0Y0prTzNvWllwd2c9PSIsInZhbHVlIjoiUXgzd2xHcWZGcHdxNGxQYnFLbkI4eEVyNGprWVJFMCtSZk8wQjVDNGl6VDFDK01iQWdqWlczdTgrYUhmUExnVFhXcmxsRHZzYW9aWWFhSVFsdnZCRlIvelQ0ZmpMRDUvbTFWUnFUSVNtNWVPY2NzTTdRei9RdnlMenFGcnFLSTUiLCJtYWMiOiIxOGJkZjdjMzY5ZjY4YTE4YjgwZGZhYjIxZTBmYjFiZDNlZDZhYTAyYmFjNThmYTU5ZTYwNjE5OWI1MjllOTljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435855256\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-686554151 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gHMtR2YExSHHEgFc714qMfUiCrPEtqVPiRz3Qc6o</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JssC6QxH2OM9qTm4IZiT0fAxVxpTy09zuEADGqN4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-686554151\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-285362797 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 29 Jun 2025 00:11:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii9DUEZEUGdaK0Q0ZktNaGg5MjY3L0E9PSIsInZhbHVlIjoiWGpnRVBnS1JkWS80OElZQnNBOElFeXpyb2dXOUMxS1hiN1VRTCtqWFplc0xPTmhzWkFlcWVCTXhLSFNpZ3l4UW56aDNVVEhVM2ZjUU43V1k1T3VqME9xWmtaeHhsN0NjQkI3MDNYclFmTEg5SzE5eXFheTBOa0R3UU9pQTBsMEciLCJtYWMiOiJlNDQxZjM4NmY4ZDg4YjA2NTUwYjQxZGExN2VlODU2NzQzOTdiNzAxYTczN2VkYTY3NzhhYTM3YWQzYTFlYWViIiwidGFnIjoiIn0%3D; expires=Sun, 29 Jun 2025 02:11:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IitLdU9VZXJEVkljZ1FJSTNqMm85QkE9PSIsInZhbHVlIjoiTTJaU1VkOGxXbktMNWR4dHUrNnlXNzZTMWxuSVNFTFdJamJTd25mVEkyVXdFcUtvd2M3Z0ZjRlBZTFMyWUZVZEZlMjY3Wi9VK1RNM1NVSDJrRnpkZVR6MG5DTEtxTlBUSVREai9mZWZJQ0NsR2ozWXZ6enNvMzBhWEtLd1k5d3oiLCJtYWMiOiI4YjkwMTY5ZGMxMmQyMmIyNGExMzBiYzAxNGE3NmE2N2M3ZjEzMTUxZjQ0OWI4ZmY0Njc1ZmUyNWI2ZDA3ODBlIiwidGFnIjoiIn0%3D; expires=Sun, 29 Jun 2025 02:11:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii9DUEZEUGdaK0Q0ZktNaGg5MjY3L0E9PSIsInZhbHVlIjoiWGpnRVBnS1JkWS80OElZQnNBOElFeXpyb2dXOUMxS1hiN1VRTCtqWFplc0xPTmhzWkFlcWVCTXhLSFNpZ3l4UW56aDNVVEhVM2ZjUU43V1k1T3VqME9xWmtaeHhsN0NjQkI3MDNYclFmTEg5SzE5eXFheTBOa0R3UU9pQTBsMEciLCJtYWMiOiJlNDQxZjM4NmY4ZDg4YjA2NTUwYjQxZGExN2VlODU2NzQzOTdiNzAxYTczN2VkYTY3NzhhYTM3YWQzYTFlYWViIiwidGFnIjoiIn0%3D; expires=Sun, 29-Jun-2025 02:11:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IitLdU9VZXJEVkljZ1FJSTNqMm85QkE9PSIsInZhbHVlIjoiTTJaU1VkOGxXbktMNWR4dHUrNnlXNzZTMWxuSVNFTFdJamJTd25mVEkyVXdFcUtvd2M3Z0ZjRlBZTFMyWUZVZEZlMjY3Wi9VK1RNM1NVSDJrRnpkZVR6MG5DTEtxTlBUSVREai9mZWZJQ0NsR2ozWXZ6enNvMzBhWEtLd1k5d3oiLCJtYWMiOiI4YjkwMTY5ZGMxMmQyMmIyNGExMzBiYzAxNGE3NmE2N2M3ZjEzMTUxZjQ0OWI4ZmY0Njc1ZmUyNWI2ZDA3ODBlIiwidGFnIjoiIn0%3D; expires=Sun, 29-Jun-2025 02:11:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285362797\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-107619552 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gHMtR2YExSHHEgFc714qMfUiCrPEtqVPiRz3Qc6o</span>\"\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>20</span>\n  \"<span class=sf-dump-key>store_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>402</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107619552\", {\"maxDepth\":0})</script>\n"}}