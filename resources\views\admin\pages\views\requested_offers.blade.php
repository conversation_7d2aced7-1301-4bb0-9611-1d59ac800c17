@extends('admin.layout')

@section('title', __('admin_labels.view_offer'))

@section('content')
@if(session('success'))
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="bx bx-check-circle me-2"></i> {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif
@if(session('error'))
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="bx bx-error-circle me-2"></i> {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
@endif

<div class="container-fluid py-4">
    <div class="card border-0 shadow-sm rounded-lg">
        <!-- Header Section -->
        <div class="card-header bg-white border-bottom px-4 py-3 d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-1 fw-semibold">{{ __('admin_labels.offer_details') }}</h5>
                <span class="text-muted small">ID: #{{ $offer->id }}</span>
            </div>
            <span class="badge bg-{{ $offer->status_color }} rounded-pill px-3 py-1">
                {{ __('admin_labels.status_' . $offer->status) }}
            </span>
        </div>

        <div class="card-body p-4">
            <!-- Images Gallery -->
            @if($offer->images->isNotEmpty())
            <div class="mb-4">
                <h6 class="mb-3 text-dark fw-medium">{{ __('admin_labels.images') }}</h6>
                <div class="d-flex flex-wrap gap-3">
                    @foreach($offer->images as $image)
                    <a href="{{ getMediaImageUrl($image->image_url) }}" data-fancybox="gallery" class="d-block">
                        <img src="{{ getMediaImageUrl($image->image_url) }}" 
                             class="rounded border shadow-sm" 
                             style="width: 140px; height: 100px; object-fit: cover; cursor: pointer;">
                    </a>
                    @endforeach
                </div>
            </div>
            <hr class="my-4">
            @endif

            <div class="row">
                <!-- Left Column - Offer Content -->
                <div class="col-lg-6 pe-lg-4">
                    <!-- Titles -->
                    <div class="mb-4">
                        <h6 class="mb-3 text-dark fw-medium">{{ __('admin_labels.titles') }}</h6>
                        <div class="p-3 bg-light rounded">
                            <div class="mb-3">
                                <label class="d-block text-muted small mb-1">العربية</label>
                                <p class="mb-0">{{ $offer->title_ar }}</p>
                            </div>
                            <div>
                                <label class="d-block text-muted small mb-1">English</label>
                                <p class="mb-0">{{ $offer->title_en }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Descriptions -->
                    <div class="mb-4">
                        <h6 class="mb-3 text-dark fw-medium">{{ __('admin_labels.descriptions') }}</h6>
                        <div class="p-3 bg-light rounded">
                            <div class="mb-3">
                                <label class="d-block text-muted small mb-1">العربية</label>
                                <p class="mb-0">{{ $offer->description_ar }}</p>
                            </div>
                            <div>
                                <label class="d-block text-muted small mb-1">English</label>
                                <p class="mb-0">{{ $offer->description_en }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Offer Details -->
                <div class="col-lg-6 ps-lg-4">
                    <!-- Pricing Information -->
                    <div class="mb-4">
                        <h6 class="mb-3 text-dark fw-medium">{{ __('admin_labels.pricing') }}</h6>
                        <div class="p-3 bg-light rounded">
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">{{ __('admin_labels.price') }}</span>
                                <strong>{{ number_format($offer->price, 2) }} د.ل</strong>
                            </div>
                            @if($offer->discounted_price)
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">{{ __('admin_labels.discounted_price') }}</span>
                                <strong class="text-danger">{{ number_format($offer->discounted_price, 2) }} د.ل</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">{{ __('admin_labels.discount') }}</span>
                                <span class="badge bg-danger bg-opacity-10 text-danger">
                                    {{ calculate_discount_percentage($offer->price, $offer->discounted_price) }}%
                                </span>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Offer Metadata -->
                    <div class="mb-4">
                        <h6 class="mb-3 text-dark fw-medium">{{ __('admin_labels.offer_info') }}</h6>
                        <div class="p-3 bg-light rounded">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="d-block text-muted small mb-1">{{ __('admin_labels.offer_type') }}</label>
                                    <span class="badge bg-primary bg-opacity-10 text-primary">
                                        {{ $offerType }}
                                    </span>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="d-block text-muted small mb-1">{{ __('admin_labels.created_at') }}</label>
                                    <span>{{ $offer->created_at->format('Y-m-d H:i') }}</span>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="d-block text-muted small mb-1">{{ __('admin_labels.expire_date') }}</label>
                                    <span class="d-flex align-items-center gap-2">
                                        {{ $offer->expire_date->format('Y-m-d H:i') }}
                                        @if(is_offer_expired($offer->expire_date))
                                        <span class="badge bg-danger bg-opacity-10 text-danger">منتهي</span>
                                        @endif
                                    </span>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="d-block text-muted small mb-1">{{ __('admin_labels.categories') }}</label>
                                    <div class="d-flex flex-wrap gap-2">
                                        @foreach($offer->categories as $category)
                                        <span class="badge bg-secondary bg-opacity-10 text-secondary">
                                            {{ $category->name_ar }}
                                        </span>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Store Information -->
                    <div>
                        <h6 class="mb-3 text-dark fw-medium">{{ __('admin_labels.store_info') }}</h6>
                        <div class="p-3 bg-light rounded">
                            <div class="d-flex align-items-start gap-3">
                                @if($offer->store?->logo)
                                <img src="{{ getMediaImageUrl($offer->store->logo) }}" 
                                     class="rounded-circle border" 
                                     style="width: 60px; height: 60px; object-fit: cover;">
                                @endif
                                <div>
                                    <div class="mb-2">
                                        <label class="d-block text-muted small mb-1">{{ __('admin_labels.store_name') }}</label>
                                        <span>{{ $offer->store?->name ?? 'N/A' }}</span>
                                    </div>
                                    @if($offer->store?->contact_number)
                                    <div class="mb-2">
                                        <label class="d-block text-muted small mb-1">{{ __('admin_labels.contact_number') }}</label>
                                        <span>{{ $offer->store->contact_number }}</span>
                                    </div>
                                    @endif
                                    @if($offer->store?->address_ar)
                                    <div>
                                        <label class="d-block text-muted small mb-1">{{ __('admin_labels.address') }}</label>
                                        <span>{{ $offer->store->address_ar }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
          @if($offer->is_approved === 0)
            <div class="mt-5 pt-4 border-top">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <form method="POST" action="{{ route('admin.requested_offers.approve', $offer->id) }}" >
                            @csrf
                            <button  type="submit" class="btn btn-success w-100 py-2 d-flex align-items-center justify-content-center gap-2" >
                                <i class="bx bx-check-circle"></i>
                                {{ __('admin_labels.approve') }}
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6 mb-3">
                        <form method="POST" action="{{ route('admin.requested_offers.reject', $offer->id) }}" >
                            @csrf
                            <button type="submit" class="btn btn-outline-danger w-100 py-2 d-flex align-items-center justify-content-center gap-2" >
                                <i class="bx bx-x-circle"></i>
                                {{ __('admin_labels.reject') }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            @endif
        </div>
       
    </div>
    
</div>

@push('styles')
<style>
    .card {
        border-radius: 8px;
    }
    .bg-light {
        background-color: #f8f9fa !important;
    }
    hr {
        opacity: 0.15;
    }
    .badge {
        font-weight: 500;
        padding: 0.35em 0.65em;
    }
</style>
@endpush

@endsection