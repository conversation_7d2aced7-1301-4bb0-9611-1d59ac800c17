<?php return [
    'login_desc' => "Hey, Enter your details to get sign in to your account",
    'seller_login' => "Seller Login",
    'admin_login' => "Admin Login",
    'dont_have_account' => "Don't have any account?",
    'sign_up' => "Sign Up",
    'seller_registration' => 'Seller Registration',
    'created_at' => 'Created At',
    'expire_date' => 'Expire Date',
    'offers' => 'Offers',
    'manage_offers' => 'Manage Offers',
    'add_offers' => 'Add Offers',
    'id' => 'ID',
    'offer_views' => 'Offer Views',
    'name' => 'Name',
    'image' => 'Image',
    'action' => 'Action',
    'track_and_manage_offers' => 'Track and Manage Offers',
    'home' => 'Home',
    'search' => 'Search',
    'simple_offers' => 'Simple Offers',
    'offers_with_code' => 'Offers With Code',
    'select_type' => 'Select Type',
    'product_type' => 'Product Type',
    'select_category' => 'Select Category',
    'select_status' => 'Select Status',
    'active' => 'Active',
    'deactive' => 'Deactive',
    "view_offer" => "View Offer",
    'view_offer_details_and_analytics' => 'View Offer Details & Analytics',
    'select_offer_category' => 'Select Offer Category',
    'offer_information' => 'Offer Information',
    'offer_name_ar' => 'Offer Name in Arabic',
    'offer_name_en' => 'Offer Name in English',
    'offer_description_ar' => 'Offer Description in Arabic',
    'offer_description_en' => 'Offer Description in English',
    'short_description' => 'Short Description',
    'images' => 'Images',
    'add_offer' => "Add Offer",
    'add_offers_with_power_and_simplicity' => 'Add offers with power and simplicity',
    'offer_price' => 'Offer Price',
    'price_before_discount' => 'Price Before Discount',
    'discounted_price' => 'Discounted Price',
    'expire_date' => 'Expire Date',
    'select_expire_date' => 'Select Expire Date',
    'offer_store_branches' => 'Offer Store Branches',
    'all_branches' => 'All Branches',
    'branches' => 'Branches',
    'branch' => 'Branch',
    'offer_code' => 'Offer Code',
    'add_code' => 'Add Code',
    'code_count' => 'Code count in each branch',
    'offer_media' => 'Offer Media',
    'choose_offer_image' => 'Choose Offer Image',
    'description_required' => "Please enter a description.",
    "offer_added_successfully" => 'Offer added successfully.',
    'offer_added_failed' => 'Offer added failed.',
    'edit_offer' => 'Edit Offer',
    'select_branches' => 'Select Branches',
    'select_all_branches' => 'Select All Branches',
    'search_branches'    => 'Search Branches',
    'all_branches_description' => 'Select all branches to apply the offer to all branches.',
    'offer_deleted_successfully' => 'Offer deleted successfully.',
    'offer_deleted_failed' => 'Offer deleted failed.',
    'delete' => 'Delete',
    'edit' => 'Edit',
    'view' => 'View',
    'name_ar' => 'Name in Arabic',
    'name_en' => 'Name in English',
    'offer_code' => 'Offer Code',
    'offer_id' => 'Offer ID',
    'offer_discount' => 'Offer Discount',
    'offer_end_date' => 'Offer End Date',
    'offer_status' => 'Offer Status',
    'description_ar' => 'Description in Arabic',
    'description_en' => 'Description in English',
    'short_description' => 'Short Description',
    'offer_details' => 'Offer Details',
    'offer_information' => 'Offer Information',
    'offer_views' => 'Offer Views',
    'codes_available' => 'Codes Avl.',
    'claims' => 'Claims',
    'expired_date' => 'Expired Date',
    'expired' => 'Expired',
    'offer_reviews' => 'Offer Reviews',
    'anonymous' => 'Anonymous',
    'ratings_avg' => 'Avg. Rating',
    'offer_name' => 'Offer Name',
    'offer_codes' => 'Offer Codes',
    'all_advertisments' => 'All Advertisments',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'active' => 'Active',
    'expired' => 'Expired',
    'manage_advertisments' => 'Manage Advertisments',
    'advertisment_overview' => 'Advertisments Overview',
    'advertisments' => 'Advertisments',
    'package_id' => 'Package ID',
    'package_name' => 'Package Name',

    'advertisment_details' => 'Advertisment Details',
    'advertisment_information' => 'Advertisment Information',
    'advertisment_views' => 'Advertisment Views',
    'advertisment_clicks' => 'Advertisment Clicks',
    'advertisment_status' => 'Advertisment Status',
    'advertisment_type' => 'Advertisment Type',
    'advertisment_code' => 'Advertisment Code',
    'advertisment_start_date' => 'Advertisment Start Date',
    'advertisment_end_date' => 'Advertisment End Date',
    'expire_status' => 'Expire Status',
    'all_status' => 'All Status',

    'create_advertisement' => 'Create Advertisement',
    'create_advertisement_description' => 'Select an ad package and provide the necessary details to promote your offer',
    'ad_title_ar' => 'Ad Title (Arabic)',
    'ad_title_en' => 'Ad Title (English)',
    'ad_description_ar' => 'Ad Description (Arabic)',
    'ad_description_en' => 'Ad Description (English)',
    'advertisement_type' => 'Advertisement Type',
    'select_advertisement_type' => 'Select Advertisement Type',
    'ad_package' => 'Advertisement Package',
    'ad_image' => 'Ad Image',
    'submit_Advertisement' => 'Submit Advertisement',
    'general' => 'General',
    'store' => 'Store',
    'offer' => 'Offer',
    'choose_offer' => 'Choose Offer',
    'note' => 'Note',
    'ad_review_notice' => 'Your advertisement will first be reviewed by an admin. If approved, you will be required to complete payment either online or in cash before the ad becomes active.',
    'title_required' => 'The ad title is required.',
    'description_string' => 'The ad description must be a string.',
    'advertisement_type_required' => 'The advertisement type is required.',
    'package_id_required' => 'The package is required.',
    'package_id_exists' => 'The selected package is invalid.',
    'advertisment_added_successfully' => 'Advertisment added successfully.',
    'advertisment_added_failed' => 'Advertisment added failed.',
    'offer_type' => 'Offer Type',
    'offer_code' => 'Offer Code',
    'offer_discount' => 'Offer Discount',
    'offer_start_date' => 'Offer Start Date',
    'offer_end_date' => 'Offer End Date',
    'offer_status' => 'Offer Status',
    'offer_views' => 'Offer Views',
    'offer_clicks' => 'Offer Clicks',
    'validation_failed' => 'Validation failed',
    'advertisement_details' => 'Advertisement Details',
    'ad_package_type' => 'Advertisement Package Type',
    'analytics' => 'Analytics',
    'analytic_view' => 'Views',
    'analytic_clicks' => 'Clicks',
    'payment_status' => 'Payment Status',
    'paid' => 'Paid',
    'unpaid' => 'Unpaid',
    'pay_now' => 'Pay Now',
    'waiting_for_payment' => 'Approved, waiting for you to complete the payment',
    'waiting_for_payment_description' => 'Your advertisement has been approved, but payment has not been completed yet. Please complete the payment to activate the advertisement.',
    'category_desc' => 'All information about categories',
    'offer_count' => 'Offer Count in this Category',
    'add_new' => 'Add',
    'select_store_category' => 'Select Store Categories',
    'failed_to_update_categories' => 'Failed to update categories.',
    'categories_updated_successfully' => 'Categories updated successfully.',
    'completed' => 'Completed',
    'canceled' => 'Canceled',
    'branches_manage' => 'Branches Management',
    'branches_manage_desc' => 'Effortlessly Organize and Control Branches Data',
    'branch_name' => 'Branch Name',
    'branch_phone' => 'Phone',
    'branch_street' => 'Branch Street',
    'branch_city' => 'Branch City',
    'label_required' => 'The Name is required.',
    'phone_required' => 'The phone is required.',
    'street_required' => 'The street is required.',
    'city_id_required' => 'The city is required.',
    'city_id_exists' => 'The selected city is invalid.',
    'google_map_link_required' => 'The google map link is required.',
    'phone_required' => 'The phone is required.',
    'branch_added_successfully' => 'Branch added successfully.',
    'branch_added_failed' => 'Branch added failed.',
    'branch_deleted_successfully' => 'Branch deleted',
    'branch_google_map_link' => 'Google Map Link',
    'add_branch' => 'Add Branch',
    'add_branch_desc' => 'Fill in the details to create a new branch for your store.',
    'longitude' => 'Longitude',
    'latitude' => 'Latitude',
    'set_main_branch' => 'Set as Main Branch',
    'is_main' => 'Is Main',
    'edit_branch' => 'Edit Branch',
    'edit_branch_desc' => 'Edit branch details.',
    'cannot_delete_main_branch' => 'Cannot delete main branch.',
    'cannot_delete_last_branch' => 'Cannot delete last branch.',
    'store_account_manage' => 'Store Accounts Management',
    'role' => 'Role',
    'admin' => 'Admin',
    'store_employee' => 'Store Employee',
    'customer' => 'Customer',
    'phone' => 'Phone',
    'email' => 'Email',
    "create_store_user" => 'Create new User',
    'store_user_desc' => 'Add new user to the store',
    'confirm_password' => 'Confirm Password',
    'select_role' => 'Select User Role',
    'role_description' => 'Role Descriptions',
    'store_admin_desc' => 'Full access to manage the store, employees, offers, and settings.',
    'store_employee_desc' => 'Limited access to manage products and view reports only.',
    'store_admin' => 'Store Admin',
    'store_employee' => 'Store Employee',
    'email_required' => 'Email is required.',
    'phone_min' => 'Phone number must be at least 8 characters.',
    'password_required' => 'Password is required.',
    'password_min' => 'Password must be at least 6 characters.',
    'password_confirmation' => 'Password confirmation does not match.',
    'role_required' => 'Role is required.',
    'store_user_created_successfully' => 'Store user created successfully.',
    'email_already_exists' => 'Email already exists.',
    'phone_already_exists' => 'Phone already exists.',
    'edit_store_user' => 'Edit Store User',
    'edit_store_user_desc' => 'Edit store user details.',
    'store_user_updated_successfully' => 'Store user updated successfully.',
    'store_user_update_failed' => 'Store user update failed.',
    'user_deleted_successfully' => 'Store user deleted successfully.',
    'user_deleted_failed' => 'Store user delete failed.',
    'store_media' => 'Store Media',
    'store_info' => 'Store Info',
    'colors' => 'Colors',
    'edit_store' => 'Store Edit',
    'primary_color' => 'Primary Color',
    'secondary_color' => 'Secondary Color',
    'hover_color' => 'Hover Color',
    'active_color' => 'Active Color',
    'background_color' => 'Background Color',
    'store_created_successfully' => 'Store created successfully.',
    'store_user_data' => 'Store Account Data',

    'name_ar_required' => 'The Arabic name is required.',
    'name_ar_string'   => 'The Arabic name must be a string.',
    'name_ar_min'      => 'The Arabic name must be at least :min characters.',
    'name_ar_max'      => 'The Arabic name must not exceed :max characters.',

    'name_en_required' => 'The English name is required.',
    'name_en_string'   => 'The English name must be a string.',
    'name_en_min'      => 'The English name must be at least :min characters.',
    'name_en_max'      => 'The English name must not exceed :max characters.',

    'logo_required' => 'The store logo is required.',
    'logo_image'    => 'The logo must be an image.',
    'logo_mimes'    => 'The logo must be a file of type: jpg, jpeg, png, webp.',
    'logo_max'      => 'The logo must not be larger than 2MB.',

    'cover_image' => 'The cover must be an image.',
    'cover_mimes' => 'The cover must be a file of type: jpg, jpeg, png, webp.',
    'cover_max'   => 'The cover must not be larger than 2MB.',

    'primary_color_required' => 'The primary color is required.',
    'primary_color_string'   => 'The primary color must be a string.',
    'primary_color_max'      => 'The primary color must not exceed :max characters.',

    'is_active_required' => 'The store status is required.',
    'is_active_boolean'  => 'The store status must be true or false.',
    'last_login' => 'Last Login',
    'store_social' => 'Store Social Links',
    'store_name_ar' => 'Store Name (Arabic)',
    'store_name_en' => 'Store Name (English)',
    'cannot_change_mobile' => 'You cannot change this mobile number.',
    'cannot_change_email' => 'You cannot change this email.',
    'store_updated_successfully' => 'Store updated successfully.',
    'store_update_failed' => 'Store update failed.',
    'title_ar_required' => 'The Arabic title field is required.',
    'title_ar_string'   => 'The Arabic title must be a string.',
    'title_ar_max'      => 'The Arabic title may not be greater than 255 characters.',

    'title_en_string'   => 'The English title must be a string.',
    'title_en_max'      => 'The English title may not be greater than 255 characters.',

    'description_ar_string' => 'The Arabic description must be a string.',
    'description_ar_max'    => 'The Arabic description may not be greater than 2000 characters.',

    'description_en_string' => 'The English description must be a string.',
    'description_en_max'    => 'The English description may not be greater than 2000 characters.',

    'advertisement_type_required' => 'The advertisement type field is required.',
    'advertisement_type_in'       => 'The selected advertisement type is invalid. It must be OFFER, STORE, or GENERAL.',

    'package_id_required' => 'The package field is required.',
    'package_id_exists'   => 'The selected package is invalid or does not exist.',

    'image_image' => 'The uploaded file must be an image.',
    'image_mimes' => 'The image must be a file of type: jpeg, png, jpg, or webp.',
    'image_max'   => 'The image may not be greater than 2 MB.',
    'ad_created_successfully' => 'Ad created successfully.',
    'no_branches_found' => 'No branches found',
    'please_add_branch_before_creating_offer' => 'Please add at least one branch before creating an offer.',
    'add_branch_now' => 'Add Branch Now',
    'barnches_manage' => 'Branches Management',
    'branches_manage_desc' => 'Effortlessly Organize and Control Branches Data',
    'pricing' => 'Pricing',
    'offer_updated_successfully' => 'Offer updated successfully.',
    'name_ar_required' => 'Arabic name is required.',
    'description_ar_required' => 'Arabic description is required.',
    'invalid_category' => 'Selected category does not exist.',
    'image_required' => 'Main image is required.',
    'expire_date_required' => 'Expire date is required.',
    'code_count_integer' => 'Code count must be a number.',
    'code_count_min' => 'Code count must be at least 1.',
    'branches_required' => 'Please select at least one branch.',
    'branches_must_be_array' => 'Branches must be a list.',
    'price_required' => 'Price before discount is required.',
    'price_numeric' => 'Price before discount must be a number.',
    'price_min' => 'Price before discount cannot be negative.',
    'discounted_price_required' => 'Discounted price is required.',
    'discounted_price_numeric' => 'Discounted price must be a number.',
    'discounted_price_min' => 'Discounted price cannot be negative.',
    'new_offer_notification_title' => 'New Offer',
    'new_offer_notification_body' => 'There is a new offer available now!',
    'store_register' => 'Register New Store',
    'please_provide_name' => 'Please provide name.',
    'please_provide_valid_email' => 'Please provide valid email.',
    'please_provide_valid_password' => 'Please provide valid password.',
    'password_min' => 'Password must be at least 6 characters.',
    'passwords_must_match' => 'Password confirmation does not match.',
    'please_provide_valid_logo' => 'Please provide valid logo.',
    'please_provide_valid_cover' => 'Please provide valid cover.',
    'please_provide_valid_primary_color' => 'Please provide valid primary color.',
    'please_provide_mobile' => 'Please provide valid mobile number.',
    'please_provide_store_name_ar' => 'Please provide store name in Arabic.',
    'please_provide_store_name_en' => 'Please provide store name in English.',
    'please_select_at_least_one_category' => 'Please select at least one category.',
    'invalid_image_format' => 'Invalid image format.',
    'please_upload_store_logo' => 'Please upload valid store logo.',
    'mobile_min' => 'Mobile number must be at least 8 characters.',
    'mobile_max' => 'Mobile number must not exceed 20 characters.',
    'please_provide_valid_mobile' => 'Please provide valid mobile number.',
    'sending' => 'Sending OTP...',
    'otp_sent_success' => 'OTP sent successfully.',
    'failed_to_send_otp' => 'Failed to send OTP.',
    'resend_in' => 'Resend in',
    'resend_otp' => 'Resend Otp',
    'enter_otp' => 'Enter Otp',
    'registration_successful' => 'Store register successfully ',
    'no_reviews_found' => 'No Reviewa Found',
    'store_employee_login' => 'Store Employee Login',
    'mobile_not_found' => 'Mobile number not found.',
    'please_enter_mobile_number' => 'Please enter mobile number.',
    'login_successful' => 'Login successful.',
    'store_approve_successfully' => 'Store approved successfully.',
    'store_owner' => 'Store Owner',
    'reject' => 'Reject',
    'reason_for_rejection' => 'Reson For Rejection',
    "reject_store_request" => 'Reject Store',
    'confirm_rejection' => 'Confirm Rejection',
    'store_rejected_successfully' => 'Store Rejected Successfully',
    'offers_review' => 'Offers Review',
    'expire_offers' => 'Expire Offers',
    'reported_by' => 'Reported by',
    'reason' => 'Reason',
    'report_details' => 'Report Details',
    'offer_reports' => 'Offer Reports',
    'offer_report ' => 'Offer Report',
    'offer_info' => 'Offer Info',
    'registration_date' => 'Registration date',
    'category_name_exists' => 'The category name already exists in this store.',
    'upload_new_image' => 'Upload New Image',
    'main_cate' => 'Main Category',
    'sub_cate' => 'Sub Category',
    'failed' => 'Failed',
    'revenue' => 'Revenue',
    'all_transactions' => 'All transactions',
    'successful' => 'Successful',
    'in_process' => 'In process',
    'earnings' => 'Earnings',
    'transaction_type' => 'Transaction Type',
    'not_started' => 'Not Started',
    'add_new_slider' => 'Add New Slider',
    'slider_created_successfully' => 'Slider created successfully',
    'end_date_required' => 'The end date is required.',
    'end_date_date' => 'The end date must be a valid date.',
    'end_date_after' => 'The end date must be after or equal to the start date.',

    'start_date_required' => 'The start date is required.',
    'start_date_date' => 'The start date must be a valid date.',
    'start_date_before' => 'The start date must be before or equal to the end date.',
    'note' => 'Note',
    'slider_type_offer_explanation' => 'Displays an offer slider that links directly to the offer details page. You must select the related offer.',
    'slider_type_store_explanation' => 'Displays a store banner slider that links to the selected store profile. You must select the related store.',
    'slider_type_general_explanation' => 'Displays a general-purpose banner. You must provide an external URL to redirect users.',
    'total_slider' => 'Total Banner',
    'active_slider' => 'Active Banner',
    'expire_slider' => 'Expired Banner',
    'created_by'=>'Created By',
    'edit_slider'=>'Edit Slider',
    'slider_details' => 'Slider Details',
'created_by_store' => 'Created by Store',
'created_by_admin' => 'Created by Admin',
'store_id' => 'Store ID',
'store_name' => 'Store Name',
'package_info' => 'Package Info',
'package' => 'Package',
'price' => 'Price',
'duration' => 'Duration',
'days' => 'days',

'slider_info' => 'Slider Info',
'slider_overview' => 'Slider Overview',
'requested_slider' => 'Requested Slider',
'premuim_ads' => 'Premuim Ads',
'premuim_ads_overview' => 'Premuim Ads Overview',
'manage_premuim_ads' => 'Manage Premuim Ads',
'requested_premuim_ads' => 'Requested Premuim Ads',
'total_premuim' => 'Total Premuim Ads',
'active_premuim' => 'Active Premuim Ads',
'expire_premuim' => 'Expired Premuim Ads',
'add_new_premuim' => 'Add New Premuim Ad',
'premuim_created_successfully' => 'Premuim Ad created successfully',
'premuim_updated_successfully' => 'Premuim Ad updated successfully',
'edit_premuim' => 'Edit Premuim Ad',
'premuim_details' => 'Premuim Ad Details',
'premuim_info' => 'Premuim Ad Information',
'spotlight_ads' => 'Spotlight Ads',
'spotlight_ads_explanation' => 'Displays a spotlight offer or stores on home page ,search and categories page.',
'spotlight_ads_overview' => 'Spotlight Ads Overview',
'manage_spotlight_ads' => 'Manage Spotlight Ads',
'requested_spotlight_ads' => 'Requested Spotlight Ads',
'total_spotlight' => 'Total Spotlight Ads',
'active_spotlight' => 'Active Spotlight Ads',
'expire_spotlight' => 'Expired Spotlight Ads',
'add_new_spotlight' => 'Add New Spotlight Ad',
'start_date_required' => 'Start date is required',
'start_date_date' => 'Start date must be a valid date',
'start_date_before' => 'Start date must be before end date',

'end_date_required' => 'End date is required',
'end_date_date' => 'End date must be a valid date',
'end_date_after' => 'End date must be after start date',
'user_info' => 'User Info',
'user_id' => 'User ID',
'user_name' => 'User Name',
'email' => 'Email',
'phone' => 'Phone',
'basic_info'=>'Basic Info',
'requested_ads' => 'Requested Ads',
'view' => 'View',
'no_requested_ads' => 'No requested ads found.',
'ad_info' => 'Ad Info',
'created_by_user' => 'Created by User',
'ad_details'    => 'Ad Details',
'ad_approved_successfully' => 'Ad approved successfully!',
'ad_rejected_successfully' => 'Ad rejected successfully!',
'ban'=>'Ban',
'unban'=>'Unban',
'last_active'=>'Last Active',
'active_customers' => 'Active Customers',
'total_customers'=> 'Total Customers',
'banned_customers' => 'Banned Customers',
'deleted_customers' => 'Deleted Customers',
'customers_overview'    => 'Customers Overview',
'customer_details' => 'Customer Details',
'claim_date'=> 'Claim Date',
'redeemed'=>'Redeemed',
'not_redeemed'=>'Not Redeemed',
'no_claims_found'=>'No Claims Found',
'join_at'=>'Join At',
'user_banned_successfully'=>'User banned successfully',
'user_unbanned_successfully'=>'User unbanned successfully',
'activity'=>'Activity',
'total_claims'=>'Total Claims',
'favorites'=>'Favorites',
'is_redeemed'=>'Is Redeemed',
'fcm_ids_not_set'=>'FCM IDs are not set for the selected users.',
'all_users'=>'All Users',
'specific_user'=>'Specific User',
'seller_email_notification'=>'Seller email notification',
'all_store'=>'All Stores',
'specific_store'=>'Specific Stores',
'total_users' => 'Total Users',
    'banned_users' => 'Banned Users',
    'active_stores' => 'Active Stores',
    'active_offers' => 'Active Offers',
    'claimed_offers' => 'Claimed Offers',
    'ads_running' => 'Running Ads',
    'new_users_today' => 'New Users Today',
    'active_users_today' => 'Active Users Today',
    'pending_stores' => 'Pending Stores',
    'pending_offers' => 'Pending Offers',
    'pending_ads' => 'Pending Ads',
    'redeemed_offers' => 'Redeemed Offers',
    'new_users_growth' => 'New Users Growth',
    'new_users' => 'New Users',
    'recent_activity' => 'Recent Activity',
    'no_recent_activity' => 'No recent activity available',
    'new_user_registered'=>'New user registered:',
    'new_offer_created'=>'New offer created:',
    'offer_claimed'=>'Offer claimed by User ID:',
    'top_stores'=>'Top Stores',
    'top_offers'=>'Top Offers',
    'top_categories'=>'Top Categories',
    'view_store'=>'View Store',
    'view_offer'=>'View Offer',
    'view_category'=>'View Category',
    'view_user'=>'View User',
    'top_active_users'=>'Top Active Users',
    'active_ads'=>'Active Ads',
    'pending_ads'=>'Pending Ads',
    'with_for_pay'=>'With For Pay',
    'expired_ads'=>'Expired Ads',
    'ad_distribution'=>'Ad Distribution',
    'ads_per_month'=>'Ads Per Month',
    'latest_reports'=>'Latest Reports',
    'loading'=>'Loading...',
    'no_reports'=>'No reports found',
    'by'=>'By',
    'failed_to_load'=>'Failed to load data',
    'manage_requested_offers'=>'Manage Requested Offers',
    'no_requested_offers'=>'No requested offers found.',
    'offers_with_code'=>'Offers With Code',
    'offer_in_store'=>'Offer In Store',
    'offer_type'=>'Offer Type',
    'offer_approved_successfully' => 'Offer approved successfully!',
    'offer_rejected_successfully' => 'Offer rejected successfully!',
    'titles'=>'Titles',
    'descriptions'=>'Descriptions',
    'is_approved'=>'Is Approved'





















];
