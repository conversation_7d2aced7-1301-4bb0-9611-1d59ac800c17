{"__meta": {"id": "X059e068a0909d96d018a5fffcb453716", "datetime": "2025-06-28 18:16:53", "utime": **********.549345, "method": "GET", "uri": "/admin/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[18:16:53] LOG.warning: str_contains(): Passing null to parameter #1 ($haystack) of type string is deprecated in C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 268", "message_html": null, "is_string": false, "label": "warning", "time": **********.530755, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751134611.082179, "end": **********.549462, "duration": 2.467283010482788, "duration_str": "2.47s", "measures": [{"label": "Booting", "start": 1751134611.082179, "relative_start": 0, "end": **********.442183, "relative_end": **********.442183, "duration": 2.36000394821167, "duration_str": "2.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.442209, "relative_start": 2.360029935836792, "end": **********.549466, "relative_end": 3.814697265625e-06, "duration": 0.10725688934326172, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 29500880, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "admin.pages.forms.login", "param_count": null, "params": [], "start": **********.526179, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\resources\\views/admin/pages/forms/login.blade.phpadmin.pages.forms.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fresources%2Fviews%2Fadmin%2Fpages%2Fforms%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}]}, "route": {"uri": "GET admin/login", "middleware": "web, CheckInstallation", "controller": "App\\Http\\Controllers\\Admin\\UserController@login", "namespace": null, "prefix": "", "where": [], "as": "admin.login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FControllers%2FAdmin%2FUserController.php&line=28\" onclick=\"\">app/Http/Controllers/Admin/UserController.php:28-31</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0005600000000000001, "accumulated_duration_str": "560μs", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `stores` where `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "getDefaultData", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\GetDefaultData.php", "line": 18}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}], "start": **********.5011458, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "SetDefaultStore.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Middleware/SetDefaultStore.php", "file": "C:\\xampp\\htdocs\\work\\dashboard_tem\\Code\\app\\Http\\Middleware\\SetDefaultStore.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FHttp%2FMiddleware%2FSetDefaultStore.php&line=25", "ajax": false, "filename": "SetDefaultStore.php", "line": "25"}, "connection": "eshop", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Store": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fwork%2Fdashboard_tem%2FCode%2Fapp%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"firebase_settings": "{\"apiKey\":\"\",\"authDomain\":\"\",\"databaseURL\":\"\",\"projectId\":\"\",\"storageBucket\":\"ezeemart-3e0b9.firebasestorage.app\",\"messagingSenderId\":\"\",\"appId\":\"\",\"measurementId\":\"\",\"google_client_id\":\"\",\"google_client_secret\":\"\",\"google_redirect_url\":\"\",\"facebook_client_id\":\"\",\"facebook_client_secret\":\"\",\"facebook_redirect_url\":\"\"}", "_token": "ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "store_id": "20", "store_name": "null", "store_image": "null", "store_slug": "null", "default_store_slug": "null", "show_store_popup": "true"}, "request": {"path_info": "/admin/login", "status_code": "<pre class=sf-dump id=sf-dump-955691872 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-955691872\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-637460817 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-637460817\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1916247337 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1916247337\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-488862538 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://127.0.0.1:8000/admin/settings/system_settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImxWYldPUEh4MUV0TDEvZXFHaFZtU1E9PSIsInZhbHVlIjoiQktXdHl2dUlTL1l5TGg2alRpeWYweVJmTENyREJ4VVR0VEZWcC8vM1UxMXdyT2FQTjVwWVpHRVEyeUJoTVQzMGNobjVzS3drTmtlVk1CaE1RSGpadHA2R0tWRWNwdHZFd1NhTzNvUXZtUlFCUkVFZTFXQW55enU1N3RSbFFNdTVtbDkxR2l4TmQrMzdFd1ZOTEFBcGR3S1FIWE91aFFwL082ckN1cDZMbytPRU92d3Z0MHNpUFZpV1NqL1pIbUxDNXhRWUVKeWduRCtsZTB0dW1wK0IzamVyNDRTQklYcEY0R2ZtRXpkREJMRT0iLCJtYWMiOiI1ZjA1MTZiOGRhYzJhYmMxNTZhNTkxNDJhNjM1ZmMzMGE1MzEzNzUxODY2ZTE1NzQ0NGMwOTExNWI5MjZkNjdhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImpiMkc3YU5XQmZYYmdFVDJFdG9Dc1E9PSIsInZhbHVlIjoiU3RZWmJrNDZHbTcrbklvRlBYUjJKZWxSREc0d1RJY2FKMk9IQ0hZaFpsQkNsYklPK3htWmowaUZ2MjdqVFNtRTFzaUM0dFQveG8rSkpWZllYRUkzQ2hhdkY5ZlpBL0trSk9pZms2bTUxR3dyWWJFYlFtbVBBL3NsakltWHdQZUUiLCJtYWMiOiIxMmM4MTFkOTA1NzFlYzk2MTczNDMyOTI0OGEyMTk0NzAzZTMxNzVhM2MzZWUwYTc1M2VlNzQxNWEzYjRhMWFiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlFtdCt5cU5RSmFDaWM0Z0sxUUlYSXc9PSIsInZhbHVlIjoiMXR3alh3RzRZRUFzL0I1SFI4Qnlrc0o4dU5FRnFhby82cFV6b0RnSFVDUkNaekNjcUVxRUNuQlZJdDVzZ0FCdUxEMUx2UWFmZ0dPblJrQWRaeEpydEJ2ejJhV29XUnFHVklGUFVUbmVhQzI3NU1sdmJHOEhrb3VZaUJRSFFIcTQiLCJtYWMiOiIzOWMzMWYzNmIyM2NjOGJmMDkyN2FkYmFkOGE5NjRkNTU0Zjg3ODg1NDc0ZDY1MWQ3ZWY0MDQ0ZjA5NjQ4YzUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488862538\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1653665042 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_askme_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">m5KnpsFs3z7ZLQidbCVSsw3zOaksGe1drCjl9RxZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653665042\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1479795572 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 18:16:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ikc2bldCeXNkTWphbDB5M2JDN3hwdmc9PSIsInZhbHVlIjoiVkVSbGxsc1Z6ZnNXaCtIbzRjVDJZc3JFQnYwVG84dXIxbElUaTB4QnU2eEp6SzI5aEZ5NTZYUHVCV1dNbDhBOWdyQzk4YzFVSStSWWFyZHhTeDBwaFlNTWZYL3F1NXVKaWljQTNRdFFXTFBPbE9NTFVXKy9JOW53UitMREthRk0iLCJtYWMiOiI4ZWUyZWIzZGQ5YWEwMTcwYWExZDZhZDVjNzIyYjdlMDA0ZWRiN2M3ZjY1NDQyYTIxMzU5YzUzMWE5M2QzN2E5IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:16:53 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkliSW50NkROcTBsRXY3ODBaOGVhQ3c9PSIsInZhbHVlIjoidHBOTlBrc0VvaFU4N0FlR2lZSkZoS1grNlVOUHdDY1B6d1dXa2R0YXRJQ1drejR6dzErS3ZWYjJ3SXVObGZCMkZQd2h0OTM0WHFiSWV2aHNxUlQzNktIeUpCMzhSVTFlSVdzVzF2cFdVMlo4K3Nvbk9RbXErbS9pcDJFZzhDcngiLCJtYWMiOiI5MGMzNjJiZWVmZTUwYzMyMzJjYTQ1YjhlODk4MGIzMmM0MjJkZTk1ZDljNWRmODJjNmJjYzFiZGYxNmE0ZjEzIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 20:16:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ikc2bldCeXNkTWphbDB5M2JDN3hwdmc9PSIsInZhbHVlIjoiVkVSbGxsc1Z6ZnNXaCtIbzRjVDJZc3JFQnYwVG84dXIxbElUaTB4QnU2eEp6SzI5aEZ5NTZYUHVCV1dNbDhBOWdyQzk4YzFVSStSWWFyZHhTeDBwaFlNTWZYL3F1NXVKaWljQTNRdFFXTFBPbE9NTFVXKy9JOW53UitMREthRk0iLCJtYWMiOiI4ZWUyZWIzZGQ5YWEwMTcwYWExZDZhZDVjNzIyYjdlMDA0ZWRiN2M3ZjY1NDQyYTIxMzU5YzUzMWE5M2QzN2E5IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:16:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkliSW50NkROcTBsRXY3ODBaOGVhQ3c9PSIsInZhbHVlIjoidHBOTlBrc0VvaFU4N0FlR2lZSkZoS1grNlVOUHdDY1B6d1dXa2R0YXRJQ1drejR6dzErS3ZWYjJ3SXVObGZCMkZQd2h0OTM0WHFiSWV2aHNxUlQzNktIeUpCMzhSVTFlSVdzVzF2cFdVMlo4K3Nvbk9RbXErbS9pcDJFZzhDcngiLCJtYWMiOiI5MGMzNjJiZWVmZTUwYzMyMzJjYTQ1YjhlODk4MGIzMmM0MjJkZTk1ZDljNWRmODJjNmJjYzFiZGYxNmE0ZjEzIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 20:16:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1479795572\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-341991616 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>firebase_settings</span>\" => \"<span class=sf-dump-str title=\"319 characters\">{&quot;apiKey&quot;:&quot;&quot;,&quot;authDomain&quot;:&quot;&quot;,&quot;databaseURL&quot;:&quot;&quot;,&quot;projectId&quot;:&quot;&quot;,&quot;storageBucket&quot;:&quot;ezeemart-3e0b9.firebasestorage.app&quot;,&quot;messagingSenderId&quot;:&quot;&quot;,&quot;appId&quot;:&quot;&quot;,&quot;measurementId&quot;:&quot;&quot;,&quot;google_client_id&quot;:&quot;&quot;,&quot;google_client_secret&quot;:&quot;&quot;,&quot;google_redirect_url&quot;:&quot;&quot;,&quot;facebook_client_id&quot;:&quot;&quot;,&quot;facebook_client_secret&quot;:&quot;&quot;,&quot;facebook_redirect_url&quot;:&quot;&quot;}</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ct8UHpzHJEPROfJL2soiOJxCMtUNRZ96TtliXJLz</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>store_id</span>\" => <span class=sf-dump-num>20</span>\n  \"<span class=sf-dump-key>store_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>default_store_slug</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>show_store_popup</span>\" => <span class=sf-dump-const>true</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341991616\", {\"maxDepth\":0})</script>\n"}}