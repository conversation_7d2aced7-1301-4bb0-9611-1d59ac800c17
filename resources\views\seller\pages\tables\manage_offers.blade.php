@extends('seller/layout')
@section('title')
{{ __('admin_labels.manage_offers') }}
@endsection
@section('content')
<section class="main-content" {{ session()->get('locale') == 'en' ? 'dir=ltr' : 'dir=rtl' }}>
        <div class="row">
            <div class="d-flex row align-items-center">
                <div class="col-md-12 col-xl-6 page-info-title">
                    <h3>{{ __('admin_labels.manage_offers') }}</h3>
                    <p class="sub_title">
                        {{ __('admin_labels.track_and_manage_offers') }}
                    </p>
                </div>
                <div class="col-md-12 col-xl-6 d-flex justify-content-end">
                    <nav aria-label="breadcrumb" class="float-end" >
                        <ol class="breadcrumb">
                            <i class='bx bx-home-smile'></i>
                            <li class="breadcrumb-item"></li>
                            <li class="breadcrumb-item"><a href="{{ route('seller.home') }}">{{ __('admin_labels.home') }}</a></li>
                            <li class="breadcrumb-item">{{ __('admin_labels.offers') }}</li>
                            <li class="breadcrumb-item active" aria-current="page">
                                {{ __('admin_labels.manage_offers') }}
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>

            <section class="overview-data">
                <div class="card content-area p-4 ">
                    <div class="row align-items-center d-flex heading mb-5" dir='ltr'>
                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-md-12 col-xl-8 d-flex justify-content-end">
                                    <a href="{{ route('seller.offers.index') }}" class="btn btn-dark me-3"><i class='bx bx-plus-circle me-1'></i>
                                        {{ __('admin_labels.add_new') }}</a>

                                    <div class="input-group me-2 search-input-grp">
                                        <span class="search-icon"><i class='bx bx-search-alt'></i></span>
                                        <input type="text" data-table="offer_table" class="form-control searchInput" placeholder='{{ __('admin_labels.search') }}...' {{ session()->get('locale') == 'en' ? 'dir=ltr' : 'dir=rtl' }}>
                                        <span class="input-group-text">{{ __('admin_labels.search') }}</span>
                                    </div>
                                    <a class="btn me-2" id="tableFilter" data-bs-toggle="offcanvas" data-bs-target="#columnFilterOffcanvas" data-table="offer_table" offerTypeFilter='true' brandFilter='true' StatusFilter='true' categoryFilter='true'><i class='bx bx-filter-alt'></i></a>
                                    <a class="btn me-2" id="tableRefresh" data-table="offer_table"><i class='bx bx-refresh'></i></a>
                                    <div class="dropdown">
                                        <a class="btn dropdown-toggle export-btn" type="button" id="exportOptionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class='bx bx-download'></i>
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="exportOptionsDropdown">
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('offer_table','csv')">CSV</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('offer_table','json')">JSON</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('offer_table','sql')">SQL</button></li>
                                            <li><button class="dropdown-item" type="button" onclick="exportTableData('offer_table','excel')">Excel</button></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="pt-0">
                                <div class="table-responsive">

                                    <table id='offer_table' data-toggle="table" data-loading-template="loadingTemplate" data-url="{{ route('seller.products.list') }}" data-click-to-select="true" data-side-pagination="server" data-pagination="true" data-page-list="[5, 10, 20, 50, 100, 200]" data-search="false" data-show-columns="false" data-show-refresh="false" data-trim-on-search="false" data-sort-name="id" data-sort-order="desc" data-mobile-responsive="true" data-toolbar="" data-show-export="false" data-maintain-selected="true" data-export-types='["txt","excel"]' data-query-params="queryParams">
                                        <thead>
                                            <tr>
                                                <th data-field="id" data-sortable="true" data-visible='true'>
                                                    {{ __('admin_labels.id') }}
                                                </th>

                                                <th class="d-flex justify-content-center" data-field="image" data-sortable="false">
                                                    {{ __('admin_labels.image') }}
                                                </th>
                                                <th data-field="name" data-sortable="false" data-disabled="1">
                                                    {{ __('admin_labels.name') }}
                                                </th>
                                                <th data-field="offer_views" data-sortable="false" data-disabled="1">
                                                    {{ __('admin_labels.offer_views') }}
                                                </th>
                                                <th data-field="created_at" data-sortable="false">
                                                    {{ __('admin_labels.created_at') }}
                                                </th>
                                                <th data-field="expire_date" data-sortable="false">
                                                    {{ __('admin_labels.expire_date') }}
                                                </th>
                                                <th data-field="status" data-sortable="false" >
                                                    {{ __('admin_labels.status') }}

                                                </th>
                                                <th data-field="is_approved" data-sortable="false" >
                                                    {{ __('admin_labels.is_approved') }}

                                                </th>
                                                <th data-field="operate" data-sortable="false">
                                                    {{ __('admin_labels.action') }}
                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
</section>
@endsection
