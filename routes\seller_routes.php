<?php

use App\Http\Controllers\Admin\SellerController;
use App\Http\Controllers\Seller\AdvertisementController;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redirect;
use App\Http\Controllers\Seller\HomeController;
use App\Http\Controllers\Seller\CategoryController;
use App\Http\Controllers\Seller\MediaController;
use App\Http\Controllers\Seller\AreaController;
use App\Http\Controllers\Seller\AttributeController;
use App\Http\Controllers\Seller\BranchController;
use App\Http\Controllers\Seller\CodesController;
use App\Http\Controllers\Seller\ProductController;
use App\Http\Controllers\Seller\PosController;
use App\Http\Controllers\Seller\StockController;
use App\Http\Controllers\Seller\OrderController;
use App\Http\Controllers\Seller\PaymentRequestController;
use App\Http\Controllers\Seller\ProductFaqController;
use App\Http\Controllers\Seller\TransactionController;
use App\Http\Controllers\Seller\ComboProductController;
use App\Http\Controllers\Seller\LanguageController;
use App\Http\Controllers\Seller\OfferController;
use App\Http\Controllers\Seller\ReportController;
use App\Http\Controllers\Seller\UserController;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Artisan;

Route::group(
    ['middleware' => ['auth', 'role:seller']],
    function () {

        // account

        Route::get('seller/account/{id}', [UserController::class, 'edit']);

        Route::put('seller/account/update/{id}', [UserController::class, 'update'])->name('seller.account.update');

        // categories

        Route::get(
            '/seller/home',
            [HomeController::class, 'index']
        )->name('seller.home');

        Route::get('seller/categories', [CategoryController::class, 'index'])->name('seller_categories.index');

        Route::get('seller/categories/list', [CategoryController::class, 'list'])->name('seller_categories.list');
        Route::get('seller/categories/edit', [CategoryController::class, 'edit'])->name('seller.category.edit');
        Route::put('seller/categories/update', [CategoryController::class, 'update'])->name('seller.category.update');
        Route::get('seller/categories/get_seller_categories', [CategoryController::class, 'getSellerCategories']);
        Route::get('seller/categories/get_seller_categories_filter', [CategoryController::class, 'get_seller_categories_filter']);

        //store_account_manage
        Route::get('seller/accounts', [UserController::class, 'index'])->name('store_accounts.index');

        Route::get('seller/accounts/list', [UserController::class, 'users_list'])->name('store_accounts.list');
        Route::get('/seller/accounts/create', [UserController::class, 'create_account'])->name('store_accounts.create');
        Route::post('/seller/accounts/store', [UserController::class, 'store_user_account'])->name('store_accounts.store');
        Route::get('/seller/accounts/{id}/edit', [UserController::class, 'edit_user_account'])->name('store_accounts.edit');
        Route::put('/seller/accounts/{id}', [UserController::class, 'update_user_account'])->name('store_accounts.update');
        Route::get('/seller/accounts/{id}', [UserController::class, 'destroy'])->name('store_accounts.destroy');






        // pickup locations

        // Route::resource("seller/pickup_locations", PickupLocationController::class)->names([
        //     'index' => 'pickup_locations.index',
        // ])->except('show');


        // branch

        Route::get("seller/branches", [BranchController::class, 'branches'])->name('seller.branches');
        Route::get('/seller/branches/create', [BranchController::class, 'create'])->name('storebranches.create');
        Route::post('/seller/branches/store', [BranchController::class, 'store'])->name('storebranches.store');
        Route::get('/seller/branches/{id}/edit', [BranchController::class, 'edit'])->name('storebranches.edit');
        Route::put('/seller/branches/{id}', [BranchController::class, 'update'])->name('storebranches.update');
        Route::get('/seller/branches/{id}', [BranchController::class, 'destroy'])->name('storebranches.destroy');


        Route::get(
            'seller/branche/list',
            [BranchController::class, 'branch_list']
        )->name('seller_branches.list');

        Route::resource("seller/offers/codes", CodesController::class)->names([
            'index' => 'codes.index',
        ])->except('show');

        Route::get('seller/codes/list', [codesController::class, 'list'])->name('codes.list');

        //products

        Route::resource("seller/offers", OfferController::class)->names([
            'index' => 'seller.offers.index',
            'edit' => 'seller.offers.edit',
        ])->except('show');
        Route::post('seller/offers', [OfferController::class, 'store'])->name('seller_products.store');

        Route::get('seller/offers/update_status/{id}', [OfferController::class, 'update_status']);

        Route::get('seller/offers/destroy/{id}', [OfferController::class, 'destroy'])->name('seller.offers.destroy');

        Route::get('seller/offers/fetch_attributes_by_id', [OfferController::class, 'fetchAttributesById']);

        Route::put('seller/offers/update/{id}', [OfferController::class, 'update'])->name('seller.products.update');





        Route::get('seller/offers/get_branches', [OfferController::class, 'getBranches'])->name('seller.getBranches');

        Route::get('seller/offers/manage_offers', [OfferController::class, 'manageOffers'])->name('store.offer.manage_offer');

        Route::get('seller/offers/list', [OfferController::class, 'list'])->name('seller.products.list');



        Route::post('seller/offers/delete_image', [OfferController::class, 'deleteImage']);

        Route::get('seller/product/product_bulk_upload', [OfferController::class, 'bulk_upload'])->name('seller.product_bulk_upload');

        Route::post("seller/product/bulk_upload", [OfferController::class, 'process_bulk_upload'])->name('seller.product.bulk_upload');

        Route::get('seller/offers/view_offers/{id}', [OfferController::class, 'show'])->name('seller.offer.show');


        //product faqs

      
        //Transaction
        Route::get('seller/transaction/wallet_transactions', [TransactionController::class, 'wallet_transactions'])->name('seller.transaction.wallet_transactions');
        Route::get('seller/transaction/wallet_transactions_list', [TransactionController::class, 'wallet_transactions_list'])->name('seller.transaction.wallet_transactions_list');

        // ADS Section

        Route::resource("seller/ads", AdvertisementController::class)->names([
            'index' => 'seller.ads.index',
            'edit' => 'seller.ads.edit',
        ])->except('show');
        Route::get('seller/ads/list', [AdvertisementController::class, 'list'])->name('seller.orders.list');
        Route::get('seller/ads/ads_list', [AdvertisementController::class, 'ads_list'])->name('seller.orders.ads_list');
        Route::get('seller/ads/create', [AdvertisementController::class, 'create'])->name('ads.create');
        Route::post('seller/ads/store', [AdvertisementController::class, 'store'])->name('ads.store');
        Route::get('seller/ads/{advertisement}', [AdvertisementController::class, 'show'])->name('ads.show');

        Route::post('seller/set_store', function (Request $request) {

            session(['store_id' => $request->store_id]);
            session(['store_name' => $request->store_name]);
            session(['store_image' => $request->store_image]);
            return response()->json(['success' => true]);
        })->name('set_store');

        // language

        Route::get("seller/settings/language", [LanguageController::class, 'index']);

        Route::put("seller/settings/languages/savelabel", [LanguageController::class, 'savelabel'])->name('savelabel');

        Route::get('seller/settings/languages/change', [LanguageController::class, 'change'])->name('changeLang');

        Route::get("seller/settings/set-language/{locale}", [LanguageController::class, 'setLanguage'])->name('set-language'); // language


        //Reports


   Route::get("seller/seller/get_seller_deliverable_type", [UserController::class, 'get_seller_deliverable_type'])->name('admin.sellers.get_seller_deliverable_type');

        Route::get('seller/zones/seller_zones_data', [UserController::class, 'seller_zones_data']);

        // T-lync routes
        Route::post('/tlync', [PaymentRequestController::class, 'initiate'])->name('initiate_payment');

        // Routes for askme whatsapp pay
        Route::get('/payment/success/{customRef}', [PaymentRequestController::class, 'showPaymentSucceeded'])->name('show_payment_succeeded');
        Route::get('/payment/failed/{customRef}',  [PaymentRequestController::class, 'showPaymentFailed'])->name('show_payment_failed');
    }


);
