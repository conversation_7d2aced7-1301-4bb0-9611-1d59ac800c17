<?php $__env->startSection('title'); ?>
<?php echo e(labels('admin_labels.update_store', 'Update Store')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <h2 class="mb-4"><?php echo e(__('admin_labels.edit_store')); ?></h2>
  <?php if($errors->has('error')): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <?php echo e($errors->first('error')); ?>

    </div>
    <?php endif; ?>
    <form action="<?php echo e(route('admin.stores.update', $store->id)); ?>" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>

        
        <div class="card mb-4">
            <div class="card-header fw-bold"><?php echo e(__('admin_labels.store_user_data')); ?></div>
            <div class="card-body row g-3">
                
                <div class="col-md-4">
                    <label class="form-label"><?php echo e(__('admin_labels.username')); ?></label>
                    <input type="text" name="username" value="<?php echo e(old('username', $store->user->username ?? '')); ?>" class="form-control <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="col-md-4">
                    <label class="form-label"><?php echo e(__('admin_labels.email')); ?></label>
                    <input type="email" name="email" value="<?php echo e(old('email', $store->user->email ?? '')); ?>" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                
                <div class="col-md-4">
                    <label class="form-label"><?php echo e(__('admin_labels.mobile')); ?></label>
                    <input type="text" name="mobile" value="<?php echo e(old('mobile', $store->user->mobile ?? '')); ?>" class="form-control <?php $__errorArgs = ['mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php $__errorArgs = ['mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                
                <div class="col-12">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="togglePasswordFields">
                        <?php echo e(__('admin_labels.change_password')); ?>

                    </button>
                </div>

                
                <div class="col-md-6 password-fields d-none">
                    <label class="form-label"><?php echo e(__('admin_labels.password')); ?></label>
                    <input type="password" name="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="col-md-6 password-fields d-none">
                    <label class="form-label"><?php echo e(__('admin_labels.password_confirmation')); ?></label>
                    <input type="password" name="password_confirmation" class="form-control">
                </div>

            </div>
        </div>

        
        <div class="card mb-4">
            <div class="card-header fw-bold">
                <?php echo e(__('admin_labels.store_info')); ?>

            </div>
            <div class="card-body">
                <div class="row g-3">
                    
                    <div class="col-md-6">
                        <label for="name_ar" class="form-label"><?php echo e(__('admin_labels.name_ar')); ?></label>
                        <input type="text" class="form-control <?php $__errorArgs = ['name_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="name_ar" id="name_ar" value="<?php echo e(old('name_ar', $store->name_ar)); ?>">
                        <?php $__errorArgs = ['name_ar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    
                    <div class="col-md-6">
                        <label for="name_en" class="form-label"><?php echo e(__('admin_labels.name_en')); ?></label>
                        <input type="text" class="form-control <?php $__errorArgs = ['name_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="name_en" id="name_en" value="<?php echo e(old('name_en', $store->name_en)); ?>">
                        <?php $__errorArgs = ['name_en'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    
                    <div class="col-md-6">
                        <label for="description_ar" class="form-label"><?php echo e(__('admin_labels.description_ar')); ?></label>
                        <textarea class="form-control" name="description_ar" id="description_ar" rows="3"><?php echo e(old('description_ar', $store->description_ar)); ?></textarea>
                    </div>

                    
                    <div class="col-md-6">
                        <label for="description_en" class="form-label"><?php echo e(__('admin_labels.description_en')); ?></label>
                        <textarea class="form-control" name="description_en" id="description_en" rows="3"><?php echo e(old('description_en', $store->description_en)); ?></textarea>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="card mb-4">
            <div class="card-header fw-bold">
                <?php echo e(__('admin_labels.store_media')); ?>

            </div>
            <div class="card-body row g-4">
                
                <div class="col-md-6">
                    <label for="logo" class="form-label"><?php echo e(__('admin_labels.logo')); ?></label>
                    <?php if($store->logo): ?>
                    <div class="mb-2">
                        <img src="<?php echo e(getMediaImageUrl($store->logo, 'STORE_IMG_PATH')); ?>" alt="Logo" class="rounded border" width="100">
                    </div>
                    <?php endif; ?>
                    <input type="file" class="form-control" name="logo" id="logo" accept="image/*">
                </div>

                
                <div class="col-md-6">
                    <label for="cover" class="form-label"><?php echo e(__('admin_labels.banner')); ?></label>
                    <?php if($store->cover): ?>
                    <div class="mb-2">
                        <img src="<?php echo e(getMediaImageUrl($store->cover, 'STORE_IMG_PATH')); ?>" alt="Cover" class="rounded border" width="100">
                    </div>
                    <?php endif; ?>
                    <input type="file" class="form-control" name="cover" id="cover" accept="image/*">
                </div>
            </div>
        </div>

        
        <div class="card mb-4">
            <div class="card-header fw-bold">
                <?php echo e(__('admin_labels.colors')); ?>

            </div>
            <div class="card-body row g-3">
                <?php $__currentLoopData = ['primary', 'secondary', 'hover', 'active', 'background']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $color): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-4">
                    <label for="<?php echo e($color); ?>_color" class="form-label"><?php echo e(__('admin_labels.' . $color . '_color')); ?></label>
                    <input type="color" class="form-control form-control-color" name="<?php echo e($color); ?>_color" id="<?php echo e($color); ?>_color" value="<?php echo e(old("{$color}_color", $store->{"{$color}_color"})); ?>">
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        
        <div class="mb-4">
            <label for="is_active" class="form-label fw-bold"><?php echo e(__('admin_labels.status')); ?></label>
            <select class="form-select" name="is_active" id="is_active">
                <option value="1" <?php echo e($store->is_active ? 'selected' : ''); ?>><?php echo e(__('admin_labels.active')); ?></option>
                <option value="0" <?php echo e(!$store->is_active ? 'selected' : ''); ?>><?php echo e(__('admin_labels.deactive')); ?></option>
            </select>
        </div>

        
        <div class="d-flex justify-content-start gap-3">
            <button type="submit" class="btn btn-primary px-4"><?php echo e(__('admin_labels.update')); ?></button>
            <a href="<?php echo e(route('admin.stores.manage_store')); ?>" class="btn btn-outline-secondary"><?php echo e(__('admin_labels.cancel')); ?></a>
        </div>
    </form>
</div>
<script>
    document.getElementById('togglePasswordFields').addEventListener('click', function() {
        document.querySelectorAll('.password-fields').forEach(function(field) {
            field.classList.toggle('d-none');
        });
    });
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/pages/forms/update_store.blade.php ENDPATH**/ ?>