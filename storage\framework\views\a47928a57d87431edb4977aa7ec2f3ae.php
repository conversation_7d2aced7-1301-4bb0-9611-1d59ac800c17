<?php $__env->startSection('title'); ?>
<?php echo e(labels('admin_labels.home', 'Home')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<div class="d-flex row align-items-center">
    <div class="col-md-6 col-xl-6 page-info-title">
        <h3><?php echo e(labels('admin_labels.dashboard', 'Dashboard')); ?>

        </h3>
        <p class="sub_title">
            <?php echo e(labels('admin_labels.drive_success_for_you_and_your_sellers', 'Drive Success For You and Your Sellers')); ?>

        </p>
    </div>
</div>
<!--================================================Overview=========================================== -->
<div class="col-md-12">
    <div class="row  g-4">

        <?php
        $stats = [
        ['label' => __('admin_labels.total_users'), 'value' => $totalUsers, 'icon' => 'bx-user'],
        ['label' => __('admin_labels.banned_users'), 'value' => $bannedUsers, 'icon' => 'bx-block'],
        ['label' => __('admin_labels.active_stores'), 'value' => $activeStores, 'icon' => 'bx-store'],
        ['label' => __('admin_labels.active_offers'), 'value' => $activeOffers, 'icon' => 'bx-tag'],
        ['label' => __('admin_labels.claimed_offers'), 'value' => $claimedOffers, 'icon' => 'bx-gift'],
        ['label' => __('admin_labels.ads_running'), 'value' => $adsRunning, 'icon' => 'bx-bullseye'],
        ['label' => __('admin_labels.new_users_today'), 'value' => $newUsersToday, 'icon' => 'bx-user-plus'],
        ['label' => __('admin_labels.active_users_today'), 'value' => $activeUsersToday, 'icon' => 'bx-user-check'],
        ['label' => __('admin_labels.pending_stores'), 'value' => $pandingStore, 'icon' => 'bx-store'],
        ['label' => __('admin_labels.pending_offers'), 'value' => $pendingOffers, 'icon' => 'bx-tag'],
        ['label' => __('admin_labels.pending_ads'), 'value' => $pendingAs, 'icon' => 'bx-bullseye'],
        ['label' => __('admin_labels.redeemed_offers'), 'value' => $redeemedOffers, 'icon' => 'bx-gift'],
        ];

        ?>

        <?php $__currentLoopData = $stats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="card text-center shadow-sm">
                <div class="card-body">
                    <div class="avatar bg-light rounded-circle p-3 mb-2 mx-auto">
                        <i class="bx <?php echo e($stat['icon']); ?> fs-3" style="color: #FF3344;"></i>
                    </div>
                    <h5 class="card-title"><?php echo e($stat['value']); ?></h5>
                    <p class="card-text"><?php echo e($stat['label']); ?></p>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <div class="col-md-12 col-xxl-8 mt-md-2 mt-xxl-0">
            <!-- ============================================ User Growth  ======================================== -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('admin_labels.new_users_growth')); ?></h5>
                </div>
                <div class="card-body">
                    <canvas id="newUsersChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <!-- ============================================ Recent Activities  ======================================== -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0"><?php echo e(__('admin_labels.recent_activity')); ?></h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <?php $__empty_1 = true; $__currentLoopData = $recentActivities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bx bx-<?php echo e($activity['type'] === 'user' ? 'user' : ($activity['type'] === 'offer' ? 'gift' : 'check-double')); ?> me-2 text-primary"></i>
                            <a href="<?php echo e($activity['url']); ?>" class="text-decoration-none">
                                <?php echo e($activity['message']); ?>

                            </a>
                        </div>
                        <small class="text-muted"><?php echo e($activity['time']->diffForHumans()); ?></small>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <li class="list-group-item text-muted"><?php echo e(__('admin_labels.no_recent_activity')); ?></li>
                    <?php endif; ?>
                </ul>

            </div>
        </div>

        <!-- ============================================ Top Stores, Offers, and Categories  ======================================== -->
        <!-- Top Stats Dashboard -->
        <div class="row g-4">
            <!-- Top Stores Card -->
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom-0 py-3">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="bx bx-store text-primary me-2"></i>
                            <?php echo e(__('admin_labels.top_stores')); ?>

                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <?php $__currentLoopData = $topStores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item border-0 d-flex align-items-center py-3 px-4">
                                <div class="flex-shrink-0 me-3">
                                    <?php if($store->logo): ?>
                                    <img src="<?php echo e(getMediaImageUrl($store->logo)); ?>" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                                    <?php else: ?>
                                    <div class="avatar avatar-sm bg-light-primary rounded-circle">
                                        <i class="bx bx-store"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-medium"><?php echo e($store->name_ar); ?></div>
                                    <small class="text-muted"><?php echo e($store->name_en ?? 'N/A'); ?></small>
                                </div>
                                <span class="badge bg-success bg-opacity-10 text-success">
                                    <?php echo e($store->store_views); ?> <i class="bx bx-show ms-1"></i>
                                </span>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Offers Card -->
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom-0 py-3">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="bx bx-gift text-success me-2"></i>
                            <?php echo e(__('admin_labels.top_offers')); ?>

                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <?php $__currentLoopData = $topOffers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $offer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item border-0 d-flex align-items-center py-3 px-4">
                                <div class="flex-shrink-0 me-3">
                                    <?php if($offer->images->first()): ?>
                                    <img src="<?php echo e(getMediaImageUrl($offer->images->first()->image_url)); ?>" class="rounded" width="40" height="40" style="object-fit: cover;">
                                    <?php else: ?>
                                    <div class="avatar avatar-sm bg-light-success rounded-circle">
                                        <i class="bx bx-gift"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-medium text-truncate"><?php echo e($offer->title_ar); ?></div>
                                    <small class="text-muted"><?php echo e($offer->store->name_ar ?? 'N/A'); ?></small>
                                </div>
                                <span class="badge bg-success bg-opacity-10 text-success">
                                    <?php echo e($offer->offer_views); ?> <i class="bx bx-show ms-1"></i>
                                </span>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Categories Card -->
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom-0 py-3">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="bx bx-category text-warning me-2"></i>
                            <?php echo e(__('admin_labels.top_categories')); ?>

                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <?php $__currentLoopData = $topCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item border-0 d-flex align-items-center py-3 px-4">
                                <div class="flex-shrink-0 me-3">
                                    <?php if($category->image): ?>
                                    <img src="<?php echo e(getMediaImageUrl($category->image)); ?>" class="rounded" width="40" height="40" style="object-fit: cover;">
                                    <?php else: ?>
                                    <div class="avatar avatar-sm bg-light-warning rounded-circle">
                                        <i class="bx bx-category"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-medium"><?php echo e($category->name_ar); ?></div>
                                </div>
                                <span class="badge bg-warning bg-opacity-10 text-warning">
                                    <?php echo e($category->claims_count); ?> <i class="bx bx-check-circle ms-1"></i>
                                </span>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- -------------------------------------------------------Top Active Users----------------------- -->
        <div class="row g-4">

            <div class="card shadow-sm mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('admin_labels.top_active_users')); ?></h5>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        <?php $__currentLoopData = $topUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div>
                                    <h6 class="mb-0"><?php echo e($user->username); ?></h6>
                                    <small><?php echo e($user->email); ?></small>
                                </div>
                            </div>
                            <div>
                                <span class="badge bg-primary">Claims: <?php echo e($user->claims_count); ?></span>
                                <a href="<?php echo e(route('admin.customers.show', $user->id)); ?>" class="btn btn-sm btn-link"><?php echo e(__('admin_labels.view_user')); ?></a>
                            </div>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
        </div>

        <!-- -------------------------------------------Ads Statistics----------------------------- -->
        <div class="row">
        <?php
        $stats = [
        ['label' => __('admin_labels.active_ads'), 'value' => $activeAds, 'icon' => 'bx-bullseye'],
        ['label' => __('admin_labels.pending_ads'), 'value' => $pendingAds, 'icon' => 'bx-time'],
        ['label' => __('admin_labels.with_for_pay'), 'value' => $withForPay, 'icon' => 'bx-wallet'],
        ['label' => __('admin_labels.expired_ads'), 'value' => $expiredAds, 'icon' => 'bx-x'],
        ];

        ?>

        <?php $__currentLoopData = $stats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="card text-center shadow-sm">
                <div class="card-body">
                    <div class="avatar bg-light rounded-circle p-3 mb-2 mx-auto">
                        <i class="bx <?php echo e($stat['icon']); ?> fs-3" style="color: #FF3344;"></i>
                    </div>
                    <h5 class="card-title"><?php echo e($stat['value']); ?></h5>
                    <p class="card-text"><?php echo e($stat['label']); ?></p>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        </div>
<!-- -----------------------------Ads Pie Chart----------------------------- -->
<div class="row align-items-stretch">
    <!-- Pie Chart: Ad Distribution -->
    <div class="col-md-4 col-sm-12 mb-4 d-flex">
        <div class="card shadow-sm text-center flex-fill">
            <div class="card-header">
                <strong><?php echo e(__('admin_labels.ad_distribution')); ?></strong>
            </div>
            <div class="card-body d-flex justify-content-center align-items-center" style="min-height: 250px;">
                <canvas id="adTypeChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Bar Chart: Ads per Month -->
    <div class="col-md-8 col-sm-12 mb-4 d-flex">
        <div class="card shadow-sm flex-fill">
            <div class="card-header">
                <h5 class="mb-0"><?php echo e(__('admin_labels.ads_per_month')); ?></h5>
            </div>
            <div class="card-body" style="min-height: 250px;">
                <canvas id="adsBarChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>


<div class="col-12 mb-4">
    <div class="card shadow-sm h-100">
        <div class="card-header">
            <h5 class="mb-0"><?php echo e(__('admin_labels.latest_reports')); ?></h5>
        </div>
        <div class="card-body p-0" id="latestReportsList">
            <div class="text-center text-muted p-3">
                <?php echo e(__('admin_labels.loading')); ?>

            </div>
        </div>
    </div>
</div>


     
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // ===== New Users Chart =====
    new Chart(document.getElementById('newUsersChart').getContext('2d'), {
        type: 'line',
        data: {
            labels: <?php echo json_encode($userGrowth['labels'], 15, 512) ?>,
            datasets: [{
                label: '<?php echo e(__("admin_labels.new_users")); ?>',
                data: <?php echo json_encode($userGrowth['data'], 15, 512) ?>,
                fill: true,
                tension: 0.4,
                backgroundColor: 'rgba(255, 51, 68, 0.1)',
                borderColor: '#FF3344',
                pointBackgroundColor: '#FF3344',
                pointBorderColor: '#FF3344',
                borderWidth: 2,
            }]
        },
        options: {
            responsive: true,
            plugins: {
                tooltip: { mode: 'index', intersect: false },
                legend: { display: false }
            },
            scales: {
                x: { grid: { display: false } },
                y: { beginAtZero: true, ticks: { stepSize: 1 } }
            }
        }
    });

    // ===== Ad Distribution Pie Chart =====
    new Chart(document.getElementById('adTypeChart').getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: ['Banner', 'Premuim', 'Spotlight'],
            datasets: [{
                label: 'Ad Type Distribution',
                data: [<?php echo e($bannerCount); ?>, <?php echo e($premuimCount); ?>, <?php echo e($popupCount); ?>],
                backgroundColor: ['#FF3344', '#36A2EB', '#FFCE56'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { position: 'bottom' },
                tooltip: { enabled: true }
            }
        }
    });

    // ===== Ads Per Month Bar Chart =====
    new Chart(document.getElementById('adsBarChart').getContext('2d'), {
        type: 'bar',
        data: {
            labels: <?php echo json_encode($months, 15, 512) ?>,
            datasets: [{
                label: '<?php echo e(__("admin_labels.advertisments")); ?>',
                data: <?php echo json_encode($counts, 15, 512) ?>,
                backgroundColor: '#FF3344',
                borderRadius: 6,
                barThickness: 14,
                maxBarThickness: 14,
            }]
        },
        options: {
            responsive: true,
            plugins: { legend: { display: false } },
            scales: { y: { beginAtZero: true } }
        }
    });

    // ===== Latest Reports Loader =====
    fetch("<?php echo e(route('admin.dashboard.latest-reports')); ?>")
        .then(res => res.json())
        .then(data => {
            const container = document.getElementById('latestReportsList');
            container.innerHTML = '';

            if (!data.reports.length) {
                container.innerHTML = `<div class="text-center text-muted p-3"><?php echo e(__("admin_labels.no_reports")); ?></div>`;
                return;
            }

            data.reports.forEach(report => {
                const link = `/admin/reported_offers/${report.id}`;
                container.innerHTML += `
                    <div class="border-bottom p-3 d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1 pe-3">
                            <div class="fw-bold mb-1">${report.offer_name} #${report.related_id}</div>
                            <div class="text-muted small">${report.description}</div>
                            <div class="small text-secondary mt-1"><?php echo e(__('admin_labels.by')); ?>: ${report.user_name} · ${report.created_at}</div>
                        </div>
                        <div class="text-end">
                            <a href="${link}" class="btn btn-sm btn-outline-primary"><?php echo e(__('admin_labels.view')); ?></a>
                        </div>
                    </div>`;
            });
        })
        .catch(() => {
            document.getElementById('latestReportsList').innerHTML =
                '<div class="text-danger text-center p-3"><?php echo e(__("admin_labels.failed_to_load")); ?></div>';
        });
});
</script>





        <?php $__env->startPush('styles'); ?>
        <style>
            .avatar {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .avatar-sm {
                width: 40px;
                height: 40px;
            }

            .bg-light-primary {
                background-color: rgba(13, 110, 253, 0.1);
                color: #0d6efd;
            }

            .bg-light-success {
                background-color: rgba(25, 135, 84, 0.1);
                color: #198754;
            }

            .bg-light-warning {
                background-color: rgba(255, 193, 7, 0.1);
                color: #ffc107;
            }

            .list-group-item {
                transition: background-color 0.2s ease;
            }

            .list-group-item:hover {
                background-color: rgba(0, 0, 0, 0.03);
            }

            .text-truncate {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 150px;
            }
        </style>
        <?php $__env->stopPush(); ?>

        <?php $__env->stopSection(); ?>
<?php echo $__env->make('admin/layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\work\dashboard_tem\Code\resources\views/admin/pages/forms/home.blade.php ENDPATH**/ ?>